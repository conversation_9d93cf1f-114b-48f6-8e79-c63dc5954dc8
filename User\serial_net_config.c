#include "serial_net_config.h"
#include <string.h>
#include <stdlib.h>
#include <ctype.h>
#include <stdio.h>
#include "stm32f4xx_flash.h"
#include "wizchip_conf.h"

/* 参数验证模块实现 */

/**
 * @brief 验证IP地址字符串格式
 * @param ip_str IP地址字符串 (例如: "*************")
 * @return NetConfigError_t 验证结果
 */
NetConfigError_t validate_ip_format(const char* ip_str)
{
    if (ip_str == NULL) {
        return NET_CONFIG_ERR_INVALID_IP;
    }
    
    // 检查字符串长度 (最小7字符: "0.0.0.0", 最大15字符: "***************")
    size_t len = strlen(ip_str);
    if (len < 7 || len > 15) {
        return NET_CONFIG_ERR_INVALID_IP;
    }
    
    // 创建工作副本以避免修改原字符串
    char ip_copy[16];
    strncpy(ip_copy, ip_str, sizeof(ip_copy) - 1);
    ip_copy[sizeof(ip_copy) - 1] = '\0';
    
    // 检查是否以点号开头或结尾
    if (ip_copy[0] == '.' || ip_copy[len-1] == '.') {
        return NET_CONFIG_ERR_INVALID_IP;
    }
    
    // 检查是否有连续的点号
    for (size_t i = 0; i < len - 1; i++) {
        if (ip_copy[i] == '.' && ip_copy[i+1] == '.') {
            return NET_CONFIG_ERR_INVALID_IP;
        }
    }
    
    // 分割IP地址的四个部分
    char* token;
    char* saveptr;
    int octet_count = 0;
    int octets[4];
    
    //token = strtok(ip_copy, ".", &saveptr);
		token = strtok(ip_copy, ".");
    while (token != NULL && octet_count < 4) {
        // 检查每个八位组是否为纯数字
        for (int i = 0; token[i] != '\0'; i++) {
            if (!isdigit(token[i])) {
                return NET_CONFIG_ERR_INVALID_IP;
            }
        }
        
        // 检查是否有前导零 (除了单独的"0")
        if (strlen(token) > 1 && token[0] == '0') {
            return NET_CONFIG_ERR_INVALID_IP;
        }
        
        // 转换为整数并检查范围
        int octet = atoi(token);
        if (octet < 0 || octet > 255) {
            return NET_CONFIG_ERR_INVALID_IP;
        }
        
        octets[octet_count] = octet;
        octet_count++;
        token = strtok(NULL, ".");
    }
    
    // 必须恰好有4个八位组
    if (octet_count != 4) {
        return NET_CONFIG_ERR_INVALID_IP;
    }
    
    // 检查是否还有多余的字符
    if (token != NULL) {
        return NET_CONFIG_ERR_INVALID_IP;
    }
    
    return NET_CONFIG_OK;
}

/**
 * @brief 将IP地址字符串转换为字节数组
 * @param ip_str IP地址字符串
 * @param ip_bytes 输出的IP字节数组 (4字节)
 * @return NetConfigError_t 转换结果
 */
NetConfigError_t parse_ip_string(const char* ip_str, uint8_t ip_bytes[4])
{
    NetConfigError_t result = validate_ip_format(ip_str);
    if (result != NET_CONFIG_OK) {
        return result;
    }
    
    if (ip_bytes == NULL) {
        return NET_CONFIG_ERR_INVALID_PARAM;
    }
    
    // 创建工作副本
    char ip_copy[16];
    strncpy(ip_copy, ip_str, sizeof(ip_copy) - 1);
    ip_copy[sizeof(ip_copy) - 1] = '\0';
    
    // 解析四个八位组
    char* token;
    char* saveptr;
    int octet_count = 0;
    
    token = strtok(ip_copy, ".");
    while (token != NULL && octet_count < 4) {
        ip_bytes[octet_count] = (uint8_t)atoi(token);
        octet_count++;
        token = strtok(NULL, ".");
    }
    
    return NET_CONFIG_OK;
}

/**
 * @brief 检查IP地址是否为特殊地址
 * @param ip_bytes IP地址字节数组
 * @return int 1=特殊地址, 0=普通地址
 */
int is_special_ip_address(const uint8_t ip_bytes[4])
{
    if (ip_bytes == NULL) {
        return 1; // 空指针视为特殊地址
    }
    
    uint32_t ip = (ip_bytes[0] << 24) | (ip_bytes[1] << 16) | 
                  (ip_bytes[2] << 8) | ip_bytes[3];
    
    // 检查各种特殊地址范围
    
    // 0.0.0.0/8 - 本网络
    if ((ip & 0xFF000000) == 0x00000000) {
        return 1;
    }
    
    // *********/8 - 回环地址
    if ((ip & 0xFF000000) == 0x7F000000) {
        return 1;
    }
    
    // *********/4 - 多播地址 (224-239)
    if ((ip & 0xF0000000) == 0xE0000000) {
        return 1;
    }
    
    // 240.0.0.0/4 - 保留地址 (240-255)
    if ((ip & 0xF0000000) == 0xF0000000) {
        return 1;
    }
    
    // *************** - 广播地址
    if (ip == 0xFFFFFFFF) {
        return 1;
    }
    
    return 0; // 普通地址
}

/**
 * @brief 检查子网掩码是否有效
 * @param mask_bytes 子网掩码字节数组
 * @return NetConfigError_t 验证结果
 */
NetConfigError_t validate_subnet_mask(const uint8_t mask_bytes[4])
{
    if (mask_bytes == NULL) {
        return NET_CONFIG_ERR_INVALID_MASK;
    }
    
    uint32_t mask = (mask_bytes[0] << 24) | (mask_bytes[1] << 16) | 
                    (mask_bytes[2] << 8) | mask_bytes[3];
    
    // 子网掩码不能为0
    if (mask == 0) {
        return NET_CONFIG_ERR_INVALID_MASK;
    }
    
    // 子网掩码不能为全1 (***************)
    if (mask == 0xFFFFFFFF) {
        return NET_CONFIG_ERR_INVALID_MASK;
    }
    
    // 检查子网掩码是否为连续的1后跟连续的0
    // 将掩码取反，然后加1，结果应该是2的幂
    uint32_t inverted = ~mask;
    uint32_t test = inverted + 1;
    
    // 检查是否为2的幂 (只有一个bit为1)
    if ((test & (test - 1)) != 0) {
        return NET_CONFIG_ERR_INVALID_MASK;
    }
    
    return NET_CONFIG_OK;
}/**
 
* @brief 验证网络参数的完整性和逻辑关系
 * @param config 网络配置结构体指针
 * @return NetConfigError_t 验证结果
 */
NetConfigError_t validate_network_params(const NetConfig_t* config)
{
    if (config == NULL) {
        return NET_CONFIG_ERR_INVALID_PARAM;
    }
    
    // 验证IP地址
    if (is_special_ip_address(config->ip)) {
        return NET_CONFIG_ERR_INVALID_IP;
    }
    
    // 验证子网掩码
    NetConfigError_t mask_result = validate_subnet_mask(config->subnet);
    if (mask_result != NET_CONFIG_OK) {
        return mask_result;
    }
    
    // 验证网关地址
    if (is_special_ip_address(config->gateway)) {
        return NET_CONFIG_ERR_INVALID_GW;
    }
    
    // 验证DNS服务器地址
    if (is_special_ip_address(config->dns)) {
        return NET_CONFIG_ERR_INVALID_DNS;
    }
    
    // 检查IP地址和网关是否在同一子网
    uint32_t ip = (config->ip[0] << 24) | (config->ip[1] << 16) | 
                  (config->ip[2] << 8) | config->ip[3];
    uint32_t gateway = (config->gateway[0] << 24) | (config->gateway[1] << 16) | 
                       (config->gateway[2] << 8) | config->gateway[3];
    uint32_t mask = (config->subnet[0] << 24) | (config->subnet[1] << 16) | 
                    (config->subnet[2] << 8) | config->subnet[3];
    
    uint32_t ip_network = ip & mask;
    uint32_t gw_network = gateway & mask;
    
    if (ip_network != gw_network) {
        return NET_CONFIG_ERR_INVALID_GW; // 网关不在同一子网
    }
    
    // 检查IP地址是否为网络地址或广播地址
    uint32_t broadcast = ip_network | (~mask);
    if (ip == ip_network || ip == broadcast) {
        return NET_CONFIG_ERR_INVALID_IP; // IP不能是网络地址或广播地址
    }
    
    // 检查网关是否为网络地址或广播地址
    if (gateway == ip_network || gateway == broadcast) {
        return NET_CONFIG_ERR_INVALID_GW; // 网关不能是网络地址或广播地址
    }
    
    // 检查IP地址和网关是否相同
    if (ip == gateway) {
        return NET_CONFIG_ERR_INVALID_GW; // IP地址和网关不能相同
    }
    
    return NET_CONFIG_OK;
}

/**
 * @brief 检查两个网络配置是否冲突
 * @param config1 第一个网络配置
 * @param config2 第二个网络配置
 * @return int 1=冲突, 0=无冲突
 */
int check_network_conflict(const NetConfig_t* config1, const NetConfig_t* config2)
{
    if (config1 == NULL || config2 == NULL) {
        return 0;
    }
    
    // 检查IP地址是否相同
    if (memcmp(config1->ip, config2->ip, 4) == 0) {
        return 1;
    }
    
    // 检查MAC地址是否相同
    if (memcmp(config1->mac, config2->mac, 6) == 0) {
        return 1;
    }
    
    return 0;
}

/**
 * @brief 验证MAC地址格式
 * @param mac_bytes MAC地址字节数组 (6字节)
 * @return NetConfigError_t 验证结果
 */
NetConfigError_t validate_mac_address(const uint8_t mac_bytes[6])
{
    if (mac_bytes == NULL) {
        return NET_CONFIG_ERR_INVALID_PARAM;
    }
    
    // 检查是否为全零MAC地址
    int all_zero = 1;
    for (int i = 0; i < 6; i++) {
        if (mac_bytes[i] != 0) {
            all_zero = 0;
            break;
        }
    }
    if (all_zero) {
        return NET_CONFIG_ERR_INVALID_PARAM;
    }
    
    // 检查是否为广播MAC地址
    int all_ff = 1;
    for (int i = 0; i < 6; i++) {
        if (mac_bytes[i] != 0xFF) {
            all_ff = 0;
            break;
        }
    }
    if (all_ff) {
        return NET_CONFIG_ERR_INVALID_PARAM;
    }
    
    // 检查是否为多播MAC地址 (第一个字节的最低位为1)
    if (mac_bytes[0] & 0x01) {
        return NET_CONFIG_ERR_INVALID_PARAM;
    }
    
    return NET_CONFIG_OK;
}

/* 串口命令解析模块实现 */

/**
 * @brief 去除字符串首尾空白字符
 * @param str 要处理的字符串
 */
void trim_whitespace(char* str)
{
    if (str == NULL) {
        return;
    }
    
    // 去除尾部空白字符
    int len = strlen(str);
    while (len > 0 && (str[len-1] == ' ' || str[len-1] == '\t' || 
                       str[len-1] == '\r' || str[len-1] == '\n')) {
        str[--len] = '\0';
    }
    
    // 去除头部空白字符
    int start = 0;
    while (str[start] && (str[start] == ' ' || str[start] == '\t' || 
                          str[start] == '\r' || str[start] == '\n')) {
        start++;
    }
    
    if (start > 0) {
        memmove(str, str + start, len - start + 1);
    }
}

/**
 * @brief 识别命令类型
 * @param cmd_str 命令字符串
 * @return CommandType_t 命令类型
 */
CommandType_t identify_command(const char* cmd_str)
{
    if (cmd_str == NULL) {
        return CMD_UNKNOWN;
    }
    
    // 转换为大写进行比较
    char upper_cmd[32];
    int i = 0;
    while (cmd_str[i] && i < sizeof(upper_cmd) - 1) {
        upper_cmd[i] = toupper(cmd_str[i]);
        i++;
    }
    upper_cmd[i] = '\0';
    
    // 命令识别
    if (strcmp(upper_cmd, "NET_CONFIG") == 0 || strcmp(upper_cmd, "NETCONFIG") == 0) {
        return CMD_NET_CONFIG;
    }
    else if (strcmp(upper_cmd, "SET_IP") == 0 || strcmp(upper_cmd, "SETIP") == 0) {
        return CMD_SET_IP;
    }
    else if (strcmp(upper_cmd, "SET_MASK") == 0 || strcmp(upper_cmd, "SETMASK") == 0) {
        return CMD_SET_MASK;
    }
    else if (strcmp(upper_cmd, "SET_GW") == 0 || strcmp(upper_cmd, "SETGW") == 0) {
        return CMD_SET_GW;
    }
    else if (strcmp(upper_cmd, "SET_DNS") == 0 || strcmp(upper_cmd, "SETDNS") == 0) {
        return CMD_SET_DNS;
    }
    else if (strcmp(upper_cmd, "APPLY_NET") == 0 || strcmp(upper_cmd, "APPLYNET") == 0) {
        return CMD_APPLY_NET;
    }
    else if (strcmp(upper_cmd, "RESET_NET") == 0 || strcmp(upper_cmd, "RESETNET") == 0) {
        return CMD_RESET_NET;
    }
    else if (strcmp(upper_cmd, "AUTH") == 0 || strcmp(upper_cmd, "LOGIN") == 0) {
        return CMD_AUTH;
    }
    else if (strcmp(upper_cmd, "LOGOUT") == 0 || strcmp(upper_cmd, "EXIT") == 0) {
        return CMD_LOGOUT;
    }
    else if (strcmp(upper_cmd, "HELP") == 0 || strcmp(upper_cmd, "?") == 0) {
        return CMD_HELP;
    }
    
    return CMD_UNKNOWN;
}

/**
 * @brief 分割参数字符串
 * @param param_str 参数字符串
 * @param params 输出参数数组
 * @param max_params 最大参数个数
 * @return int 实际参数个数
 */
int split_parameters(const char* param_str, char params[][32], int max_params)
{
    if (param_str == NULL || params == NULL || max_params <= 0) {
        return 0;
    }
    
    // 创建工作副本
    char work_str[SERIAL_PARAM_MAX_SIZE];
    strncpy(work_str, param_str, sizeof(work_str) - 1);
    work_str[sizeof(work_str) - 1] = '\0';
    
    int param_count = 0;
    char* token;
    char* saveptr;
    
    // 使用空格和制表符作为分隔符
    token = strtok(work_str, " \t");
    while (token != NULL && param_count < max_params) {
        // 去除参数的首尾空白
        trim_whitespace(token);
        
        // 只保存非空参数
        if (strlen(token) > 0) {
            strncpy(params[param_count], token, 31);
            params[param_count][31] = '\0';
            param_count++;
        }
        
        token = strtok(NULL, " \t");
    }
    
    return param_count;
}

/**
 * @brief 解析串口命令
 * @param input 输入的命令字符串
 * @param cmd 输出的命令结构体
 * @return CommandType_t 解析后的命令类型
 */
CommandType_t parse_serial_command(const char* input, SerialCommand_t* cmd)
{
    if (input == NULL || cmd == NULL) {
        return CMD_UNKNOWN;
    }
    
    // 初始化命令结构体
    memset(cmd, 0, sizeof(SerialCommand_t));
    cmd->cmd_type = CMD_UNKNOWN;
    
    // 创建输入字符串的工作副本
    char work_input[SERIAL_CMD_BUFFER_SIZE];
    strncpy(work_input, input, sizeof(work_input) - 1);
    work_input[sizeof(work_input) - 1] = '\0';
    
    // 去除首尾空白字符
    trim_whitespace(work_input);
    
    // 检查是否为空命令
    if (strlen(work_input) == 0) {
        return CMD_UNKNOWN;
    }
    
    // 查找第一个空格，分离命令和参数
    char* space_pos = strchr(work_input, ' ');
    char command_part[32] = {0};
    char param_part[SERIAL_PARAM_MAX_SIZE] = {0};
    
    if (space_pos != NULL) {
        // 有参数的情况
        size_t cmd_len = space_pos - work_input;
        if (cmd_len >= sizeof(command_part)) {
            cmd_len = sizeof(command_part) - 1;
        }
        
        strncpy(command_part, work_input, cmd_len);
        command_part[cmd_len] = '\0';
        
        // 复制参数部分
        strncpy(param_part, space_pos + 1, sizeof(param_part) - 1);
        param_part[sizeof(param_part) - 1] = '\0';
        trim_whitespace(param_part);
    } else {
        // 无参数的情况
        strncpy(command_part, work_input, sizeof(command_part) - 1);
        command_part[sizeof(command_part) - 1] = '\0';
    }
    
    // 识别命令类型
    cmd->cmd_type = identify_command(command_part);
    
    // 保存原始参数字符串
    strncpy(cmd->param, param_part, sizeof(cmd->param) - 1);
    cmd->param[sizeof(cmd->param) - 1] = '\0';
    
    // 分割参数
    cmd->param_count = split_parameters(param_part, cmd->params, 4);
    
    return cmd->cmd_type;
}

/* 命令响应格式化模块实现 */

/**
 * @brief 获取错误代码对应的错误消息
 * @param error_code 错误代码
 * @return const char* 错误消息字符串
 */
const char* get_error_message(NetConfigError_t error_code)
{
    switch (error_code) {
        case NET_CONFIG_OK:
            return "Success";
        case NET_CONFIG_ERR_INVALID_IP:
            return "Invalid IP address format";
        case NET_CONFIG_ERR_INVALID_MASK:
            return "Invalid subnet mask";
        case NET_CONFIG_ERR_INVALID_GW:
            return "Invalid gateway address";
        case NET_CONFIG_ERR_INVALID_DNS:
            return "Invalid DNS server address";
        case NET_CONFIG_ERR_INVALID_CMD:
            return "Unknown command";
        case NET_CONFIG_ERR_INVALID_PARAM:
            return "Invalid parameter";
        case NET_CONFIG_ERR_AUTH_FAIL:
            return "Authentication failed";
        case NET_CONFIG_ERR_AUTH_REQUIRED:
            return "Authentication required";
        case NET_CONFIG_ERR_AUTH_LOCKED:
            return "Access locked due to too many failed attempts";
        case NET_CONFIG_ERR_SESSION_TIMEOUT:
            return "Session timeout";
        case NET_CONFIG_ERR_STORAGE_READ:
            return "Storage read error";
        case NET_CONFIG_ERR_STORAGE_WRITE:
            return "Storage write error";
        case NET_CONFIG_ERR_STORAGE_ERASE:
            return "Storage erase error";
        case NET_CONFIG_ERR_STORAGE_CRC:
            return "Storage CRC check failed";
        case NET_CONFIG_ERR_NETWORK_APPLY:
            return "Failed to apply network configuration";
        case NET_CONFIG_ERR_NETWORK_TEST:
            return "Network connectivity test failed";
        case NET_CONFIG_ERR_TIMEOUT:
            return "Operation timeout";
        case NET_CONFIG_ERR_BUSY:
            return "System busy";
        case NET_CONFIG_ERR_NOT_SUPPORTED:
            return "Operation not supported";
        default:
            return "Unknown error";
    }
}

/**
 * @brief 格式化成功响应
 * @param response_buffer 响应缓冲区
 * @param buffer_size 缓冲区大小
 * @param message 成功消息
 * @return int 格式化后的字符串长度，-1表示错误
 */
int format_success_response(char* response_buffer, size_t buffer_size, const char* message)
{
    if (response_buffer == NULL || buffer_size == 0) {
        return -1;
    }
    
    const char* msg = (message != NULL) ? message : "OK";
    int len = snprintf(response_buffer, buffer_size, "OK:%s\r\n", msg);
    
    if (len >= (int)buffer_size) {
        return -1; // 缓冲区溢出
    }
    
    return len;
}

/**
 * @brief 格式化错误响应
 * @param response_buffer 响应缓冲区
 * @param buffer_size 缓冲区大小
 * @param error_code 错误代码
 * @param details 详细错误信息 (可选)
 * @return int 格式化后的字符串长度，-1表示错误
 */
int format_error_response(char* response_buffer, size_t buffer_size, NetConfigError_t error_code, const char* details)
{
    if (response_buffer == NULL || buffer_size == 0) {
        return -1;
    }
    
    const char* error_msg = get_error_message(error_code);
    int len;
    
    if (details != NULL && strlen(details) > 0) {
        len = snprintf(response_buffer, buffer_size, "ERROR:%s - %s\r\n", error_msg, details);
    } else {
        len = snprintf(response_buffer, buffer_size, "ERROR:%s\r\n", error_msg);
    }
    
    if (len >= (int)buffer_size) {
        return -1; // 缓冲区溢出
    }
    
    return len;
}

/**
 * @brief 格式化网络配置显示响应
 * @param response_buffer 响应缓冲区
 * @param buffer_size 缓冲区大小
 * @param config 网络配置结构体
 * @return int 格式化后的字符串长度，-1表示错误
 */
int format_config_display_response(char* response_buffer, size_t buffer_size, const NetConfig_t* config)
{
    if (response_buffer == NULL || buffer_size == 0 || config == NULL) {
        return -1;
    }
    
    int len = snprintf(response_buffer, buffer_size,
        "OK:Network Configuration\r\n"
        "IP=%d.%d.%d.%d\r\n"
        "MASK=%d.%d.%d.%d\r\n"
        "GW=%d.%d.%d.%d\r\n"
        "DNS=%d.%d.%d.%d\r\n"
        "MAC=%02X:%02X:%02X:%02X:%02X:%02X\r\n"
        "DHCP=%s\r\n",
        config->ip[0], config->ip[1], config->ip[2], config->ip[3],
        config->subnet[0], config->subnet[1], config->subnet[2], config->subnet[3],
        config->gateway[0], config->gateway[1], config->gateway[2], config->gateway[3],
        config->dns[0], config->dns[1], config->dns[2], config->dns[3],
        config->mac[0], config->mac[1], config->mac[2], config->mac[3], config->mac[4], config->mac[5],
        config->dhcp_mode ? "Enabled" : "Disabled"
    );
    
    if (len >= (int)buffer_size) {
        return -1; // 缓冲区溢出
    }
    
    return len;
}

/**
 * @brief 通用响应格式化函数
 * @param response_buffer 响应缓冲区
 * @param buffer_size 缓冲区大小
 * @param error_code 错误代码 (NET_CONFIG_OK表示成功)
 * @param message 消息内容
 * @return int 格式化后的字符串长度，-1表示错误
 */
int format_response(char* response_buffer, size_t buffer_size, NetConfigError_t error_code, const char* message)
{
    if (error_code == NET_CONFIG_OK) {
        return format_success_response(response_buffer, buffer_size, message);
    } else {
        return format_error_response(response_buffer, buffer_size, error_code, message);
    }
}

/* 存储管理模块实现 */

// CRC32查找表
static const uint32_t crc32_table[256] = {
    0x00000000, 0x77073096, 0xEE0E612C, 0x990951BA, 0x076DC419, 0x706AF48F,
    0xE963A535, 0x9E6495A3, 0x0EDB8832, 0x79DCB8A4, 0xE0D5E91E, 0x97D2D988,
    0x09B64C2B, 0x7EB17CBD, 0xE7B82D07, 0x90BF1D91, 0x1DB71064, 0x6AB020F2,
    0xF3B97148, 0x84BE41DE, 0x1ADAD47D, 0x6DDDE4EB, 0xF4D4B551, 0x83D385C7,
    0x136C9856, 0x646BA8C0, 0xFD62F97A, 0x8A65C9EC, 0x14015C4F, 0x63066CD9,
    0xFA0F3D63, 0x8D080DF5, 0x3B6E20C8, 0x4C69105E, 0xD56041E4, 0xA2677172,
    0x3C03E4D1, 0x4B04D447, 0xD20D85FD, 0xA50AB56B, 0x35B5A8FA, 0x42B2986C,
    0xDBBBC9D6, 0xACBCF940, 0x32D86CE3, 0x45DF5C75, 0xDCD60DCF, 0xABD13D59,
    0x26D930AC, 0x51DE003A, 0xC8D75180, 0xBFD06116, 0x21B4F4B5, 0x56B3C423,
    0xCFBA9599, 0xB8BDA50F, 0x2802B89E, 0x5F058808, 0xC60CD9B2, 0xB10BE924,
    0x2F6F7C87, 0x58684C11, 0xC1611DAB, 0xB6662D3D, 0x76DC4190, 0x01DB7106,
    0x98D220BC, 0xEFD5102A, 0x71B18589, 0x06B6B51F, 0x9FBFE4A5, 0xE8B8D433,
    0x7807C9A2, 0x0F00F934, 0x9609A88E, 0xE10E9818, 0x7F6A0DBB, 0x086D3D2D,
    0x91646C97, 0xE6635C01, 0x6B6B51F4, 0x1C6C6162, 0x856530D8, 0xF262004E,
    0x6C0695ED, 0x1B01A57B, 0x8208F4C1, 0xF50FC457, 0x65B0D9C6, 0x12B7E950,
    0x8BBEB8EA, 0xFCB9887C, 0x62DD1DDF, 0x15DA2D49, 0x8CD37CF3, 0xFBD44C65,
    0x4DB26158, 0x3AB551CE, 0xA3BC0074, 0xD4BB30E2, 0x4ADFA541, 0x3DD895D7,
    0xA4D1C46D, 0xD3D6F4FB, 0x4369E96A, 0x346ED9FC, 0xAD678846, 0xDA60B8D0,
    0x44042D73, 0x33031DE5, 0xAA0A4C5F, 0xDD0D7CC9, 0x5005713C, 0x270241AA,
    0xBE0B1010, 0xC90C2086, 0x5768B525, 0x206F85B3, 0xB966D409, 0xCE61E49F,
    0x5EDEF90E, 0x29D9C998, 0xB0D09822, 0xC7D7A8B4, 0x59B33D17, 0x2EB40D81,
    0xB7BD5C3B, 0xC0BA6CAD, 0xEDB88320, 0x9ABFB3B6, 0x03B6E20C, 0x74B1D29A,
    0xEAD54739, 0x9DD277AF, 0x04DB2615, 0x73DC1683, 0xE3630B12, 0x94643B84,
    0x0D6D6A3E, 0x7A6A5AA8, 0xE40ECF0B, 0x9309FF9D, 0x0A00AE27, 0x7D079EB1,
    0xF00F9344, 0x8708A3D2, 0x1E01F268, 0x6906C2FE, 0xF762575D, 0x806567CB,
    0x196C3671, 0x6E6B06E7, 0xFED41B76, 0x89D32BE0, 0x10DA7A5A, 0x67DD4ACC,
    0xF9B9DF6F, 0x8EBEEFF9, 0x17B7BE43, 0x60B08ED5, 0xD6D6A3E8, 0xA1D1937E,
    0x38D8C2C4, 0x4FDFF252, 0xD1BB67F1, 0xA6BC5767, 0x3FB506DD, 0x48B2364B,
    0xD80D2BDA, 0xAF0A1B4C, 0x36034AF6, 0x41047A60, 0xDF60EFC3, 0xA867DF55,
    0x316E8EEF, 0x4669BE79, 0xCB61B38C, 0xBC66831A, 0x256FD2A0, 0x5268E236,
    0xCC0C7795, 0xBB0B4703, 0x220216B9, 0x5505262F, 0xC5BA3BBE, 0xB2BD0B28,
    0x2BB45A92, 0x5CB36A04, 0xC2D7FFA7, 0xB5D0CF31, 0x2CD99E8B, 0x5BDEAE1D,
    0x9B64C2B0, 0xEC63F226, 0x756AA39C, 0x026D930A, 0x9C0906A9, 0xEB0E363F,
    0x72076785, 0x05005713, 0x95BF4A82, 0xE2B87A14, 0x7BB12BAE, 0x0CB61B38,
    0x92D28E9B, 0xE5D5BE0D, 0x7CDCEFB7, 0x0BDBDF21, 0x86D3D2D4, 0xF1D4E242,
    0x68DDB3F8, 0x1FDA836E, 0x81BE16CD, 0xF6B9265B, 0x6FB077E1, 0x18B74777,
    0x88085AE6, 0xFF0F6A70, 0x66063BCA, 0x11010B5C, 0x8F659EFF, 0xF862AE69,
    0x616BFFD3, 0x166CCF45, 0xA00AE278, 0xD70DD2EE, 0x4E048354, 0x3903B3C2,
    0xA7672661, 0xD06016F7, 0x4969474D, 0x3E6E77DB, 0xAED16A4A, 0xD9D65ADC,
    0x40DF0B66, 0x37D83BF0, 0xA9BCAE53, 0xDEBB9EC5, 0x47B2CF7F, 0x30B5FFE9,
    0xBDBDF21C, 0xCABAC28A, 0x53B39330, 0x24B4A3A6, 0xBAD03605, 0xCDD70693,
    0x54DE5729, 0x23D967BF, 0xB3667A2E, 0xC4614AB8, 0x5D681B02, 0x2A6F2B94,
    0xB40BBE37, 0xC30C8EA1, 0x5A05DF1B, 0x2D02EF8D
};

/**
 * @brief 计算CRC32校验值
 * @param data 数据指针
 * @param length 数据长度
 * @return uint32_t CRC32校验值
 */
uint32_t calculate_crc32(const uint8_t* data, size_t length)
{
    uint32_t crc = 0xFFFFFFFF;
    
    if (data == NULL) {
        return 0;
    }
    
    for (size_t i = 0; i < length; i++) {
        uint8_t byte = data[i];
        crc = crc32_table[(crc ^ byte) & 0xFF] ^ (crc >> 8);
    }
    
    return crc ^ 0xFFFFFFFF;
}

/**
 * @brief 验证配置数据完整性
 * @param header 配置头部
 * @param config 网络配置数据
 * @return NetConfigError_t 验证结果
 */
NetConfigError_t verify_config_integrity(const NetConfigHeader_t* header, const NetConfig_t* config)
{
    if (header == NULL || config == NULL) {
        return NET_CONFIG_ERR_INVALID_PARAM;
    }
    
    // 检查魔数
    if (header->magic != NET_CONFIG_MAGIC_NUMBER) {
        return NET_CONFIG_ERR_STORAGE_CRC;
    }
    
    // 检查版本
    if (header->version != NET_CONFIG_VERSION) {
        return NET_CONFIG_ERR_STORAGE_CRC;
    }
    
    // 检查数据长度
    if (header->length != sizeof(NetConfig_t)) {
        return NET_CONFIG_ERR_STORAGE_CRC;
    }
    
    // 计算并验证CRC32
    uint32_t calculated_crc = calculate_crc32((const uint8_t*)config, sizeof(NetConfig_t));
    if (calculated_crc != header->crc32) {
        return NET_CONFIG_ERR_STORAGE_CRC;
    }
    
    return NET_CONFIG_OK;
}

/**
 * @brief 擦除配置存储的Flash扇区
 * @return NetConfigError_t 操作结果
 */
NetConfigError_t erase_config_flash_sector(void)
{
    // 解锁Flash
    FLASH_Unlock();
    
    // 禁用数据缓存
    FLASH_DataCacheCmd(DISABLE);
    
    // 擦除扇区 (使用Sector 11, 地址0x080E0000对应STM32F429的Sector 11)
    FLASH_Status status = FLASH_EraseSector(FLASH_Sector_11, VoltageRange_3);
    
    // 重新启用数据缓存
    FLASH_DataCacheCmd(ENABLE);
    
    // 锁定Flash
    FLASH_Lock();
    
    if (status != FLASH_COMPLETE) {
        return NET_CONFIG_ERR_STORAGE_ERASE;
    }
    
    return NET_CONFIG_OK;
}

/**
 * @brief 将配置数据写入Flash
 * @param header 配置头部
 * @param config 网络配置数据
 * @return NetConfigError_t 操作结果
 */
NetConfigError_t write_config_to_flash(const NetConfigHeader_t* header, const NetConfig_t* config)
{
    if (header == NULL || config == NULL) {
        return NET_CONFIG_ERR_INVALID_PARAM;
    }
    
    uint32_t flash_addr = NET_CONFIG_FLASH_ADDR;
    FLASH_Status status;
    
    // 解锁Flash
    FLASH_Unlock();
    
    // 禁用数据缓存
    FLASH_DataCacheCmd(DISABLE);
    
    // 写入配置头部 (16字节)
    const uint32_t* header_data = (const uint32_t*)header;
    for (int i = 0; i < 4; i++) {
        status = FLASH_ProgramWord(flash_addr + i * 4, header_data[i]);
        if (status != FLASH_COMPLETE) {
            FLASH_DataCacheCmd(ENABLE);
            FLASH_Lock();
            return NET_CONFIG_ERR_STORAGE_WRITE;
        }
    }
    
    // 写入网络配置数据 (32字节)
    flash_addr += NET_CONFIG_HEADER_SIZE;
    const uint32_t* config_data = (const uint32_t*)config;
    for (int i = 0; i < 8; i++) { // 32字节 / 4字节 = 8个字
        status = FLASH_ProgramWord(flash_addr + i * 4, config_data[i]);
        if (status != FLASH_COMPLETE) {
            FLASH_DataCacheCmd(ENABLE);
            FLASH_Lock();
            return NET_CONFIG_ERR_STORAGE_WRITE;
        }
    }
    
    // 重新启用数据缓存
    FLASH_DataCacheCmd(ENABLE);
    
    // 锁定Flash
    FLASH_Lock();
    
    return NET_CONFIG_OK;
}

/**
 * @brief 从Flash读取配置数据
 * @param header 输出的配置头部
 * @param config 输出的网络配置数据
 * @return NetConfigError_t 操作结果
 */
NetConfigError_t read_config_from_flash(NetConfigHeader_t* header, NetConfig_t* config)
{
    if (header == NULL || config == NULL) {
        return NET_CONFIG_ERR_INVALID_PARAM;
    }
    
    uint32_t flash_addr = NET_CONFIG_FLASH_ADDR;
    
    // 读取配置头部 (16字节)
    uint32_t* header_data = (uint32_t*)header;
    for (int i = 0; i < 4; i++) {
        header_data[i] = *(volatile uint32_t*)(flash_addr + i * 4);
    }
    
    // 读取网络配置数据 (32字节)
    flash_addr += NET_CONFIG_HEADER_SIZE;
    uint32_t* config_data = (uint32_t*)config;
    for (int i = 0; i < 8; i++) { // 32字节 / 4字节 = 8个字
        config_data[i] = *(volatile uint32_t*)(flash_addr + i * 4);
    }
    
    return NET_CONFIG_OK;
}

/**
 * @brief 保存网络配置到Flash存储
 * @param config 要保存的网络配置
 * @return NetConfigError_t 操作结果
 */
NetConfigError_t save_network_config(const NetConfig_t* config)
{
    if (config == NULL) {
        return NET_CONFIG_ERR_INVALID_PARAM;
    }
    
    NetConfigError_t result;
    int retry_count = 0;
    
    // 验证配置参数
    result = validate_network_params(config);
    if (result != NET_CONFIG_OK) {
        return result;
    }
    
    // 准备配置头部
    NetConfigHeader_t header;
    header.magic = NET_CONFIG_MAGIC_NUMBER;
    header.version = NET_CONFIG_VERSION;
    header.length = sizeof(NetConfig_t);
    header.crc32 = calculate_crc32((const uint8_t*)config, sizeof(NetConfig_t));
    header.reserved = 0;
    
    // 重试机制
    while (retry_count < NET_CONFIG_MAX_RETRIES) {
        // 擦除Flash扇区
        result = erase_config_flash_sector();
        if (result != NET_CONFIG_OK) {
            retry_count++;
            continue;
        }
        
        // 写入配置数据
        result = write_config_to_flash(&header, config);
        if (result != NET_CONFIG_OK) {
            retry_count++;
            continue;
        }
        
        // 验证写入的数据
        NetConfigHeader_t read_header;
        NetConfig_t read_config;
        
        result = read_config_from_flash(&read_header, &read_config);
        if (result != NET_CONFIG_OK) {
            retry_count++;
            continue;
        }
        
        // 验证数据完整性
        result = verify_config_integrity(&read_header, &read_config);
        if (result != NET_CONFIG_OK) {
            retry_count++;
            continue;
        }
        
        // 验证配置内容是否一致
        if (memcmp(config, &read_config, sizeof(NetConfig_t)) != 0) {
            retry_count++;
            continue;
        }
        
        // 保存成功
        return NET_CONFIG_OK;
    }
    
    // 重试次数用完，返回存储写入错误
    return NET_CONFIG_ERR_STORAGE_WRITE;
}

/**
 * @brief 从Flash存储加载网络配置
 * @param config 输出的网络配置
 * @return NetConfigError_t 操作结果
 */
NetConfigError_t load_network_config(NetConfig_t* config)
{
    if (config == NULL) {
        return NET_CONFIG_ERR_INVALID_PARAM;
    }
    
    NetConfigHeader_t header;
    NetConfigError_t result;
    
    // 从Flash读取配置数据
    result = read_config_from_flash(&header, config);
    if (result != NET_CONFIG_OK) {
        return NET_CONFIG_ERR_STORAGE_READ;
    }
    
    // 验证数据完整性
    result = verify_config_integrity(&header, config);
    if (result != NET_CONFIG_OK) {
        return result;
    }
    
    // 验证网络参数有效性
    result = validate_network_params(config);
    if (result != NET_CONFIG_OK) {
        return result;
    }
    
    return NET_CONFIG_OK;
}

/**
 * @brief 备份当前网络配置
 * @return NetConfigError_t 操作结果
 */
NetConfigError_t backup_current_config(void)
{
    // 在这个实现中，我们将当前配置保存到Flash的备份区域
    // 由于Flash扇区大小限制，我们使用同一扇区的不同偏移地址作为备份
    
    NetConfig_t current_config;
    NetConfigError_t result;
    
    // 首先尝试加载当前配置
    result = load_network_config(&current_config);
    if (result != NET_CONFIG_OK) {
        // 如果无法加载当前配置，使用默认配置作为备份
        uint8_t default_ip[] = DEFAULT_IP_ADDR;
        uint8_t default_mask[] = DEFAULT_SUBNET_MASK;
        uint8_t default_gw[] = DEFAULT_GATEWAY;
        uint8_t default_dns[] = DEFAULT_DNS_SERVER;
        uint8_t default_mac[] = DEFAULT_MAC_ADDR;
        
        memcpy(current_config.ip, default_ip, 4);
        memcpy(current_config.subnet, default_mask, 4);
        memcpy(current_config.gateway, default_gw, 4);
        memcpy(current_config.dns, default_dns, 4);
        memcpy(current_config.mac, default_mac, 6);
        current_config.dhcp_mode = 0; // 默认静态IP
        memset(current_config.reserved, 0, sizeof(current_config.reserved));
        current_config.checksum = 0; // 将在保存时计算
    }
    
    // 准备备份头部 (使用不同的魔数标识备份)
    NetConfigHeader_t backup_header;
    backup_header.magic = NET_CONFIG_MAGIC_NUMBER ^ 0xAAAAAAAA; // 备份魔数
    backup_header.version = NET_CONFIG_VERSION;
    backup_header.length = sizeof(NetConfig_t);
    backup_header.crc32 = calculate_crc32((const uint8_t*)&current_config, sizeof(NetConfig_t));
    backup_header.reserved = 0x42414B55; // "BAKU" 备份标识
    
    // 写入备份到Flash偏移地址 (在同一扇区的后半部分)
    uint32_t backup_addr = NET_CONFIG_FLASH_ADDR + 0x1000; // 4KB偏移
    FLASH_Status status;
    
    // 解锁Flash
    FLASH_Unlock();
    FLASH_DataCacheCmd(DISABLE);
    
    // 写入备份头部
    const uint32_t* header_data = (const uint32_t*)&backup_header;
    for (int i = 0; i < 4; i++) {
        status = FLASH_ProgramWord(backup_addr + i * 4, header_data[i]);
        if (status != FLASH_COMPLETE) {
            FLASH_DataCacheCmd(ENABLE);
            FLASH_Lock();
            return NET_CONFIG_ERR_STORAGE_WRITE;
        }
    }
    
    // 写入备份配置数据
    backup_addr += NET_CONFIG_HEADER_SIZE;
    const uint32_t* config_data = (const uint32_t*)&current_config;
    for (int i = 0; i < 8; i++) {
        status = FLASH_ProgramWord(backup_addr + i * 4, config_data[i]);
        if (status != FLASH_COMPLETE) {
            FLASH_DataCacheCmd(ENABLE);
            FLASH_Lock();
            return NET_CONFIG_ERR_STORAGE_WRITE;
        }
    }
    
    FLASH_DataCacheCmd(ENABLE);
    FLASH_Lock();
    
    return NET_CONFIG_OK;
}

/**
 * @brief 恢复默认网络配置
 * @return NetConfigError_t 操作结果
 */
NetConfigError_t restore_default_config(void)
{
    NetConfig_t default_config;
    
    // 设置默认配置值
    uint8_t default_ip[] = DEFAULT_IP_ADDR;
    uint8_t default_mask[] = DEFAULT_SUBNET_MASK;
    uint8_t default_gw[] = DEFAULT_GATEWAY;
    uint8_t default_dns[] = DEFAULT_DNS_SERVER;
    uint8_t default_mac[] = DEFAULT_MAC_ADDR;
    
    memcpy(default_config.ip, default_ip, 4);
    memcpy(default_config.subnet, default_mask, 4);
    memcpy(default_config.gateway, default_gw, 4);
    memcpy(default_config.dns, default_dns, 4);
    memcpy(default_config.mac, default_mac, 6);
    default_config.dhcp_mode = 0; // 默认静态IP模式
    memset(default_config.reserved, 0, sizeof(default_config.reserved));
    default_config.checksum = 0; // 将在save_network_config中计算
    
    // 验证默认配置的有效性
    NetConfigError_t result = validate_network_params(&default_config);
    if (result != NET_CONFIG_OK) {
        return result;
    }
    
    // 保存默认配置到Flash
    return save_network_config(&default_config);
}

/**
 * @brief 应用网络配置到W5500芯片
 * @return NetConfigError_t 操作结果
 */
NetConfigError_t apply_network_config(void)
{
    NetConfig_t config;
    NetConfigError_t result;
    wiz_NetInfo netinfo;

    // 从Flash加载配置
    result = load_network_config(&config);
    if (result != NET_CONFIG_OK) {
        return result;
    }

    // 验证配置参数
    result = validate_network_params(&config);
    if (result != NET_CONFIG_OK) {
        return result;
    }

    // 准备W5500网络信息结构体
    memcpy(netinfo.mac, config.mac, 6);
    memcpy(netinfo.ip, config.ip, 4);
    memcpy(netinfo.sn, config.subnet, 4);
    memcpy(netinfo.gw, config.gateway, 4);
    memcpy(netinfo.dns, config.dns, 4);
    netinfo.dhcp = config.dhcp_mode ? NETINFO_DHCP : NETINFO_STATIC;

    // 应用配置到W5500芯片
    wizchip_setnetinfo(&netinfo);

    // 验证配置是否成功应用
    wiz_NetInfo read_netinfo;
    wizchip_getnetinfo(&read_netinfo);

    // 检查关键配置是否正确应用
    if (memcmp(read_netinfo.ip, config.ip, 4) != 0 ||
        memcmp(read_netinfo.sn, config.subnet, 4) != 0 ||
        memcmp(read_netinfo.gw, config.gateway, 4) != 0) {
        return NET_CONFIG_ERR_APPLY_FAILED;
    }

    return NET_CONFIG_OK;
}