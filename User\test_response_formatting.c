#include "serial_net_config.h"
#include <stdio.h>
#include <string.h>
#include <assert.h>

/* 测试框架 */
static int test_count = 0;
static int test_passed = 0;

#define TEST_ASSERT(condition, message) \
    do { \
        test_count++; \
        if (condition) { \
            test_passed++; \
            printf("PASS: %s\n", message); \
        } else { \
            printf("FAIL: %s\n", message); \
        } \
    } while(0)

/* 测试成功响应格式化 */
void test_format_success_response(void)
{
    printf("\n=== 成功响应格式化测试 ===\n");
    
    char response_buffer[512];
    int len;
    
    // 基本成功响应
    len = format_success_response(response_buffer, sizeof(response_buffer), "Configuration saved");
    TEST_ASSERT(len > 0, "基本成功响应返回长度 > 0");
    TEST_ASSERT(strstr(response_buffer, "OK:Configuration saved") != NULL, "包含正确的成功消息");
    TEST_ASSERT(strstr(response_buffer, "\r\n") != NULL, "包含正确的行结束符");
    printf("成功响应: %s", response_buffer);
    
    // 空消息测试
    len = format_success_response(response_buffer, sizeof(response_buffer), NULL);
    TEST_ASSERT(len > 0, "空消息成功响应返回长度 > 0");
    TEST_ASSERT(strstr(response_buffer, "OK:OK") != NULL, "空消息使用默认'OK'");
    printf("空消息响应: %s", response_buffer);
    
    // 空字符串消息测试
    len = format_success_response(response_buffer, sizeof(response_buffer), "");
    TEST_ASSERT(len > 0, "空字符串消息成功响应返回长度 > 0");
    TEST_ASSERT(strstr(response_buffer, "OK:") != NULL, "空字符串消息正确处理");
    
    // 长消息测试
    const char* long_msg = "This is a very long success message that should still be formatted correctly";
    len = format_success_response(response_buffer, sizeof(response_buffer), long_msg);
    TEST_ASSERT(len > 0, "长消息成功响应返回长度 > 0");
    TEST_ASSERT(strstr(response_buffer, long_msg) != NULL, "长消息正确包含");
    
    // 缓冲区溢出测试
    char small_buffer[20];
    len = format_success_response(small_buffer, sizeof(small_buffer), "This message is too long for the buffer");
    TEST_ASSERT(len == -1, "缓冲区溢出返回 -1");
    
    // 空缓冲区测试
    len = format_success_response(NULL, 100, "test");
    TEST_ASSERT(len == -1, "空缓冲区返回 -1");
    
    // 零长度缓冲区测试
    len = format_success_response(response_buffer, 0, "test");
    TEST_ASSERT(len == -1, "零长度缓冲区返回 -1");
}

/* 测试错误响应格式化 */
void test_format_error_response(void)
{
    printf("\n=== 错误响应格式化测试 ===\n");
    
    char response_buffer[512];
    int len;
    
    // 基本错误响应
    len = format_error_response(response_buffer, sizeof(response_buffer), NET_CONFIG_ERR_INVALID_IP, NULL);
    TEST_ASSERT(len > 0, "基本错误响应返回长度 > 0");
    TEST_ASSERT(strstr(response_buffer, "ERROR:Invalid IP address format") != NULL, "包含正确的错误消息");
    TEST_ASSERT(strstr(response_buffer, "\r\n") != NULL, "包含正确的行结束符");
    printf("基本错误响应: %s", response_buffer);
    
    // 带详细信息的错误响应
    len = format_error_response(response_buffer, sizeof(response_buffer), NET_CONFIG_ERR_INVALID_IP, "192.168.1.256");
    TEST_ASSERT(len > 0, "带详细信息错误响应返回长度 > 0");
    TEST_ASSERT(strstr(response_buffer, "Invalid IP address format - 192.168.1.256") != NULL, "包含详细错误信息");
    printf("详细错误响应: %s", response_buffer);
    
    // 空详细信息测试
    len = format_error_response(response_buffer, sizeof(response_buffer), NET_CONFIG_ERR_AUTH_FAIL, "");
    TEST_ASSERT(len > 0, "空详细信息错误响应返回长度 > 0");
    TEST_ASSERT(strstr(response_buffer, "ERROR:Authentication failed") != NULL, "空详细信息正确处理");
    
    // 测试所有错误代码
    NetConfigError_t error_codes[] = {
        NET_CONFIG_ERR_INVALID_MASK,
        NET_CONFIG_ERR_INVALID_GW,
        NET_CONFIG_ERR_INVALID_DNS,
        NET_CONFIG_ERR_INVALID_CMD,
        NET_CONFIG_ERR_INVALID_PARAM,
        NET_CONFIG_ERR_AUTH_REQUIRED,
        NET_CONFIG_ERR_AUTH_LOCKED,
        NET_CONFIG_ERR_SESSION_TIMEOUT,
        NET_CONFIG_ERR_STORAGE_READ,
        NET_CONFIG_ERR_STORAGE_WRITE,
        NET_CONFIG_ERR_STORAGE_ERASE,
        NET_CONFIG_ERR_STORAGE_CRC,
        NET_CONFIG_ERR_NETWORK_APPLY,
        NET_CONFIG_ERR_NETWORK_TEST,
        NET_CONFIG_ERR_TIMEOUT,
        NET_CONFIG_ERR_BUSY,
        NET_CONFIG_ERR_NOT_SUPPORTED,
        NET_CONFIG_ERR_UNKNOWN
    };
    
    for (int i = 0; i < sizeof(error_codes) / sizeof(error_codes[0]); i++) {
        len = format_error_response(response_buffer, sizeof(response_buffer), error_codes[i], NULL);
        TEST_ASSERT(len > 0, "所有错误代码都能正确格式化");
        TEST_ASSERT(strstr(response_buffer, "ERROR:") != NULL, "所有错误响应都包含ERROR前缀");
    }
    
    // 缓冲区溢出测试
    char small_buffer[30];
    len = format_error_response(small_buffer, sizeof(small_buffer), NET_CONFIG_ERR_INVALID_IP, "Very long detailed error message");
    TEST_ASSERT(len == -1, "错误响应缓冲区溢出返回 -1");
}

/* 测试网络配置显示响应格式化 */
void test_format_config_display_response(void)
{
    printf("\n=== 网络配置显示响应格式化测试 ===\n");
    
    char response_buffer[1024];
    int len;
    NetConfig_t config;
    
    // 标准网络配置
    config.ip[0] = 192; config.ip[1] = 168; config.ip[2] = 1; config.ip[3] = 100;
    config.subnet[0] = 255; config.subnet[1] = 255; config.subnet[2] = 255; config.subnet[3] = 0;
    config.gateway[0] = 192; config.gateway[1] = 168; config.gateway[2] = 1; config.gateway[3] = 1;
    config.dns[0] = 8; config.dns[1] = 8; config.dns[2] = 8; config.dns[3] = 8;
    config.mac[0] = 0x00; config.mac[1] = 0x08; config.mac[2] = 0xDC;
    config.mac[3] = 0x01; config.mac[4] = 0x02; config.mac[5] = 0x03;
    config.dhcp_mode = 0;
    
    len = format_config_display_response(response_buffer, sizeof(response_buffer), &config);
    TEST_ASSERT(len > 0, "配置显示响应返回长度 > 0");
    TEST_ASSERT(strstr(response_buffer, "OK:Network Configuration") != NULL, "包含配置标题");
    TEST_ASSERT(strstr(response_buffer, "IP=*************") != NULL, "包含正确的IP地址");
    TEST_ASSERT(strstr(response_buffer, "MASK=*************") != NULL, "包含正确的子网掩码");
    TEST_ASSERT(strstr(response_buffer, "GW=***********") != NULL, "包含正确的网关");
    TEST_ASSERT(strstr(response_buffer, "DNS=*******") != NULL, "包含正确的DNS");
    TEST_ASSERT(strstr(response_buffer, "MAC=00:08:DC:01:02:03") != NULL, "包含正确的MAC地址");
    TEST_ASSERT(strstr(response_buffer, "DHCP=Disabled") != NULL, "包含正确的DHCP状态");
    printf("配置显示响应:\n%s", response_buffer);
    
    // DHCP启用的配置
    config.dhcp_mode = 1;
    len = format_config_display_response(response_buffer, sizeof(response_buffer), &config);
    TEST_ASSERT(strstr(response_buffer, "DHCP=Enabled") != NULL, "DHCP启用状态正确显示");
    
    // 边界值测试
    config.ip[0] = 0; config.ip[1] = 0; config.ip[2] = 0; config.ip[3] = 0;
    config.subnet[0] = 255; config.subnet[1] = 255; config.subnet[2] = 255; config.subnet[3] = 255;
    config.gateway[0] = 255; config.gateway[1] = 255; config.gateway[2] = 255; config.gateway[3] = 255;
    config.dns[0] = 255; config.dns[1] = 255; config.dns[2] = 255; config.dns[3] = 255;
    config.mac[0] = 0xFF; config.mac[1] = 0xFF; config.mac[2] = 0xFF;
    config.mac[3] = 0xFF; config.mac[4] = 0xFF; config.mac[5] = 0xFF;
    
    len = format_config_display_response(response_buffer, sizeof(response_buffer), &config);
    TEST_ASSERT(len > 0, "边界值配置正确格式化");
    TEST_ASSERT(strstr(response_buffer, "IP=0.0.0.0") != NULL, "边界值IP正确显示");
    TEST_ASSERT(strstr(response_buffer, "MAC=FF:FF:FF:FF:FF:FF") != NULL, "边界值MAC正确显示");
    
    // 空指针测试
    len = format_config_display_response(response_buffer, sizeof(response_buffer), NULL);
    TEST_ASSERT(len == -1, "空配置指针返回 -1");
    
    // 空缓冲区测试
    len = format_config_display_response(NULL, 100, &config);
    TEST_ASSERT(len == -1, "空缓冲区返回 -1");
    
    // 缓冲区太小测试
    char tiny_buffer[50];
    len = format_config_display_response(tiny_buffer, sizeof(tiny_buffer), &config);
    TEST_ASSERT(len == -1, "缓冲区太小返回 -1");
}

/* 测试通用响应格式化函数 */
void test_format_response(void)
{
    printf("\n=== 通用响应格式化测试 ===\n");
    
    char response_buffer[512];
    int len;
    
    // 成功响应测试
    len = format_response(response_buffer, sizeof(response_buffer), NET_CONFIG_OK, "Operation completed");
    TEST_ASSERT(len > 0, "成功响应返回长度 > 0");
    TEST_ASSERT(strstr(response_buffer, "OK:Operation completed") != NULL, "成功响应正确格式化");
    
    // 错误响应测试
    len = format_response(response_buffer, sizeof(response_buffer), NET_CONFIG_ERR_INVALID_IP, "Invalid input");
    TEST_ASSERT(len > 0, "错误响应返回长度 > 0");
    TEST_ASSERT(strstr(response_buffer, "ERROR:Invalid IP address format - Invalid input") != NULL, "错误响应正确格式化");
    
    // 空消息测试
    len = format_response(response_buffer, sizeof(response_buffer), NET_CONFIG_OK, NULL);
    TEST_ASSERT(len > 0, "空消息成功响应正确处理");
    
    len = format_response(response_buffer, sizeof(response_buffer), NET_CONFIG_ERR_AUTH_FAIL, NULL);
    TEST_ASSERT(len > 0, "空消息错误响应正确处理");
}

/* 测试错误消息获取函数 */
void test_get_error_message(void)
{
    printf("\n=== 错误消息获取测试 ===\n");
    
    // 测试所有已知错误代码
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_OK), "Success") == 0, "成功消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_INVALID_IP), "Invalid IP address format") == 0, "IP错误消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_INVALID_MASK), "Invalid subnet mask") == 0, "掩码错误消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_INVALID_GW), "Invalid gateway address") == 0, "网关错误消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_INVALID_DNS), "Invalid DNS server address") == 0, "DNS错误消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_INVALID_CMD), "Unknown command") == 0, "命令错误消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_INVALID_PARAM), "Invalid parameter") == 0, "参数错误消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_AUTH_FAIL), "Authentication failed") == 0, "认证失败消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_AUTH_REQUIRED), "Authentication required") == 0, "需要认证消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_AUTH_LOCKED), "Access locked due to too many failed attempts") == 0, "锁定消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_SESSION_TIMEOUT), "Session timeout") == 0, "会话超时消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_STORAGE_READ), "Storage read error") == 0, "存储读取错误消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_STORAGE_WRITE), "Storage write error") == 0, "存储写入错误消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_STORAGE_ERASE), "Storage erase error") == 0, "存储擦除错误消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_STORAGE_CRC), "Storage CRC check failed") == 0, "CRC错误消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_NETWORK_APPLY), "Failed to apply network configuration") == 0, "网络应用错误消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_NETWORK_TEST), "Network connectivity test failed") == 0, "网络测试错误消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_TIMEOUT), "Operation timeout") == 0, "超时错误消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_BUSY), "System busy") == 0, "系统忙错误消息正确");
    TEST_ASSERT(strcmp(get_error_message(NET_CONFIG_ERR_NOT_SUPPORTED), "Operation not supported") == 0, "不支持操作错误消息正确");
    
    // 测试未知错误代码
    TEST_ASSERT(strcmp(get_error_message((NetConfigError_t)999), "Unknown error") == 0, "未知错误消息正确");
}

/* 测试多行响应格式化 */
void test_multiline_response_formatting(void)
{
    printf("\n=== 多行响应格式化测试 ===\n");
    
    char response_buffer[1024];
    NetConfig_t config;
    
    // 设置测试配置
    config.ip[0] = 10; config.ip[1] = 0; config.ip[2] = 0; config.ip[3] = 100;
    config.subnet[0] = 255; config.subnet[1] = 0; config.subnet[2] = 0; config.subnet[3] = 0;
    config.gateway[0] = 10; config.gateway[1] = 0; config.gateway[2] = 0; config.gateway[3] = 1;
    config.dns[0] = 1; config.dns[1] = 1; config.dns[2] = 1; config.dns[3] = 1;
    config.mac[0] = 0xAA; config.mac[1] = 0xBB; config.mac[2] = 0xCC;
    config.mac[3] = 0xDD; config.mac[4] = 0xEE; config.mac[5] = 0xFF;
    config.dhcp_mode = 1;
    
    int len = format_config_display_response(response_buffer, sizeof(response_buffer), &config);
    
    // 检查多行格式
    TEST_ASSERT(len > 0, "多行响应返回长度 > 0");
    
    // 计算行数
    int line_count = 0;
    char* ptr = response_buffer;
    while ((ptr = strstr(ptr, "\r\n")) != NULL) {
        line_count++;
        ptr += 2;
    }
    TEST_ASSERT(line_count >= 6, "多行响应包含足够的行数");
    
    // 检查每行都有正确的格式
    TEST_ASSERT(strstr(response_buffer, "OK:Network Configuration\r\n") != NULL, "标题行格式正确");
    TEST_ASSERT(strstr(response_buffer, "IP=********00\r\n") != NULL, "IP行格式正确");
    TEST_ASSERT(strstr(response_buffer, "MASK=*********\r\n") != NULL, "掩码行格式正确");
    TEST_ASSERT(strstr(response_buffer, "GW=********\r\n") != NULL, "网关行格式正确");
    TEST_ASSERT(strstr(response_buffer, "DNS=*******\r\n") != NULL, "DNS行格式正确");
    TEST_ASSERT(strstr(response_buffer, "MAC=AA:BB:CC:DD:EE:FF\r\n") != NULL, "MAC行格式正确");
    TEST_ASSERT(strstr(response_buffer, "DHCP=Enabled\r\n") != NULL, "DHCP行格式正确");
    
    printf("多行响应示例:\n%s", response_buffer);
}

/* 测试响应格式的一致性 */
void test_response_format_consistency(void)
{
    printf("\n=== 响应格式一致性测试 ===\n");
    
    char response_buffer[512];
    
    // 测试所有成功响应都以"OK:"开头
    format_success_response(response_buffer, sizeof(response_buffer), "Test message");
    TEST_ASSERT(strncmp(response_buffer, "OK:", 3) == 0, "成功响应以OK:开头");
    
    // 测试所有错误响应都以"ERROR:"开头
    format_error_response(response_buffer, sizeof(response_buffer), NET_CONFIG_ERR_INVALID_IP, NULL);
    TEST_ASSERT(strncmp(response_buffer, "ERROR:", 6) == 0, "错误响应以ERROR:开头");
    
    // 测试所有响应都以\r\n结尾
    format_success_response(response_buffer, sizeof(response_buffer), "Test");
    int len = strlen(response_buffer);
    TEST_ASSERT(len >= 2 && strcmp(&response_buffer[len-2], "\r\n") == 0, "成功响应以\\r\\n结尾");
    
    format_error_response(response_buffer, sizeof(response_buffer), NET_CONFIG_ERR_TIMEOUT, NULL);
    len = strlen(response_buffer);
    TEST_ASSERT(len >= 2 && strcmp(&response_buffer[len-2], "\r\n") == 0, "错误响应以\\r\\n结尾");
    
    // 测试配置显示响应的一致性
    NetConfig_t config = {
        .ip = {192, 168, 1, 1},
        .subnet = {255, 255, 255, 0},
        .gateway = {192, 168, 1, 1},
        .dns = {8, 8, 8, 8},
        .mac = {0x00, 0x01, 0x02, 0x03, 0x04, 0x05},
        .dhcp_mode = 0
    };
    
    format_config_display_response(response_buffer, sizeof(response_buffer), &config);
    TEST_ASSERT(strncmp(response_buffer, "OK:", 3) == 0, "配置显示响应以OK:开头");
    len = strlen(response_buffer);
    TEST_ASSERT(len >= 2 && strcmp(&response_buffer[len-2], "\r\n") == 0, "配置显示响应以\\r\\n结尾");
}

/* 主测试函数 */
int main(void)
{
    printf("=== 命令响应格式化测试套件 ===\n");
    
    test_format_success_response();
    test_format_error_response();
    test_format_config_display_response();
    test_format_response();
    test_get_error_message();
    test_multiline_response_formatting();
    test_response_format_consistency();
    
    printf("\n=== 测试结果汇总 ===\n");
    printf("总测试数: %d\n", test_count);
    printf("通过测试: %d\n", test_passed);
    printf("失败测试: %d\n", test_count - test_passed);
    printf("通过率: %.1f%%\n", (float)test_passed / test_count * 100);
    
    if (test_count == test_passed) {
        printf("\n✅ 所有响应格式化测试通过！\n");
        return 0;
    } else {
        printf("\n❌ 部分测试失败，请检查实现。\n");
        return 1;
    }
}