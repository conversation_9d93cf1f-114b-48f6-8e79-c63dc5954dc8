<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [..\Output\NewStation-F429.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image ..\Output\NewStation-F429.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6220000: Last Updated: Tue Sep  2 16:31:43 2025
<BR><P>
<H3>Maximum Stack Usage =       2200 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; RUN_SHOW &rArr; Rate_Set &rArr; Si5340_Config &rArr; Si5340_WriteOneByteRMW &rArr; Si5340_WriteOneByte &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[2d]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2d]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[2d]">ADC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[15]">BusFault_Handler</a> from stm32f4xx_it.o(.text.BusFault_Handler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2f]">CAN1_RX0_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[30]">CAN1_RX1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[31]">CAN1_SCE_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2e]">CAN1_TX_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5b]">CAN2_RX0_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5c]">CAN2_RX1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5d]">CAN2_SCE_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5a]">CAN2_TX_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6a]">CRYP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[69]">DCMI_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[26]">DMA1_Stream0_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[27]">DMA1_Stream1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[28]">DMA1_Stream2_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[29]">DMA1_Stream3_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2a]">DMA1_Stream4_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2b]">DMA1_Stream5_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2c]">DMA1_Stream6_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4a]">DMA1_Stream7_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[75]">DMA2D_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[53]">DMA2_Stream0_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[54]">DMA2_Stream1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[55]">DMA2_Stream2_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[56]">DMA2_Stream3_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[57]">DMA2_Stream4_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5f]">DMA2_Stream5_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[60]">DMA2_Stream6_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[61]">DMA2_Stream7_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[18]">DebugMon_Handler</a> from stm32f4xx_it.o(.text.DebugMon_Handler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[58]">ETH_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[59]">ETH_WKUP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[21]">EXTI0_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[43]">EXTI15_10_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[22]">EXTI1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[23]">EXTI2_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[24]">EXTI3_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[25]">EXTI4_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[32]">EXTI9_5_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1f]">FLASH_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4b]">FMC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6c]">FPU_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6b]">HASH_RNG_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[13]">HardFault_Handler</a> from stm32f4xx_it.o(.text.HardFault_Handler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3b]">I2C1_ER_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3a]">I2C1_EV_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3d]">I2C2_ER_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3c]">I2C2_EV_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[64]">I2C3_ER_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[63]">I2C3_EV_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[74]">LTDC_ER_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[73]">LTDC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[14]">MemManage_Handler</a> from stm32f4xx_it.o(.text.MemManage_Handler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[12]">NMI_Handler</a> from stm32f4xx_it.o(.text.NMI_Handler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5e]">OTG_FS_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[45]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[66]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[65]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[68]">OTG_HS_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[67]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1c]">PVD_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[19]">PendSV_Handler</a> from stm32f4xx_it.o(.text.PendSV_Handler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[20]">RCC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[44]">RTC_Alarm_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1e]">RTC_WKUP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[11]">Reset_Handler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[72]">SAI1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4c]">SDIO_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3e]">SPI1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3f]">SPI2_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4e]">SPI3_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6f]">SPI4_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[70]">SPI5_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[71]">SPI6_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[7f]">SPI_CS_Deselect</a> from spi.o(.text.SPI_CS_Deselect) referenced 2 times from udp.o(.text.upd_connect)
 <LI><a href="#[7e]">SPI_CS_Select</a> from spi.o(.text.SPI_CS_Select) referenced 2 times from udp.o(.text.upd_connect)
 <LI><a href="#[7c]">SPI_CrisEnter</a> from spi.o(.text.SPI_CrisEnter) referenced 2 times from udp.o(.text.upd_connect)
 <LI><a href="#[7d]">SPI_CrisExit</a> from spi.o(.text.SPI_CrisExit) referenced 2 times from udp.o(.text.upd_connect)
 <LI><a href="#[80]">SPI_ReadByte</a> from spi.o(.text.SPI_ReadByte) referenced 2 times from udp.o(.text.upd_connect)
 <LI><a href="#[81]">SPI_WriteByte</a> from spi.o(.text.SPI_WriteByte) referenced 2 times from udp.o(.text.upd_connect)
 <LI><a href="#[17]">SVC_Handler</a> from stm32f4xx_it.o(.text.SVC_Handler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1a]">SysTick_Handler</a> from stm32f4xx_it.o(.text.SysTick_Handler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[77]">SystemInit</a> from system_stm32f4xx.o(.text.SystemInit) referenced from startup_stm32f429_439xx.o(.text)
 <LI><a href="#[1d]">TAMP_STAMP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[33]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[36]">TIM1_CC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[35]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[34]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[37]">TIM2_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[38]">TIM3_IRQHandler</a> from gpio.o(.text.TIM3_IRQHandler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[39]">TIM4_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4d]">TIM5_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[51]">TIM6_DAC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[52]">TIM7_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[46]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[49]">TIM8_CC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[48]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[47]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4f]">UART4_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[50]">UART5_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6d]">UART7_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6e]">UART8_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[40]">USART1_IRQHandler</a> from gpio.o(.text.USART1_IRQHandler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[41]">USART2_IRQHandler</a> from gpio.o(.text.USART2_IRQHandler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[42]">USART3_IRQHandler</a> from gpio.o(.text.USART3_IRQHandler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[62]">USART6_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[16]">UsageFault_Handler</a> from stm32f4xx_it.o(.text.UsageFault_Handler) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1b]">WWDG_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[78]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f429_439xx.o(.text)
 <LI><a href="#[83]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0snprintf)
 <LI><a href="#[84]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[c]">cs4224_diags_format_dividers</a> from cs4224_api.o(.text.cs4224_diags_format_dividers) referenced 2 times from cs4224_api.o(.data.g_edc_fields)
 <LI><a href="#[b]">cs4224_diags_format_edc_mode</a> from cs4224_api.o(.text.cs4224_diags_format_edc_mode) referenced 2 times from cs4224_api.o(.data.g_edc_fields)
 <LI><a href="#[d]">cs4224_diags_format_fracn</a> from cs4224_api.o(.text.cs4224_diags_format_fracn) referenced 2 times from cs4224_api.o(.data.g_edc_fields)
 <LI><a href="#[10]">cs4224_diags_format_pcs_status</a> from cs4224_api.o(.text.cs4224_diags_format_pcs_status) referenced 2 times from cs4224_api.o(.data.g_edc_fields)
 <LI><a href="#[e]">cs4224_diags_format_polarity_inversion</a> from cs4224_api.o(.text.cs4224_diags_format_polarity_inversion) referenced 2 times from cs4224_api.o(.data.g_edc_fields)
 <LI><a href="#[f]">cs4224_diags_format_reset_count</a> from cs4224_api.o(.text.cs4224_diags_format_reset_count) referenced 2 times from cs4224_api.o(.data.g_edc_fields)
 <LI><a href="#[a]">cs4224_diags_format_rxlock</a> from cs4224_api.o(.text.cs4224_diags_format_rxlock) referenced 2 times from cs4224_api.o(.data.g_edc_fields)
 <LI><a href="#[6]">cs4224_diags_format_sku</a> from cs4224_api.o(.text.cs4224_diags_format_sku) referenced 2 times from cs4224_api.o(.data.g_edc_fields)
 <LI><a href="#[7]">cs4224_diags_format_temperature</a> from cs4224_api.o(.text.cs4224_diags_format_temperature) referenced 2 times from cs4224_api.o(.data.g_edc_fields)
 <LI><a href="#[9]">cs4224_diags_format_voltage_0p9</a> from cs4224_api.o(.text.cs4224_diags_format_voltage_0p9) referenced 2 times from cs4224_api.o(.data.g_edc_fields)
 <LI><a href="#[8]">cs4224_diags_format_voltage_1p8</a> from cs4224_api.o(.text.cs4224_diags_format_voltage_1p8) referenced 2 times from cs4224_api.o(.data.g_edc_fields)
 <LI><a href="#[79]">cs4224_kran_wait_for_an</a> from cs4224_api.o(.text.cs4224_kran_wait_for_an) referenced 2 times from cs4224_api.o(.text.cs4224_rules_set_default)
 <LI><a href="#[82]">fputc</a> from fputc.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[76]">main</a> from main.o(.text.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[4]">wizchip_bus_readbyte</a> from wizchip_conf.o(.text.wizchip_bus_readbyte) referenced 2 times from wizchip_conf.o(.data.WIZCHIP)
 <LI><a href="#[5]">wizchip_bus_writebyte</a> from wizchip_conf.o(.text.wizchip_bus_writebyte) referenced 2 times from wizchip_conf.o(.data.WIZCHIP)
 <LI><a href="#[0]">wizchip_cris_enter</a> from wizchip_conf.o(.text.wizchip_cris_enter) referenced 2 times from wizchip_conf.o(.data.WIZCHIP)
 <LI><a href="#[0]">wizchip_cris_enter</a> from wizchip_conf.o(.text.wizchip_cris_enter) referenced 2 times from wizchip_conf.o(.text.reg_wizchip_cris_cbfunc)
 <LI><a href="#[1]">wizchip_cris_exit</a> from wizchip_conf.o(.text.wizchip_cris_exit) referenced 2 times from wizchip_conf.o(.data.WIZCHIP)
 <LI><a href="#[1]">wizchip_cris_exit</a> from wizchip_conf.o(.text.wizchip_cris_exit) referenced 2 times from wizchip_conf.o(.text.reg_wizchip_cris_cbfunc)
 <LI><a href="#[3]">wizchip_cs_deselect</a> from wizchip_conf.o(.text.wizchip_cs_deselect) referenced 2 times from wizchip_conf.o(.data.WIZCHIP)
 <LI><a href="#[3]">wizchip_cs_deselect</a> from wizchip_conf.o(.text.wizchip_cs_deselect) referenced 2 times from wizchip_conf.o(.text.reg_wizchip_cs_cbfunc)
 <LI><a href="#[2]">wizchip_cs_select</a> from wizchip_conf.o(.text.wizchip_cs_select) referenced 2 times from wizchip_conf.o(.data.WIZCHIP)
 <LI><a href="#[2]">wizchip_cs_select</a> from wizchip_conf.o(.text.wizchip_cs_select) referenced 2 times from wizchip_conf.o(.text.reg_wizchip_cs_cbfunc)
 <LI><a href="#[7a]">wizchip_spi_readbyte</a> from wizchip_conf.o(.text.wizchip_spi_readbyte) referenced 2 times from wizchip_conf.o(.text.reg_wizchip_spi_cbfunc)
 <LI><a href="#[7b]">wizchip_spi_writebyte</a> from wizchip_conf.o(.text.wizchip_spi_writebyte) referenced 2 times from wizchip_conf.o(.text.reg_wizchip_spi_cbfunc)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[78]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(.text)
</UL>
<P><STRONG><a name="[242]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[85]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[9b]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[243]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[244]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[245]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[246]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[247]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[11]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>CRYP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>DMA2D_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>LTDC_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>LTDC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[72]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>SPI6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>UART8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[87]"></a>__aeabi_ldivmod</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, ldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = __aeabi_ldivmod &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mon_volt_read_fixp
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mon_temp_read_fixp
</UL>

<P><STRONG><a name="[22f]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;show_network_status
</UL>

<P><STRONG><a name="[22c]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_network_config
</UL>

<P><STRONG><a name="[89]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReStartupConfig
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_ceil
</UL>

<P><STRONG><a name="[8e]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
</UL>

<P><STRONG><a name="[8f]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_ceil
</UL>

<P><STRONG><a name="[90]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReStartupConfig
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BER_Read
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kexue
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[91]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReStartupConfig
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BER_Read
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kexue
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[185]"></a>__aeabi_dcmpge</STRONG> (Thumb, 54 bytes, Stack size 12 bytes, dcmpge.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __aeabi_dcmpge
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_check_rules
</UL>

<P><STRONG><a name="[184]"></a>__aeabi_dcmpgt</STRONG> (Thumb, 54 bytes, Stack size 12 bytes, dcmpgt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __aeabi_dcmpgt
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_check_rules
</UL>

<P><STRONG><a name="[1bc]"></a>__aeabi_dcmpeq</STRONG> (Thumb, 56 bytes, Stack size 12 bytes, dcmpeq.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __aeabi_dcmpeq
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_rules_set_rate
</UL>

<P><STRONG><a name="[92]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReStartupConfig
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BER_Read
</UL>

<P><STRONG><a name="[93]"></a>__aeabi_ui2d</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, dfltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_ui2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReStartupConfig
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kexue
</UL>

<P><STRONG><a name="[94]"></a>__aeabi_ul2d</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, dfltul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_ul2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BER_Read
</UL>

<P><STRONG><a name="[95]"></a>__aeabi_d2iz</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, dfixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_d2iz
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
</UL>

<P><STRONG><a name="[97]"></a>__aeabi_d2uiz</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, dfixui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kexue
</UL>

<P><STRONG><a name="[a8]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BER_Read
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_check_rules
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_rules_set_rate
</UL>

<P><STRONG><a name="[98]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kexue
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[248]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[241]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[88]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
</UL>

<P><STRONG><a name="[8a]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[249]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[96]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[24a]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[8b]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[24b]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[24c]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[99]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[24d]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[8d]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[8c]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>

<P><STRONG><a name="[9a]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[23d]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_ceil
</UL>

<P><STRONG><a name="[86]"></a>__scatterload</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[24e]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[24f]"></a>__I$use$semihosting$fputc</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, iusesemip.o(.text), UNUSED)

<P><STRONG><a name="[250]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dclz77c.o(.text), UNUSED)

<P><STRONG><a name="[251]"></a>__decompress2</STRONG> (Thumb, 94 bytes, Stack size unknown bytes, __dclz77c.o(.text), UNUSED)

<P><STRONG><a name="[252]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, semi.o(.text), UNUSED)

<P><STRONG><a name="[9c]"></a>Auto_Mode</STRONG> (Thumb, 5876 bytes, Stack size 448 bytes, ber_test.o(.text.Auto_Mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 1156<LI>Call Chain = Auto_Mode &rArr; Kexue &rArr; BER_Read &rArr; BEC_Read &rArr; cs4224_diags_prbs_checker_get_errors &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kexue
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYNC_full
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYNC
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEC_Read
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_98
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_A01
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>

<P><STRONG><a name="[9f]"></a>BEC_Read</STRONG> (Thumb, 196 bytes, Stack size 32 bytes, ber_test.o(.text.BEC_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 308<LI>Call Chain = BEC_Read &rArr; cs4224_diags_prbs_checker_get_errors &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_get_errors
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BER_Read
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Auto_Mode
</UL>

<P><STRONG><a name="[a7]"></a>BER_Read</STRONG> (Thumb, 3512 bytes, Stack size 320 bytes, ber_test.o(.text.BER_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 628<LI>Call Chain = BER_Read &rArr; BEC_Read &rArr; cs4224_diags_prbs_checker_get_errors &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEC_Read
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kexue
</UL>

<P><STRONG><a name="[15]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[a9]"></a>CS4224_MAX_NUM_DIES</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, cs4224_api.o(.text.CS4224_MAX_NUM_DIES))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = CS4224_MAX_NUM_DIES &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hw_id
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_manage_ucode_download
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
</UL>

<P><STRONG><a name="[ab]"></a>CS4224_MAX_NUM_SLICES</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, cs4224_api.o(.text.CS4224_MAX_NUM_SLICES))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = CS4224_MAX_NUM_SLICES &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hw_id
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_manage_ucode_download
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reset_die_static_state
</UL>

<P><STRONG><a name="[ac]"></a>CS_MDELAY</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, cs_rtos.o(.text.CS_MDELAY))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = CS_MDELAY &rArr; CS_UDELAY &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_UDELAY
</UL>
<BR>[Called By]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_set_pfd_mode
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_wait_for_eeprom_finished
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_kran_wait_for_an
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_wait_for_an
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_fc_pre_an
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_monitor_sense_points
</UL>

<P><STRONG><a name="[19e]"></a>CS_STRLEN</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, cs_rtos.o(.text.CS_STRLEN))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CS_STRLEN
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_polarity_inversion
</UL>

<P><STRONG><a name="[ad]"></a>CS_UDELAY</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, cs_rtos.o(.text.CS_UDELAY))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = CS_UDELAY &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_MDELAY
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_vco
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_start_an
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_enable_power_savings
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_squelch_ctrl
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_stall
</UL>

<P><STRONG><a name="[15e]"></a>Checksum</STRONG> (Thumb, 68 bytes, Stack size 12 bytes, gpio.o(.text.Checksum))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = Checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_03
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_30
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_40
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_20
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_02
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_00
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_10
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_01
</UL>

<P><STRONG><a name="[18]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[224]"></a>Delay_Init</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, delay.o(.text.Delay_Init))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a4]"></a>Delay_Ms</STRONG> (Thumb, 38 bytes, Stack size 4 bytes, delay.o(.text.Delay_Ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteReg135
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Auto_Mode
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fix_network_config_error
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fix_crc_error
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_OtherRate
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_UDEFT
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_UDP
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Rate
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_More
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Timer
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Pattern
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_RTC
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_Config
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_W5500
</UL>

<P><STRONG><a name="[148]"></a>Delay_TimingMinus</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, delay.o(.text.Delay_TimingMinus))
<BR><BR>[Called By]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[ae]"></a>Delay_Us</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, delay.o(.text.Delay_Us))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Read_Byte
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Send_Byte
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_NAck
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Ack
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Stop
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Start
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByteC1
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByte
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Read_Byte
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Send_Byte
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_NAck
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Ack
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Wait_Ack
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Stop
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Start
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_UDELAY
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_W5500
</UL>

<P><STRONG><a name="[21f]"></a>EEpromReadStr</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, eeprom.o(.text.EEpromReadStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = EEpromReadStr
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;id_read
</UL>

<P><STRONG><a name="[af]"></a>EEpromWriteStr</STRONG> (Thumb, 142 bytes, Stack size 32 bytes, eeprom.o(.text.EEpromWriteStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = EEpromWriteStr &rArr; FLASH_EraseSector &rArr; FLASH_WaitForLastOperation &rArr; FLASH_GetStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ProgramHalfWord
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_DataCacheCmd
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Lock
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_EraseSector
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Unlock
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_More
</UL>

<P><STRONG><a name="[21d]"></a>FLASH_ClearFlag</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, stm32f4xx_flash.o(.text.FLASH_ClearFlag))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = FLASH_ClearFlag
</UL>
<BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;force_clear_flash_config
</UL>

<P><STRONG><a name="[b1]"></a>FLASH_DataCacheCmd</STRONG> (Thumb, 54 bytes, Stack size 4 bytes, stm32f4xx_flash.o(.text.FLASH_DataCacheCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = FLASH_DataCacheCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_config_to_flash
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;erase_config_flash_sector
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromWriteStr
</UL>

<P><STRONG><a name="[b2]"></a>FLASH_EraseSector</STRONG> (Thumb, 190 bytes, Stack size 32 bytes, stm32f4xx_flash.o(.text.FLASH_EraseSector))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = FLASH_EraseSector &rArr; FLASH_WaitForLastOperation &rArr; FLASH_GetStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;erase_config_flash_sector
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;force_clear_flash_config
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromWriteStr
</UL>

<P><STRONG><a name="[b7]"></a>FLASH_GetStatus</STRONG> (Thumb, 162 bytes, Stack size 4 bytes, stm32f4xx_flash.o(.text.FLASH_GetStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = FLASH_GetStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>

<P><STRONG><a name="[b4]"></a>FLASH_Lock</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_flash.o(.text.FLASH_Lock))
<BR><BR>[Called By]<UL><LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_config_to_flash
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;erase_config_flash_sector
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;force_clear_flash_config
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromWriteStr
</UL>

<P><STRONG><a name="[b3]"></a>FLASH_ProgramHalfWord</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, stm32f4xx_flash.o(.text.FLASH_ProgramHalfWord))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = FLASH_ProgramHalfWord &rArr; FLASH_WaitForLastOperation &rArr; FLASH_GetStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromWriteStr
</UL>

<P><STRONG><a name="[b6]"></a>FLASH_ProgramWord</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, stm32f4xx_flash.o(.text.FLASH_ProgramWord))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = FLASH_ProgramWord &rArr; FLASH_WaitForLastOperation &rArr; FLASH_GetStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>
<BR>[Called By]<UL><LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_config_to_flash
</UL>

<P><STRONG><a name="[b0]"></a>FLASH_Unlock</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, stm32f4xx_flash.o(.text.FLASH_Unlock))
<BR><BR>[Called By]<UL><LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_config_to_flash
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;erase_config_flash_sector
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;force_clear_flash_config
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromWriteStr
</UL>

<P><STRONG><a name="[b5]"></a>FLASH_WaitForLastOperation</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, stm32f4xx_flash.o(.text.FLASH_WaitForLastOperation))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = FLASH_WaitForLastOperation &rArr; FLASH_GetStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_GetStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ProgramHalfWord
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ProgramWord
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_EraseSector
</UL>

<P><STRONG><a name="[b8]"></a>FreqData_Write</STRONG> (Thumb, 126 bytes, Stack size 24 bytes, si570abb.o(.text.FreqData_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = FreqData_Write &rArr; Si570_WriteOneByte &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteReg135
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_ReadOneByte
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Freq_Set
</UL>

<P><STRONG><a name="[bc]"></a>Freq_Set</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, si570abb.o(.text.Freq_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = Freq_Set &rArr; FreqData_Write &rArr; Si570_WriteOneByte &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreqData_Write
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReStartupConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c1]"></a>GPIO_Init</STRONG> (Thumb, 246 bytes, Stack size 20 bytes, stm32f4xx_gpio.o(.text.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_Init
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Init
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_Out_Init
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_Address_Init
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SiCLK_Out_Init
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;POWER_Init
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_Init
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_MasterInit
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;w5500_init
</UL>

<P><STRONG><a name="[137]"></a>GPIO_PinAFConfig</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text.GPIO_PinAFConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GPIO_PinAFConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_Init
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_MasterInit
</UL>

<P><STRONG><a name="[cb]"></a>GPIO_ResetBits</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, stm32f4xx_gpio.o(.text.GPIO_ResetBits))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_Init
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_Out_Init
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_Address_Init
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SiCLK_Out_Init
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;POWER_Init
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CS_Select
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_W5500
</UL>

<P><STRONG><a name="[c2]"></a>GPIO_SetBits</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, stm32f4xx_gpio.o(.text.GPIO_SetBits))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GPIO_SetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Init
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_Out_Init
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;POWER_Init
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CS_Deselect
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_MasterInit
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_W5500
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;w5500_init
</UL>

<P><STRONG><a name="[13]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[bf]"></a>IICDIV_Ack</STRONG> (Thumb, 126 bytes, Stack size 24 bytes, iic_si5340.o(.text.IICDIV_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = IICDIV_Ack &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Read_Byte
</UL>

<P><STRONG><a name="[c0]"></a>IICDIV_Init</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, iic_si5340.o(.text.IICDIV_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = IICDIV_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c3]"></a>IICDIV_NAck</STRONG> (Thumb, 126 bytes, Stack size 24 bytes, iic_si5340.o(.text.IICDIV_NAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = IICDIV_NAck &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Read_Byte
</UL>

<P><STRONG><a name="[c4]"></a>IICDIV_Read_Byte</STRONG> (Thumb, 182 bytes, Stack size 24 bytes, iic_si5340.o(.text.IICDIV_Read_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = IICDIV_Read_Byte &rArr; IICDIV_NAck &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_NAck
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Ack
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_ReadOneByte
</UL>

<P><STRONG><a name="[c5]"></a>IICDIV_Send_Byte</STRONG> (Thumb, 212 bytes, Stack size 24 bytes, iic_si5340.o(.text.IICDIV_Send_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = IICDIV_Send_Byte &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByteC1
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByte
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_ReadOneByte
</UL>

<P><STRONG><a name="[c6]"></a>IICDIV_Start</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, iic_si5340.o(.text.IICDIV_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = IICDIV_Start &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByteC1
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByte
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_ReadOneByte
</UL>

<P><STRONG><a name="[c7]"></a>IICDIV_Stop</STRONG> (Thumb, 134 bytes, Stack size 24 bytes, iic_si5340.o(.text.IICDIV_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = IICDIV_Stop &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByteC1
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByte
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_ReadOneByte
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Wait_Ack
</UL>

<P><STRONG><a name="[c8]"></a>IICDIV_Wait_Ack</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, iic_si5340.o(.text.IICDIV_Wait_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = IICDIV_Wait_Ack &rArr; IICDIV_Stop &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Stop
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByteC1
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByte
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_ReadOneByte
</UL>

<P><STRONG><a name="[c9]"></a>IIC_Ack</STRONG> (Thumb, 126 bytes, Stack size 24 bytes, iic_simulation.o(.text.IIC_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = IIC_Ack &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Read_Byte
</UL>

<P><STRONG><a name="[ca]"></a>IIC_Init</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, iic_simulation.o(.text.IIC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = IIC_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[cc]"></a>IIC_NAck</STRONG> (Thumb, 126 bytes, Stack size 24 bytes, iic_simulation.o(.text.IIC_NAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = IIC_NAck &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Read_Byte
</UL>

<P><STRONG><a name="[cd]"></a>IIC_Read_Byte</STRONG> (Thumb, 182 bytes, Stack size 24 bytes, iic_simulation.o(.text.IIC_Read_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = IIC_Read_Byte &rArr; IIC_NAck &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_NAck
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Ack
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_ReadOneByte
</UL>

<P><STRONG><a name="[ce]"></a>IIC_Send_Byte</STRONG> (Thumb, 212 bytes, Stack size 24 bytes, iic_simulation.o(.text.IIC_Send_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = IIC_Send_Byte &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteReg135
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_ReadOneByte
</UL>

<P><STRONG><a name="[cf]"></a>IIC_Start</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, iic_simulation.o(.text.IIC_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = IIC_Start &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteReg135
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_ReadOneByte
</UL>

<P><STRONG><a name="[d0]"></a>IIC_Stop</STRONG> (Thumb, 134 bytes, Stack size 24 bytes, iic_simulation.o(.text.IIC_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = IIC_Stop &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteReg135
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_ReadOneByte
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
</UL>

<P><STRONG><a name="[d1]"></a>IIC_Wait_Ack</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, iic_simulation.o(.text.IIC_Wait_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = IIC_Wait_Ack &rArr; IIC_Stop &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Stop
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteReg135
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_ReadOneByte
</UL>

<P><STRONG><a name="[a2]"></a>Kexue</STRONG> (Thumb, 1176 bytes, Stack size 80 bytes, ber_test.o(.text.Kexue))
<BR><BR>[Stack]<UL><LI>Max Depth = 708<LI>Call Chain = Kexue &rArr; BER_Read &rArr; BEC_Read &rArr; cs4224_diags_prbs_checker_get_errors &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BER_Read
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Auto_Mode
</UL>

<P><STRONG><a name="[161]"></a>KeyboardDataMove</STRONG> (Thumb, 76 bytes, Stack size 12 bytes, gpio.o(.text.KeyboardDataMove))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = KeyboardDataMove
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[d2]"></a>LCD_Init</STRONG> (Thumb, 180 bytes, Stack size 40 bytes, gpio.o(.text.LCD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 840<LI>Call Chain = LCD_Init &rArr; update_status &rArr; show_network_status &rArr; fix_crc_error &rArr; fix_network_config_error &rArr; VIEWTECH_98 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_70
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_status
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d5]"></a>MDC2_Period</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, mdio_simulation.o(.text.MDC2_Period))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_Reg_Addr_Data_Write
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_TA
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_DEVAD
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_PRTAD
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_OP
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_ST
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_PRE
</UL>

<P><STRONG><a name="[d7]"></a>MDC_Period</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, mdio_simulation.o(.text.MDC_Period))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = MDC_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_Reg_Addr_Data_Write
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_TA
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_DEVAD
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_PRTAD
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_OP
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_ST
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_PRE
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d8]"></a>MDIO2_DEVAD</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, mdio_simulation.o(.text.MDIO2_DEVAD))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = MDIO2_DEVAD &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC2_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_write
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_read
</UL>

<P><STRONG><a name="[d9]"></a>MDIO2_IDLE</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, mdio_simulation.o(.text.MDIO2_IDLE))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = MDIO2_IDLE &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_write
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_read
</UL>

<P><STRONG><a name="[da]"></a>MDIO2_OP</STRONG> (Thumb, 162 bytes, Stack size 40 bytes, mdio_simulation.o(.text.MDIO2_OP))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC2_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_write
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_read
</UL>

<P><STRONG><a name="[db]"></a>MDIO2_PRE</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, mdio_simulation.o(.text.MDIO2_PRE))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = MDIO2_PRE &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC2_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_write
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_read
</UL>

<P><STRONG><a name="[dc]"></a>MDIO2_PRTAD</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, mdio_simulation.o(.text.MDIO2_PRTAD))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = MDIO2_PRTAD &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC2_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_write
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_read
</UL>

<P><STRONG><a name="[dd]"></a>MDIO2_Reg_Addr_Data_Read</STRONG> (Thumb, 166 bytes, Stack size 16 bytes, mdio_simulation.o(.text.MDIO2_Reg_Addr_Data_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = MDIO2_Reg_Addr_Data_Read &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_read
</UL>

<P><STRONG><a name="[de]"></a>MDIO2_Reg_Addr_Data_Write</STRONG> (Thumb, 150 bytes, Stack size 16 bytes, mdio_simulation.o(.text.MDIO2_Reg_Addr_Data_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = MDIO2_Reg_Addr_Data_Write &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC2_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_write
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_read
</UL>

<P><STRONG><a name="[df]"></a>MDIO2_ST</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, mdio_simulation.o(.text.MDIO2_ST))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = MDIO2_ST &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC2_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_write
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_read
</UL>

<P><STRONG><a name="[e0]"></a>MDIO2_TA</STRONG> (Thumb, 344 bytes, Stack size 32 bytes, mdio_simulation.o(.text.MDIO2_TA))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = MDIO2_TA &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC2_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_write
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_read
</UL>

<P><STRONG><a name="[e1]"></a>MDIO_DEVAD</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, mdio_simulation.o(.text.MDIO_DEVAD))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = MDIO_DEVAD &rArr; MDC_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[e2]"></a>MDIO_IDLE</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, mdio_simulation.o(.text.MDIO_IDLE))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = MDIO_IDLE &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[e3]"></a>MDIO_Init</STRONG> (Thumb, 158 bytes, Stack size 32 bytes, mdio_simulation.o(.text.MDIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = MDIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e4]"></a>MDIO_OP</STRONG> (Thumb, 162 bytes, Stack size 40 bytes, mdio_simulation.o(.text.MDIO_OP))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = MDIO_OP &rArr; MDC_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[e5]"></a>MDIO_PRE</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, mdio_simulation.o(.text.MDIO_PRE))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = MDIO_PRE &rArr; MDC_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[e6]"></a>MDIO_PRTAD</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, mdio_simulation.o(.text.MDIO_PRTAD))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = MDIO_PRTAD &rArr; MDC_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[e7]"></a>MDIO_Reg_Addr_Data_Read</STRONG> (Thumb, 166 bytes, Stack size 16 bytes, mdio_simulation.o(.text.MDIO_Reg_Addr_Data_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = MDIO_Reg_Addr_Data_Read &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[e8]"></a>MDIO_Reg_Addr_Data_Write</STRONG> (Thumb, 150 bytes, Stack size 16 bytes, mdio_simulation.o(.text.MDIO_Reg_Addr_Data_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = MDIO_Reg_Addr_Data_Write &rArr; MDC_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[e9]"></a>MDIO_ST</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, mdio_simulation.o(.text.MDIO_ST))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = MDIO_ST &rArr; MDC_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[ea]"></a>MDIO_TA</STRONG> (Thumb, 344 bytes, Stack size 32 bytes, mdio_simulation.o(.text.MDIO_TA))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = MDIO_TA &rArr; MDC_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>

<P><STRONG><a name="[d6]"></a>MDIO_delay</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, mdio_simulation.o(.text.MDIO_delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = MDIO_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_write
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_read
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_IDLE
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_IDLE
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_Reg_Addr_Data_Read
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_Reg_Addr_Data_Read
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC2_Period
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
</UL>

<P><STRONG><a name="[14]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[eb]"></a>Menu_EQDisValue</STRONG> (Thumb, 412 bytes, Stack size 32 bytes, lcd_interface.o(.text.Menu_EQDisValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = Menu_EQDisValue &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_A01
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_More
</UL>

<P><STRONG><a name="[ec]"></a>Menu_EQSet</STRONG> (Thumb, 1664 bytes, Stack size 32 bytes, lcd_interface.o(.text.Menu_EQSet))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = Menu_EQSet &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_A01
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_More
</UL>

<P><STRONG><a name="[ed]"></a>Menu_More</STRONG> (Thumb, 12106 bytes, Stack size 328 bytes, lcd_interface.o(.text.Menu_More))
<BR><BR>[Stack]<UL><LI>Max Depth = 1128<LI>Call Chain = Menu_More &rArr; update_status &rArr; show_network_status &rArr; fix_crc_error &rArr; fix_network_config_error &rArr; VIEWTECH_98 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_EQSet
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_EQDisValue
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromWriteStr
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_7C01
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_id
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_98
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_70
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_status
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XY_touch
</UL>

<P><STRONG><a name="[f0]"></a>Menu_OtherRate</STRONG> (Thumb, 1902 bytes, Stack size 40 bytes, lcd_interface.o(.text.Menu_OtherRate))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = Menu_OtherRate &rArr; VIEWTECH_71 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_70
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Rate
</UL>

<P><STRONG><a name="[f1]"></a>Menu_Pattern</STRONG> (Thumb, 4198 bytes, Stack size 96 bytes, lcd_interface.o(.text.Menu_Pattern))
<BR><BR>[Stack]<UL><LI>Max Depth = 1560<LI>Call Chain = Menu_Pattern &rArr; Menu_UDP &rArr; User_defined_pattern &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_UDP
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_70
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_status
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XY_touch
</UL>

<P><STRONG><a name="[f3]"></a>Menu_RTC</STRONG> (Thumb, 2190 bytes, Stack size 56 bytes, lcd_interface.o(.text.Menu_RTC))
<BR><BR>[Stack]<UL><LI>Max Depth = 856<LI>Call Chain = Menu_RTC &rArr; update_status &rArr; show_network_status &rArr; fix_crc_error &rArr; fix_network_config_error &rArr; VIEWTECH_98 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_E7
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_98
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_A01
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_9B5A
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_70
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_status
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XY_touch
</UL>

<P><STRONG><a name="[f6]"></a>Menu_Rate</STRONG> (Thumb, 3266 bytes, Stack size 40 bytes, lcd_interface.o(.text.Menu_Rate))
<BR><BR>[Stack]<UL><LI>Max Depth = 840<LI>Call Chain = Menu_Rate &rArr; update_status &rArr; show_network_status &rArr; fix_crc_error &rArr; fix_network_config_error &rArr; VIEWTECH_98 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_OtherRate
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_70
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_status
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XY_touch
</UL>

<P><STRONG><a name="[f7]"></a>Menu_Timer</STRONG> (Thumb, 6072 bytes, Stack size 136 bytes, lcd_interface.o(.text.Menu_Timer))
<BR><BR>[Stack]<UL><LI>Max Depth = 1008<LI>Call Chain = Menu_Timer &rArr; Menu_UDEFT &rArr; update_status &rArr; show_network_status &rArr; fix_crc_error &rArr; fix_network_config_error &rArr; VIEWTECH_98 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_UDEFT
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_70
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_status
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XY_touch
</UL>

<P><STRONG><a name="[f8]"></a>Menu_UDEFT</STRONG> (Thumb, 5204 bytes, Stack size 72 bytes, lcd_interface.o(.text.Menu_UDEFT))
<BR><BR>[Stack]<UL><LI>Max Depth = 872<LI>Call Chain = Menu_UDEFT &rArr; update_status &rArr; show_network_status &rArr; fix_crc_error &rArr; fix_network_config_error &rArr; VIEWTECH_98 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_7C01
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_98
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_A01
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_70
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_status
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Timer
</UL>

<P><STRONG><a name="[f2]"></a>Menu_UDP</STRONG> (Thumb, 9258 bytes, Stack size 928 bytes, lcd_interface.o(.text.Menu_UDP))
<BR><BR>[Stack]<UL><LI>Max Depth = 1464<LI>Call Chain = Menu_UDP &rArr; User_defined_pattern &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_defined_pattern
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_7C01
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_98
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_A01
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_70
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Pattern
</UL>

<P><STRONG><a name="[12]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[fa]"></a>NVIC_Configuration</STRONG> (Thumb, 150 bytes, Stack size 32 bytes, nvic.o(.text.NVIC_Configuration))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = NVIC_Configuration &rArr; NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fd]"></a>NVIC_Init</STRONG> (Thumb, 202 bytes, Stack size 8 bytes, misc.o(.text.NVIC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Configuration
</UL>

<P><STRONG><a name="[fb]"></a>NVIC_PriorityGroupConfig</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, misc.o(.text.NVIC_PriorityGroupConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = NVIC_PriorityGroupConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Configuration
</UL>

<P><STRONG><a name="[fe]"></a>POWER_Init</STRONG> (Thumb, 124 bytes, Stack size 32 bytes, gpio.o(.text.POWER_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = POWER_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[19]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[102]"></a>RCC_AHB1PeriphClockCmd</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text.RCC_AHB1PeriphClockCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_AHB1PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Init
</UL>

<P><STRONG><a name="[100]"></a>RCC_APB1PeriphClockCmd</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text.RCC_APB1PeriphClockCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_APB1PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Init
</UL>

<P><STRONG><a name="[135]"></a>RCC_APB1PeriphResetCmd</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text.RCC_APB1PeriphResetCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_APB1PeriphResetCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_DeInit
</UL>

<P><STRONG><a name="[101]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text.RCC_APB2PeriphClockCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_APB2PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Init
</UL>

<P><STRONG><a name="[134]"></a>RCC_APB2PeriphResetCmd</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text.RCC_APB2PeriphResetCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_APB2PeriphResetCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_DeInit
</UL>

<P><STRONG><a name="[162]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 354 bytes, Stack size 32 bytes, stm32f4xx_rcc.o(.text.RCC_GetClocksFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[ff]"></a>RCC_Init</STRONG> (Thumb, 144 bytes, Stack size 24 bytes, rcc.o(.text.RCC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = RCC_Init &rArr; RCC_AHB1PeriphClockCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[103]"></a>RUN_SHOW</STRONG> (Thumb, 1680 bytes, Stack size 72 bytes, ber_test.o(.text.RUN_SHOW))
<BR><BR>[Stack]<UL><LI>Max Depth = 2040<LI>Call Chain = RUN_SHOW &rArr; Rate_Set &rArr; Si5340_Config &rArr; Si5340_WriteOneByteRMW &rArr; Si5340_WriteOneByte &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Auto_Mode
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tra_Mode
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_squelch
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_enable
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_generator_config
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_enable
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_checker_config
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_amp_config
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[104]"></a>Rate_Set</STRONG> (Thumb, 7896 bytes, Stack size 840 bytes, ber_test.o(.text.Rate_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 1968<LI>Call Chain = Rate_Set &rArr; Si5340_Config &rArr; Si5340_WriteOneByteRMW &rArr; Si5340_WriteOneByte &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri300
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri9
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri6
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri3
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri622
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri155
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_XGSpon
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_XGpon
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpon
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Gpon
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Epon
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_otu2f
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_otu2e
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_otu2
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_5ginfi
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_10ginfi
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc2g
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc4g
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc8g
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc10g
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc16g
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_12g
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_oc48
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_oc96
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_xaui
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_rxaui
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_1g
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_10g
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_15g
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_10gwan
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_Config
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Freq_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>

<P><STRONG><a name="[bd]"></a>ReStartupConfig</STRONG> (Thumb, 416 bytes, Stack size 56 bytes, si570abb.o(.text.ReStartupConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = ReStartupConfig &rArr; Si570_WriteReg135 &rArr; IIC_Wait_Ack &rArr; IIC_Stop &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteReg135
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_ReadOneByte
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Freq_Set
</UL>

<P><STRONG><a name="[12c]"></a>Reset_W5500</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, w5500.o(.text.Reset_W5500))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Reset_W5500 &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;upd_connect
</UL>

<P><STRONG><a name="[110]"></a>Rules_Set_10g</STRONG> (Thumb, 288 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_10g))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_10g &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[11c]"></a>Rules_Set_10ginfi</STRONG> (Thumb, 280 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_10ginfi))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_10ginfi &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[10e]"></a>Rules_Set_10gwan</STRONG> (Thumb, 288 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_10gwan))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_10gwan &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[116]"></a>Rules_Set_12g</STRONG> (Thumb, 288 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_12g))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_12g &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[10f]"></a>Rules_Set_15g</STRONG> (Thumb, 292 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_15g))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_15g &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[111]"></a>Rules_Set_1g</STRONG> (Thumb, 290 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_1g))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_1g &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[11d]"></a>Rules_Set_5ginfi</STRONG> (Thumb, 282 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_5ginfi))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_5ginfi &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[123]"></a>Rules_Set_Cpon</STRONG> (Thumb, 284 bytes, Stack size 216 bytes, cs4343_operation.o(.text.Rules_Set_Cpon))
<BR><BR>[Stack]<UL><LI>Max Depth = 828<LI>Call Chain = Rules_Set_Cpon &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[126]"></a>Rules_Set_Cpri155</STRONG> (Thumb, 192 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_Cpri155))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_Cpri155 &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[128]"></a>Rules_Set_Cpri3</STRONG> (Thumb, 290 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_Cpri3))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_Cpri3 &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[12b]"></a>Rules_Set_Cpri300</STRONG> (Thumb, 218 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_Cpri300))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_Cpri300 &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[129]"></a>Rules_Set_Cpri6</STRONG> (Thumb, 290 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_Cpri6))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_Cpri6 &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[127]"></a>Rules_Set_Cpri622</STRONG> (Thumb, 290 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_Cpri622))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_Cpri622 &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[12a]"></a>Rules_Set_Cpri9</STRONG> (Thumb, 290 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_Cpri9))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_Cpri9 &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[121]"></a>Rules_Set_Epon</STRONG> (Thumb, 282 bytes, Stack size 208 bytes, cs4343_operation.o(.text.Rules_Set_Epon))
<BR><BR>[Stack]<UL><LI>Max Depth = 820<LI>Call Chain = Rules_Set_Epon &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[122]"></a>Rules_Set_Gpon</STRONG> (Thumb, 290 bytes, Stack size 224 bytes, cs4343_operation.o(.text.Rules_Set_Gpon))
<BR><BR>[Stack]<UL><LI>Max Depth = 836<LI>Call Chain = Rules_Set_Gpon &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[125]"></a>Rules_Set_XGSpon</STRONG> (Thumb, 284 bytes, Stack size 216 bytes, cs4343_operation.o(.text.Rules_Set_XGSpon))
<BR><BR>[Stack]<UL><LI>Max Depth = 828<LI>Call Chain = Rules_Set_XGSpon &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[124]"></a>Rules_Set_XGpon</STRONG> (Thumb, 284 bytes, Stack size 216 bytes, cs4343_operation.o(.text.Rules_Set_XGpon))
<BR><BR>[Stack]<UL><LI>Max Depth = 828<LI>Call Chain = Rules_Set_XGpon &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[118]"></a>Rules_Set_fc10g</STRONG> (Thumb, 288 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_fc10g))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_fc10g &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[117]"></a>Rules_Set_fc16g</STRONG> (Thumb, 294 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_fc16g))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_fc16g &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[11b]"></a>Rules_Set_fc2g</STRONG> (Thumb, 290 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_fc2g))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_fc2g &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[11a]"></a>Rules_Set_fc4g</STRONG> (Thumb, 290 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_fc4g))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_fc4g &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[119]"></a>Rules_Set_fc8g</STRONG> (Thumb, 288 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_fc8g))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_fc8g &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[115]"></a>Rules_Set_oc48</STRONG> (Thumb, 294 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_oc48))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_oc48 &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[114]"></a>Rules_Set_oc96</STRONG> (Thumb, 294 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_oc96))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_oc96 &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[11e]"></a>Rules_Set_otu2</STRONG> (Thumb, 280 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_otu2))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_otu2 &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[11f]"></a>Rules_Set_otu2e</STRONG> (Thumb, 280 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_otu2e))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_otu2e &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[120]"></a>Rules_Set_otu2f</STRONG> (Thumb, 284 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_otu2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_otu2f &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[112]"></a>Rules_Set_rxaui</STRONG> (Thumb, 288 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_rxaui))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_rxaui &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[113]"></a>Rules_Set_xaui</STRONG> (Thumb, 290 bytes, Stack size 200 bytes, cs4343_operation.o(.text.Rules_Set_xaui))
<BR><BR>[Stack]<UL><LI>Max Depth = 812<LI>Call Chain = Rules_Set_xaui &rArr; cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
</UL>

<P><STRONG><a name="[be]"></a>RunFreq</STRONG> (Thumb, 784 bytes, Stack size 56 bytes, si570abb.o(.text.RunFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = RunFreq &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_ceil
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Freq_Set
</UL>

<P><STRONG><a name="[7f]"></a>SPI_CS_Deselect</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, spi.o(.text.SPI_CS_Deselect))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI_CS_Deselect &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
</UL>
<BR>[Address Reference Count : 1]<UL><LI> udp.o(.text.upd_connect)
</UL>
<P><STRONG><a name="[7e]"></a>SPI_CS_Select</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, spi.o(.text.SPI_CS_Select))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI_CS_Select &rArr; GPIO_ResetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Address Reference Count : 1]<UL><LI> udp.o(.text.upd_connect)
</UL>
<P><STRONG><a name="[139]"></a>SPI_Cmd</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, stm32f4xx_spi.o(.text.SPI_Cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_MasterInit
</UL>

<P><STRONG><a name="[7c]"></a>SPI_CrisEnter</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, spi.o(.text.SPI_CrisEnter))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = SPI_CrisEnter
</UL>
<BR>[Address Reference Count : 1]<UL><LI> udp.o(.text.upd_connect)
</UL>
<P><STRONG><a name="[7d]"></a>SPI_CrisExit</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, spi.o(.text.SPI_CrisExit))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = SPI_CrisExit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> udp.o(.text.upd_connect)
</UL>
<P><STRONG><a name="[133]"></a>SPI_I2S_DeInit</STRONG> (Thumb, 248 bytes, Stack size 40 bytes, stm32f4xx_spi.o(.text.SPI_I2S_DeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SPI_I2S_DeInit &rArr; RCC_APB1PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphResetCmd
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_MasterInit
</UL>

<P><STRONG><a name="[13b]"></a>SPI_I2S_GetFlagStatus</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, stm32f4xx_spi.o(.text.SPI_I2S_GetFlagStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI_I2S_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_ReadAndWriteByte
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WriteByte
</UL>

<P><STRONG><a name="[13d]"></a>SPI_I2S_ReceiveData</STRONG> (Thumb, 12 bytes, Stack size 4 bytes, stm32f4xx_spi.o(.text.SPI_I2S_ReceiveData))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = SPI_I2S_ReceiveData
</UL>
<BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_ReadAndWriteByte
</UL>

<P><STRONG><a name="[13c]"></a>SPI_I2S_SendData</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, stm32f4xx_spi.o(.text.SPI_I2S_SendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI_I2S_SendData
</UL>
<BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_ReadAndWriteByte
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WriteByte
</UL>

<P><STRONG><a name="[138]"></a>SPI_Init</STRONG> (Thumb, 104 bytes, Stack size 12 bytes, stm32f4xx_spi.o(.text.SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SPI_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_MasterInit
</UL>

<P><STRONG><a name="[136]"></a>SPI_MasterInit</STRONG> (Thumb, 196 bytes, Stack size 72 bytes, spi.o(.text.SPI_MasterInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = SPI_MasterInit &rArr; SPI_I2S_DeInit &rArr; RCC_APB1PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Cmd
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Init
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;upd_connect
</UL>

<P><STRONG><a name="[13a]"></a>SPI_ReadAndWriteByte</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, spi.o(.text.SPI_ReadAndWriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SPI_ReadAndWriteByte &rArr; SPI_I2S_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_ReceiveData
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_SendData
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_ReadByte
</UL>

<P><STRONG><a name="[80]"></a>SPI_ReadByte</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, spi.o(.text.SPI_ReadByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SPI_ReadByte &rArr; SPI_ReadAndWriteByte &rArr; SPI_I2S_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_ReadAndWriteByte
</UL>
<BR>[Address Reference Count : 1]<UL><LI> udp.o(.text.upd_connect)
</UL>
<P><STRONG><a name="[81]"></a>SPI_WriteByte</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, spi.o(.text.SPI_WriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SPI_WriteByte &rArr; SPI_I2S_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_SendData
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_GetFlagStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> udp.o(.text.upd_connect)
</UL>
<P><STRONG><a name="[17]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[a0]"></a>SYNC</STRONG> (Thumb, 220 bytes, Stack size 24 bytes, ber_test.o(.text.SYNC))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = SYNC &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Auto_Mode
</UL>

<P><STRONG><a name="[a1]"></a>SYNC_full</STRONG> (Thumb, 68 bytes, Stack size 4 bytes, ber_test.o(.text.SYNC_full))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = SYNC_full
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Auto_Mode
</UL>

<P><STRONG><a name="[ee]"></a>Show_id</STRONG> (Thumb, 436 bytes, Stack size 88 bytes, lcd_interface.o(.text.Show_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = Show_id &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_98
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_A01
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_70
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_More
</UL>

<P><STRONG><a name="[13f]"></a>Si5340_Address_Init</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, gpio.o(.text.Si5340_Address_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Si5340_Address_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10d]"></a>Si5340_Config</STRONG> (Thumb, 4532 bytes, Stack size 648 bytes, iic_si5340.o(.text.Si5340_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 1128<LI>Call Chain = Si5340_Config &rArr; Si5340_WriteOneByteRMW &rArr; Si5340_WriteOneByte &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByteRMW
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByteC1
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByte
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rate_Set
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[143]"></a>Si5340_Out_Init</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, gpio.o(.text.Si5340_Out_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Si5340_Out_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[144]"></a>Si5340_ReadOneByte</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, iic_si5340.o(.text.Si5340_ReadOneByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Si5340_ReadOneByte &rArr; IICDIV_Read_Byte &rArr; IICDIV_NAck &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Read_Byte
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Send_Byte
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Wait_Ack
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Stop
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByteRMW
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByteC1
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByte
</UL>

<P><STRONG><a name="[140]"></a>Si5340_WriteOneByte</STRONG> (Thumb, 248 bytes, Stack size 48 bytes, iic_si5340.o(.text.Si5340_WriteOneByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 464<LI>Call Chain = Si5340_WriteOneByte &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_98
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_A01
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_ReadOneByte
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Send_Byte
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Wait_Ack
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Stop
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_Config
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByteRMW
</UL>

<P><STRONG><a name="[142]"></a>Si5340_WriteOneByteC1</STRONG> (Thumb, 136 bytes, Stack size 16 bytes, iic_si5340.o(.text.Si5340_WriteOneByteC1))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = Si5340_WriteOneByteC1 &rArr; Si5340_ReadOneByte &rArr; IICDIV_Read_Byte &rArr; IICDIV_NAck &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_ReadOneByte
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Send_Byte
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Wait_Ack
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Stop
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_Config
</UL>

<P><STRONG><a name="[141]"></a>Si5340_WriteOneByteRMW</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, iic_si5340.o(.text.Si5340_WriteOneByteRMW))
<BR><BR>[Stack]<UL><LI>Max Depth = 480<LI>Call Chain = Si5340_WriteOneByteRMW &rArr; Si5340_WriteOneByte &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByte
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_ReadOneByte
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_Config
</UL>

<P><STRONG><a name="[b9]"></a>Si570_ReadOneByte</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, iic_simulation.o(.text.Si570_ReadOneByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Si570_ReadOneByte &rArr; IIC_Read_Byte &rArr; IIC_NAck &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Read_Byte
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Send_Byte
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Stop
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreqData_Write
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReStartupConfig
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
</UL>

<P><STRONG><a name="[ba]"></a>Si570_WriteOneByte</STRONG> (Thumb, 248 bytes, Stack size 48 bytes, iic_simulation.o(.text.Si570_WriteOneByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 464<LI>Call Chain = Si570_WriteOneByte &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_ReadOneByte
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Send_Byte
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Stop
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Start
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_98
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_A01
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreqData_Write
</UL>

<P><STRONG><a name="[bb]"></a>Si570_WriteReg135</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, iic_simulation.o(.text.Si570_WriteReg135))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Si570_WriteReg135 &rArr; IIC_Wait_Ack &rArr; IIC_Stop &rArr; Delay_Us
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Send_Byte
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Stop
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Start
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreqData_Write
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReStartupConfig
</UL>

<P><STRONG><a name="[145]"></a>SiCLK_Out_Init</STRONG> (Thumb, 124 bytes, Stack size 32 bytes, gpio.o(.text.SiCLK_Out_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = SiCLK_Out_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14a]"></a>SysTick_CLKSourceConfig</STRONG> (Thumb, 52 bytes, Stack size 4 bytes, misc.o(.text.SysTick_CLKSourceConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = SysTick_CLKSourceConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
</UL>

<P><STRONG><a name="[1a]"></a>SysTick_Handler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_TimingMinus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[149]"></a>SysTick_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, systick.o(.text.SysTick_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SysTick_Init &rArr; SysTick_Config &rArr; NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_CLKSourceConfig
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[77]"></a>SystemInit</STRONG> (Thumb, 118 bytes, Stack size 8 bytes, system_stm32f4xx.o(.text.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SystemInit &rArr; SetSysClock
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(.text)
</UL>
<P><STRONG><a name="[38]"></a>TIM3_IRQHandler</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, gpio.o(.text.TIM3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = TIM3_IRQHandler &rArr; TIM_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearITPendingBit
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetITStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[14e]"></a>TIM3_Init</STRONG> (Thumb, 82 bytes, Stack size 32 bytes, timer.o(.text.TIM3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = TIM3_Init &rArr; TIM_DeInit &rArr; RCC_APB1PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearFlag
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[151]"></a>TIM_ClearFlag</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text.TIM_ClearFlag))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_ClearFlag
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_Init
</UL>

<P><STRONG><a name="[14d]"></a>TIM_ClearITPendingBit</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text.TIM_ClearITPendingBit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_ClearITPendingBit
</UL>
<BR>[Called By]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_IRQHandler
</UL>

<P><STRONG><a name="[153]"></a>TIM_Cmd</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text.TIM_Cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_Init
</UL>

<P><STRONG><a name="[14f]"></a>TIM_DeInit</STRONG> (Thumb, 540 bytes, Stack size 72 bytes, stm32f4xx_tim.o(.text.TIM_DeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = TIM_DeInit &rArr; RCC_APB1PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphResetCmd
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_Init
</UL>

<P><STRONG><a name="[14c]"></a>TIM_GetITStatus</STRONG> (Thumb, 90 bytes, Stack size 12 bytes, stm32f4xx_tim.o(.text.TIM_GetITStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_IRQHandler
</UL>

<P><STRONG><a name="[152]"></a>TIM_ITConfig</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text.TIM_ITConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_Init
</UL>

<P><STRONG><a name="[150]"></a>TIM_TimeBaseInit</STRONG> (Thumb, 270 bytes, Stack size 12 bytes, stm32f4xx_tim.o(.text.TIM_TimeBaseInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_TimeBaseInit
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_Init
</UL>

<P><STRONG><a name="[10c]"></a>Tra_Mode</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ber_test.o(.text.Tra_Mode))
<BR><BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>

<P><STRONG><a name="[154]"></a>UART1_Init</STRONG> (Thumb, 206 bytes, Stack size 40 bytes, uart.o(.text.UART1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = UART1_Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearFlag
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[227]"></a>UART1_TestRecTimeOut</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, uart.o(.text.UART1_TestRecTimeOut))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[159]"></a>UART2_Init</STRONG> (Thumb, 206 bytes, Stack size 48 bytes, uart.o(.text.UART2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = UART2_Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearFlag
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15a]"></a>UART3_Init</STRONG> (Thumb, 208 bytes, Stack size 40 bytes, uart.o(.text.UART3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = UART3_Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearFlag
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[40]"></a>USART1_IRQHandler</STRONG> (Thumb, 1070 bytes, Stack size 24 bytes, gpio.o(.text.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = USART1_IRQHandler &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearFlag
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>USART2_IRQHandler</STRONG> (Thumb, 446 bytes, Stack size 8 bytes, gpio.o(.text.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = USART2_IRQHandler &rArr; KeyboardDataMove
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KeyboardDataMove
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>USART3_IRQHandler</STRONG> (Thumb, 814 bytes, Stack size 16 bytes, gpio.o(.text.USART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = USART3_IRQHandler &rArr; USART_ClearITPendingBit
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[158]"></a>USART_ClearFlag</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, stm32f4xx_usart.o(.text.USART_ClearFlag))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART_ClearFlag
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_Init
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[15c]"></a>USART_ClearITPendingBit</STRONG> (Thumb, 68 bytes, Stack size 12 bytes, stm32f4xx_usart.o(.text.USART_ClearITPendingBit))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = USART_ClearITPendingBit
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[157]"></a>USART_Cmd</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, stm32f4xx_usart.o(.text.USART_Cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_Init
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[15b]"></a>USART_GetFlagStatus</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, stm32f4xx_usart.o(.text.USART_GetFlagStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_tx8p
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx8p
</UL>

<P><STRONG><a name="[15f]"></a>USART_GetITStatus</STRONG> (Thumb, 176 bytes, Stack size 24 bytes, stm32f4xx_usart.o(.text.USART_GetITStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[156]"></a>USART_ITConfig</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, stm32f4xx_usart.o(.text.USART_ITConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_Init
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[155]"></a>USART_Init</STRONG> (Thumb, 354 bytes, Stack size 48 bytes, stm32f4xx_usart.o(.text.USART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_Init
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
</UL>

<P><STRONG><a name="[15d]"></a>USART_ReceiveData</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, stm32f4xx_usart.o(.text.USART_ReceiveData))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = USART_ReceiveData
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[160]"></a>USART_SendData</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, stm32f4xx_usart.o(.text.USART_SendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART_SendData
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_tx8p
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx8p
</UL>

<P><STRONG><a name="[163]"></a>USB_00</STRONG> (Thumb, 212 bytes, Stack size 272 bytes, usb_interface.o(.text.USB_00))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = USB_00 &rArr; USB_tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_tx8p
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[165]"></a>USB_01</STRONG> (Thumb, 1656 bytes, Stack size 272 bytes, usb_interface.o(.text.USB_01))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = USB_01 &rArr; USB_tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_tx8p
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[166]"></a>USB_02</STRONG> (Thumb, 214 bytes, Stack size 272 bytes, usb_interface.o(.text.USB_02))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = USB_02 &rArr; USB_tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_tx8p
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[167]"></a>USB_03</STRONG> (Thumb, 214 bytes, Stack size 272 bytes, usb_interface.o(.text.USB_03))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = USB_03 &rArr; USB_tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_tx8p
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[168]"></a>USB_10</STRONG> (Thumb, 214 bytes, Stack size 272 bytes, usb_interface.o(.text.USB_10))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = USB_10 &rArr; USB_tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_tx8p
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[169]"></a>USB_20</STRONG> (Thumb, 1112 bytes, Stack size 288 bytes, usb_interface.o(.text.USB_20))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = USB_20 &rArr; USB_tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_tx8p
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[16a]"></a>USB_30</STRONG> (Thumb, 278 bytes, Stack size 280 bytes, usb_interface.o(.text.USB_30))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = USB_30 &rArr; USB_tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_tx8p
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[16b]"></a>USB_40</STRONG> (Thumb, 1112 bytes, Stack size 288 bytes, usb_interface.o(.text.USB_40))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = USB_40 &rArr; USB_tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_tx8p
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[16c]"></a>USB_Reso_00</STRONG> (Thumb, 1044 bytes, Stack size 64 bytes, usb_interface.o(.text.USB_Reso_00))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = USB_Reso_00 &rArr; VIEWTECH_71 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[16d]"></a>USB_Reso_02</STRONG> (Thumb, 328 bytes, Stack size 16 bytes, usb_interface.o(.text.USB_Reso_02))
<BR><BR>[Stack]<UL><LI>Max Depth = 816<LI>Call Chain = USB_Reso_02 &rArr; update_status &rArr; show_network_status &rArr; fix_crc_error &rArr; fix_network_config_error &rArr; VIEWTECH_98 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_status
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[16e]"></a>USB_Reso_10</STRONG> (Thumb, 360 bytes, Stack size 24 bytes, usb_interface.o(.text.USB_Reso_10))
<BR><BR>[Stack]<UL><LI>Max Depth = 824<LI>Call Chain = USB_Reso_10 &rArr; update_status &rArr; show_network_status &rArr; fix_crc_error &rArr; fix_network_config_error &rArr; VIEWTECH_98 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_status
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[226]"></a>USB_Reso_30</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, usb_interface.o(.text.USB_Reso_30))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[164]"></a>USB_tx8p</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, usb_interface.o(.text.USB_tx8p))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USB_tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
</UL>
<BR>[Called By]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_03
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_30
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_40
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_20
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_02
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_00
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_10
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_01
</UL>

<P><STRONG><a name="[16]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[f9]"></a>User_defined_pattern</STRONG> (Thumb, 982 bytes, Stack size 120 bytes, lcd_interface.o(.text.User_defined_pattern))
<BR><BR>[Stack]<UL><LI>Max Depth = 536<LI>Call Chain = User_defined_pattern &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_A01
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_UDP
</UL>

<P><STRONG><a name="[16f]"></a>VIEWTECH_40</STRONG> (Thumb, 136 bytes, Stack size 72 bytes, print.o(.text.VIEWTECH_40))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx8p
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_98
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_A01
</UL>

<P><STRONG><a name="[d3]"></a>VIEWTECH_70</STRONG> (Thumb, 158 bytes, Stack size 272 bytes, print.o(.text.VIEWTECH_70))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = VIEWTECH_70 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx8p
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_OtherRate
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_id
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_UDEFT
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_UDP
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Rate
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_More
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Timer
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Pattern
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_status
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_RTC
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[a5]"></a>VIEWTECH_71</STRONG> (Thumb, 442 bytes, Stack size 288 bytes, print.o(.text.VIEWTECH_71))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = VIEWTECH_71 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx8p
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Auto_Mode
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_OtherRate
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_EQSet
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_EQDisValue
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_UDEFT
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_UDP
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Rate
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_More
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Timer
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Pattern
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_status
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_RTC
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_Reso_00
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XY_touch
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ef]"></a>VIEWTECH_7C01</STRONG> (Thumb, 254 bytes, Stack size 272 bytes, print.o(.text.VIEWTECH_7C01))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = VIEWTECH_7C01 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx8p
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_UDEFT
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_UDP
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_More
</UL>

<P><STRONG><a name="[9e]"></a>VIEWTECH_98</STRONG> (Thumb, 364 bytes, Stack size 296 bytes, print.o(.text.VIEWTECH_98))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = VIEWTECH_98 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_40
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx8p
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Auto_Mode
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fix_network_config_error
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fix_crc_error
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_id
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_UDEFT
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_UDP
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;show_network_status
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_gang
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_More
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_RTC
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByte
</UL>

<P><STRONG><a name="[f4]"></a>VIEWTECH_9B5A</STRONG> (Thumb, 104 bytes, Stack size 272 bytes, print.o(.text.VIEWTECH_9B5A))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = VIEWTECH_9B5A &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx8p
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_RTC
</UL>

<P><STRONG><a name="[a3]"></a>VIEWTECH_A01</STRONG> (Thumb, 938 bytes, Stack size 320 bytes, print.o(.text.VIEWTECH_A01))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_40
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx8p
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si570_WriteOneByte
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Auto_Mode
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_amp_config
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_defined_pattern
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_EQSet
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_EQDisValue
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_id
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_UDEFT
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_UDP
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_status
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_RTC
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_WriteOneByte
</UL>

<P><STRONG><a name="[172]"></a>VIEWTECH_E0</STRONG> (Thumb, 170 bytes, Stack size 272 bytes, print.o(.text.VIEWTECH_E0))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = VIEWTECH_E0 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx8p
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f5]"></a>VIEWTECH_E7</STRONG> (Thumb, 294 bytes, Stack size 280 bytes, print.o(.text.VIEWTECH_E7))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = VIEWTECH_E7 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx8p
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEC2BCD
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_RTC
</UL>

<P><STRONG><a name="[177]"></a>WIZCHIP_READ</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, w5500.o(.text.WIZCHIP_READ))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = WIZCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_getphylink
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_network_connectivity
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_gettimeout
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_getnetmode
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_setnetmode
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_getphypmode
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_setphypmode
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_getphyconf
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_reset
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_getinterruptmask
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_getinterrupt
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_sw_reset
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlwizchip
</UL>

<P><STRONG><a name="[237]"></a>WIZCHIP_READ_BUF</STRONG> (Thumb, 150 bytes, Stack size 32 bytes, w5500.o(.text.WIZCHIP_READ_BUF))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = WIZCHIP_READ_BUF
</UL>
<BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_getnetinfo
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_sw_reset
</UL>

<P><STRONG><a name="[176]"></a>WIZCHIP_WRITE</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, w5500.o(.text.WIZCHIP_WRITE))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = WIZCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_settimeout
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_setnetmode
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_setphypmode
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_setphyconf
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_reset
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_setinterruptmask
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_clrinterrupt
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_init
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_sw_reset
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlwizchip
</UL>

<P><STRONG><a name="[238]"></a>WIZCHIP_WRITE_BUF</STRONG> (Thumb, 158 bytes, Stack size 32 bytes, w5500.o(.text.WIZCHIP_WRITE_BUF))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = WIZCHIP_WRITE_BUF
</UL>
<BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_setnetinfo
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_sw_reset
</UL>

<P><STRONG><a name="[174]"></a>XY_touch</STRONG> (Thumb, 4918 bytes, Stack size 56 bytes, lcd_interface.o(.text.XY_touch))
<BR><BR>[Stack]<UL><LI>Max Depth = 1616<LI>Call Chain = XY_touch &rArr; Menu_Pattern &rArr; Menu_UDP &rArr; User_defined_pattern &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Rate
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_More
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Timer
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Pattern
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_status
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_RTC
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[22a]"></a>calculate_crc32</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, serial_net_config.o(.text.calculate_crc32))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = calculate_crc32
</UL>
<BR>[Called By]<UL><LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;verify_config_integrity
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_network_config
</UL>

<P><STRONG><a name="[175]"></a>close</STRONG> (Thumb, 186 bytes, Stack size 16 bytes, socket.o(.text.close))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = close &rArr; WIZCHIP_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_network_connectivity
</UL>

<P><STRONG><a name="[178]"></a>cs4224_adj_mseq</STRONG> (Thumb, 384 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_adj_mseq))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = cs4224_adj_mseq &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hw_id
</UL>
<BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>

<P><STRONG><a name="[179]"></a>cs4224_adj_pp</STRONG> (Thumb, 384 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_adj_pp))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = cs4224_adj_pp &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hw_id
</UL>
<BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_scratch_regs
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>

<P><STRONG><a name="[17a]"></a>cs4224_apply_post_ucode_dwld_workarounds</STRONG> (Thumb, 136 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_apply_post_ucode_dwld_workarounds))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = cs4224_apply_post_ucode_dwld_workarounds &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_manage_ucode_download
</UL>

<P><STRONG><a name="[17e]"></a>cs4224_apply_workarounds</STRONG> (Thumb, 718 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_apply_workarounds))
<BR><BR>[Stack]<UL><LI>Max Depth = 212<LI>Call Chain = cs4224_apply_workarounds &rArr; cs4224_enable_monitor_sense_points &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_monitor_sense_points
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_manage_ucode_download
</UL>

<P><STRONG><a name="[180]"></a>cs4224_apply_workarounds_power_down</STRONG> (Thumb, 366 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_apply_workarounds_power_down))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = cs4224_apply_workarounds_power_down &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_manage_ucode_download
</UL>

<P><STRONG><a name="[1f1]"></a>cs4224_calc_crc16</STRONG> (Thumb, 200 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_calc_crc16))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = cs4224_calc_crc16
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_ucode_data_prgm_image_broadcast
</UL>

<P><STRONG><a name="[181]"></a>cs4224_check_rules</STRONG> (Thumb, 1560 bytes, Stack size 80 bytes, cs4224_api.o(.text.cs4224_check_rules))
<BR><BR>[Stack]<UL><LI>Max Depth = 268<LI>Call Chain = cs4224_check_rules &rArr; cs4224_is_hw_simplex &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dcmpge
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dcmpgt
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_check_rates
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[186]"></a>cs4224_clear_mailbox</STRONG> (Thumb, 200 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_clear_mailbox))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = cs4224_clear_mailbox &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_mseq_id
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[189]"></a>cs4224_config_polarity_inv_points</STRONG> (Thumb, 576 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_config_polarity_inv_points))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = cs4224_config_polarity_inv_points &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_polarity_inv
</UL>

<P><STRONG><a name="[18c]"></a>cs4224_config_target_application</STRONG> (Thumb, 138 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_config_target_application))
<BR><BR>[Stack]<UL><LI>Max Depth = 316<LI>Call Chain = cs4224_config_target_application &rArr; cs4224_config_target_application_intf &rArr; cs4224_init_sub_rate_fc &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_target_application_intf
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[18d]"></a>cs4224_config_target_application_intf</STRONG> (Thumb, 246 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_config_target_application_intf))
<BR><BR>[Stack]<UL><LI>Max Depth = 292<LI>Call Chain = cs4224_config_target_application_intf &rArr; cs4224_init_sub_rate_fc &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_sub_rate_fc
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_8g_fc
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_15g
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_10g
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_8g
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_5g
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_rules_set_rate
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_cfg_side
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_target_application
</UL>

<P><STRONG><a name="[1ef]"></a>cs4224_debug_dump_fcan_setup</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, cs4224_api.o(.text.cs4224_debug_dump_fcan_setup))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = cs4224_debug_dump_fcan_setup
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[196]"></a>cs4224_demux_enable</STRONG> (Thumb, 302 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_demux_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = cs4224_demux_enable &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_fec
</UL>

<P><STRONG><a name="[197]"></a>cs4224_diags_clear_interrupts</STRONG> (Thumb, 246 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_diags_clear_interrupts))
<BR><BR>[Stack]<UL><LI>Max Depth = 244<LI>Call Chain = cs4224_diags_clear_interrupts &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_kran_wait_for_an
</UL>

<P><STRONG><a name="[198]"></a>cs4224_diags_fix_ptrn_generator_cfg</STRONG> (Thumb, 410 bytes, Stack size 48 bytes, cs4224_api.o(.text.cs4224_diags_fix_ptrn_generator_cfg))
<BR><BR>[Stack]<UL><LI>Max Depth = 356<LI>Call Chain = cs4224_diags_fix_ptrn_generator_cfg &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_simplex_get_generator
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_enable_power_savings
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_fixpattern_generator
</UL>

<P><STRONG><a name="[108]"></a>cs4224_diags_fix_ptrn_generator_enable</STRONG> (Thumb, 384 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_diags_fix_ptrn_generator_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 308<LI>Call Chain = cs4224_diags_fix_ptrn_generator_enable &rArr; cs4224_diags_prbs_set_bitswap &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_set_bitswap
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_simplex_get_generator
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_fixpattern_checker
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>

<P><STRONG><a name="[c]"></a>cs4224_diags_format_dividers</STRONG> (Thumb, 560 bytes, Stack size 64 bytes, cs4224_api.o(.text.cs4224_diags_format_dividers))
<BR><BR>[Stack]<UL><LI>Max Depth = 292<LI>Call Chain = cs4224_diags_format_dividers &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cs4224_api.o(.data.g_edc_fields)
</UL>
<P><STRONG><a name="[b]"></a>cs4224_diags_format_edc_mode</STRONG> (Thumb, 368 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_diags_format_edc_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 308<LI>Call Chain = cs4224_diags_format_edc_mode &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_edc_mode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cs4224_api.o(.data.g_edc_fields)
</UL>
<P><STRONG><a name="[d]"></a>cs4224_diags_format_fracn</STRONG> (Thumb, 144 bytes, Stack size 48 bytes, cs4224_api.o(.text.cs4224_diags_format_fracn))
<BR><BR>[Stack]<UL><LI>Max Depth = 276<LI>Call Chain = cs4224_diags_format_fracn &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cs4224_api.o(.data.g_edc_fields)
</UL>
<P><STRONG><a name="[10]"></a>cs4224_diags_format_pcs_status</STRONG> (Thumb, 668 bytes, Stack size 88 bytes, cs4224_api.o(.text.cs4224_diags_format_pcs_status))
<BR><BR>[Stack]<UL><LI>Max Depth = 316<LI>Call Chain = cs4224_diags_format_pcs_status &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cs4224_api.o(.data.g_edc_fields)
</UL>
<P><STRONG><a name="[e]"></a>cs4224_diags_format_polarity_inversion</STRONG> (Thumb, 270 bytes, Stack size 48 bytes, cs4224_api.o(.text.cs4224_diags_format_polarity_inversion))
<BR><BR>[Stack]<UL><LI>Max Depth = 276<LI>Call Chain = cs4224_diags_format_polarity_inversion &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_STRLEN
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cs4224_api.o(.data.g_edc_fields)
</UL>
<P><STRONG><a name="[f]"></a>cs4224_diags_format_reset_count</STRONG> (Thumb, 112 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_diags_format_reset_count))
<BR><BR>[Stack]<UL><LI>Max Depth = 268<LI>Call Chain = cs4224_diags_format_reset_count &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cs4224_api.o(.data.g_edc_fields)
</UL>
<P><STRONG><a name="[a]"></a>cs4224_diags_format_rxlock</STRONG> (Thumb, 104 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_diags_format_rxlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = cs4224_diags_format_rxlock &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cs4224_api.o(.data.g_edc_fields)
</UL>
<P><STRONG><a name="[6]"></a>cs4224_diags_format_sku</STRONG> (Thumb, 216 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_diags_format_sku))
<BR><BR>[Stack]<UL><LI>Max Depth = 268<LI>Call Chain = cs4224_diags_format_sku &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cs4224_api.o(.data.g_edc_fields)
</UL>
<P><STRONG><a name="[7]"></a>cs4224_diags_format_temperature</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_diags_format_temperature))
<BR><BR>[Stack]<UL><LI>Max Depth = 308<LI>Call Chain = cs4224_diags_format_temperature &rArr; cs4224_mon_temp_read_fixp &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mon_temp_read_fixp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cs4224_api.o(.data.g_edc_fields)
</UL>
<P><STRONG><a name="[9]"></a>cs4224_diags_format_voltage_0p9</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_diags_format_voltage_0p9))
<BR><BR>[Stack]<UL><LI>Max Depth = 308<LI>Call Chain = cs4224_diags_format_voltage_0p9 &rArr; cs4224_mon_volt_read_fixp &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mon_volt_read_fixp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cs4224_api.o(.data.g_edc_fields)
</UL>
<P><STRONG><a name="[8]"></a>cs4224_diags_format_voltage_1p8</STRONG> (Thumb, 50 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_diags_format_voltage_1p8))
<BR><BR>[Stack]<UL><LI>Max Depth = 308<LI>Call Chain = cs4224_diags_format_voltage_1p8 &rArr; cs4224_mon_volt_read_fixp &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mon_volt_read_fixp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cs4224_api.o(.data.g_edc_fields)
</UL>
<P><STRONG><a name="[1a1]"></a>cs4224_diags_prbs_checker_config</STRONG> (Thumb, 314 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_diags_prbs_checker_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 300<LI>Call Chain = cs4224_diags_prbs_checker_config &rArr; cs4224_diags_prbs_set_bitswap &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_set_bitswap
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_simplex_get_checker
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_fixpattern_checker
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs58_checker
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs31_checker
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs23_checker
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs15_checker
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs9_5_checker
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs9_checker
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs7_checker
</UL>

<P><STRONG><a name="[10b]"></a>cs4224_diags_prbs_checker_enable</STRONG> (Thumb, 384 bytes, Stack size 48 bytes, cs4224_api.o(.text.cs4224_diags_prbs_checker_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 356<LI>Call Chain = cs4224_diags_prbs_checker_enable &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_get_errors
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_set_bitswap
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_simplex_get_checker
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_enable_power_savings
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_fixpattern_checker
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs58_checker
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs31_checker
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs23_checker
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs15_checker
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs9_5_checker
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs9_checker
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs7_checker
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>

<P><STRONG><a name="[a6]"></a>cs4224_diags_prbs_checker_get_errors</STRONG> (Thumb, 212 bytes, Stack size 48 bytes, cs4224_api.o(.text.cs4224_diags_prbs_checker_get_errors))
<BR><BR>[Stack]<UL><LI>Max Depth = 276<LI>Call Chain = cs4224_diags_prbs_checker_get_errors &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_simplex_get_checker
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEC_Read
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_fixpattern_checker
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs58_checker
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs31_checker
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs23_checker
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs15_checker
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs9_5_checker
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs9_checker
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs7_checker
</UL>

<P><STRONG><a name="[1a3]"></a>cs4224_diags_prbs_generator_config</STRONG> (Thumb, 334 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_diags_prbs_generator_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 348<LI>Call Chain = cs4224_diags_prbs_generator_config &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_simplex_get_generator
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_enable_power_savings
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs58_generator
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs31_generator
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs23_generator
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs15_generator
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs9_5_generator
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs9_generator
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs7_generator
</UL>

<P><STRONG><a name="[107]"></a>cs4224_diags_prbs_generator_enable</STRONG> (Thumb, 384 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_diags_prbs_generator_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 308<LI>Call Chain = cs4224_diags_prbs_generator_enable &rArr; cs4224_diags_prbs_set_bitswap &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_set_bitswap
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_simplex_get_generator
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>

<P><STRONG><a name="[1a4]"></a>cs4224_diags_prbs_generator_set_pfd_mode</STRONG> (Thumb, 418 bytes, Stack size 48 bytes, cs4224_api.o(.text.cs4224_diags_prbs_generator_set_pfd_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 356<LI>Call Chain = cs4224_diags_prbs_generator_set_pfd_mode &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_MDELAY
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_simplex_get_generator
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_enable_power_savings
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_stall
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_fixpattern_generator
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs58_generator
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs31_generator
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs23_generator
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs15_generator
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs9_5_generator
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs9_generator
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs7_generator
</UL>

<P><STRONG><a name="[109]"></a>cs4224_diags_prbs_generator_squelch</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_diags_prbs_generator_squelch))
<BR><BR>[Stack]<UL><LI>Max Depth = 292<LI>Call Chain = cs4224_diags_prbs_generator_squelch &rArr; cs4224_squelch_driver &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_squelch_driver
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_simplex_get_generator
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>

<P><STRONG><a name="[19b]"></a>cs4224_diags_prbs_set_bitswap</STRONG> (Thumb, 286 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_diags_prbs_set_bitswap))
<BR><BR>[Stack]<UL><LI>Max Depth = 268<LI>Call Chain = cs4224_diags_prbs_set_bitswap &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_enable
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_config
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_enable
</UL>

<P><STRONG><a name="[1a2]"></a>cs4224_diags_prbs_simplex_get_checker</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, cs4224_api.o(.text.cs4224_diags_prbs_simplex_get_checker))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = cs4224_diags_prbs_simplex_get_checker &rArr; cs4224_line_rx_to_host_tx_dir &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_get_errors
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_config
</UL>

<P><STRONG><a name="[199]"></a>cs4224_diags_prbs_simplex_get_generator</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, cs4224_api.o(.text.cs4224_diags_prbs_simplex_get_generator))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = cs4224_diags_prbs_simplex_get_generator &rArr; cs4224_line_rx_to_host_tx_dir &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_squelch
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_enable
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_cfg
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_set_pfd_mode
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_enable
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_config
</UL>

<P><STRONG><a name="[1e6]"></a>cs4224_diags_register_can_read</STRONG> (Thumb, 86 bytes, Stack size 4 bytes, cs4224_api.o(.text.cs4224_diags_register_can_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = cs4224_diags_register_can_read
</UL>
<BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
</UL>

<P><STRONG><a name="[1a7]"></a>cs4224_diags_reset_static_state</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_diags_reset_static_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = cs4224_diags_reset_static_state &rArr; cs4224_is_hw_duplex &rArr; cs4224_is_hw_simplex &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reset_static_state_for_slice
</UL>

<P><STRONG><a name="[1a8]"></a>cs4224_diags_set_phsel_mseq</STRONG> (Thumb, 566 bytes, Stack size 64 bytes, cs4224_api.o(.text.cs4224_diags_set_phsel_mseq))
<BR><BR>[Stack]<UL><LI>Max Depth = 332<LI>Call Chain = cs4224_diags_set_phsel_mseq &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_get_addr_offset
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_edc_mode
</UL>
<BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_edc_mode_intf
</UL>

<P><STRONG><a name="[1aa]"></a>cs4224_disable_tx_driver_if_req</STRONG> (Thumb, 262 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_disable_tx_driver_if_req))
<BR><BR>[Stack]<UL><LI>Max Depth = 244<LI>Call Chain = cs4224_disable_tx_driver_if_req &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1ab]"></a>cs4224_dump_debug_info</STRONG> (Thumb, 222 bytes, Stack size 64 bytes, cs4224_api.o(.text.cs4224_dump_debug_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = cs4224_dump_debug_info &rArr; cs4224_is_hw_simplex &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_translate_edc_mode
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_translate_app_mode
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_data_rate
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hw_id
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1af]"></a>cs4224_enable_fec</STRONG> (Thumb, 548 bytes, Stack size 72 bytes, cs4224_api.o(.text.cs4224_enable_fec))
<BR><BR>[Stack]<UL><LI>Max Depth = 380<LI>Call Chain = cs4224_enable_fec &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_demux_enable
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mux_enable
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_simplex_mate_slice
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_enable_power_savings
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[17f]"></a>cs4224_enable_monitor_sense_points</STRONG> (Thumb, 192 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_enable_monitor_sense_points))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = cs4224_enable_monitor_sense_points &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_MDELAY
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_apply_workarounds
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mon_volt_read_fixp
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mon_temp_read_fixp
</UL>

<P><STRONG><a name="[1b2]"></a>cs4224_enable_polarity_inv</STRONG> (Thumb, 346 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_enable_polarity_inv))
<BR><BR>[Stack]<UL><LI>Max Depth = 292<LI>Call Chain = cs4224_enable_polarity_inv &rArr; cs4224_config_polarity_inv_points &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_polarity_inv_points
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[183]"></a>cs4224_fcan_check_rates</STRONG> (Thumb, 142 bytes, Stack size 12 bytes, cs4224_api.o(.text.cs4224_fcan_check_rates))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = cs4224_fcan_check_rates
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_check_rules
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_wait_for_an
</UL>

<P><STRONG><a name="[1b3]"></a>cs4224_fcan_init_fc_post_an</STRONG> (Thumb, 502 bytes, Stack size 56 bytes, cs4224_api.o(.text.cs4224_fcan_init_fc_post_an))
<BR><BR>[Stack]<UL><LI>Max Depth = 292<LI>Call Chain = cs4224_fcan_init_fc_post_an &rArr; cs4224_fcan_set_host_sr &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_set_host_sr
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1b5]"></a>cs4224_fcan_init_fc_pre_an</STRONG> (Thumb, 2796 bytes, Stack size 200 bytes, cs4224_api.o(.text.cs4224_fcan_init_fc_pre_an))
<BR><BR>[Stack]<UL><LI>Max Depth = 556<LI>Call Chain = cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_MDELAY
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_vco
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_ro_vco_tmp_thresh
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_lc_vco_tmp_thresh
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_start_an
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_rate
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1b6]"></a>cs4224_fcan_init_rate</STRONG> (Thumb, 4238 bytes, Stack size 104 bytes, cs4224_api.o(.text.cs4224_fcan_init_rate))
<BR><BR>[Stack]<UL><LI>Max Depth = 356<LI>Call Chain = cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_save_edc_mode
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_fc_pre_an
</UL>

<P><STRONG><a name="[195]"></a>cs4224_fcan_rules_set_rate</STRONG> (Thumb, 432 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_fcan_rules_set_rate))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = cs4224_fcan_rules_set_rate &rArr; __aeabi_dcmpeq
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dcmpeq
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_target_application_intf
</UL>

<P><STRONG><a name="[1b4]"></a>cs4224_fcan_set_host_sr</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, cs4224_api.o(.text.cs4224_fcan_set_host_sr))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = cs4224_fcan_set_host_sr &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_fc_post_an
</UL>

<P><STRONG><a name="[1ba]"></a>cs4224_fcan_start_an</STRONG> (Thumb, 170 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_fcan_start_an))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = cs4224_fcan_start_an &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_UDELAY
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_fc_pre_an
</UL>

<P><STRONG><a name="[1bd]"></a>cs4224_fcan_wait_for_an</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_fcan_wait_for_an))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = cs4224_fcan_wait_for_an &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_MDELAY
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_check_rates
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1be]"></a>cs4224_force_low_lc_vco</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_force_low_lc_vco))
<BR><BR>[Stack]<UL><LI>Max Depth = 268<LI>Call Chain = cs4224_force_low_lc_vco &rArr; cs4224_force_low_lc_vco_intf &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_force_low_lc_vco_intf
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_alt_coarse_tuning_intf
</UL>

<P><STRONG><a name="[1bf]"></a>cs4224_force_low_lc_vco_intf</STRONG> (Thumb, 120 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_force_low_lc_vco_intf))
<BR><BR>[Stack]<UL><LI>Max Depth = 244<LI>Call Chain = cs4224_force_low_lc_vco_intf &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_force_low_lc_vco
</UL>

<P><STRONG><a name="[1c0]"></a>cs4224_fracdiv_cdr_init</STRONG> (Thumb, 1036 bytes, Stack size 64 bytes, cs4224_api.o(.text.cs4224_fracdiv_cdr_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 340<LI>Call Chain = cs4224_fracdiv_cdr_init &rArr; cs4224_mseq_stall &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_cfg_side
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_stall
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_cdr_fracn
</UL>

<P><STRONG><a name="[1c1]"></a>cs4224_fracdiv_core_init</STRONG> (Thumb, 962 bytes, Stack size 64 bytes, cs4224_api.o(.text.cs4224_fracdiv_core_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 340<LI>Call Chain = cs4224_fracdiv_core_init &rArr; cs4224_mseq_stall &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_stall
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_synce
</UL>

<P><STRONG><a name="[18e]"></a>cs4224_get_cfg_side</STRONG> (Thumb, 86 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_get_cfg_side))
<BR><BR>[Stack]<UL><LI>Max Depth = 212<LI>Call Chain = cs4224_get_cfg_side &rArr; cs4224_line_rx_to_host_tx_dir &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fracdiv_cdr_init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_clkdiv_ctrl_intf
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_alt_coarse_tuning_intf
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_target_application_intf
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_pp_clock_monitor
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_squelch_ctrl
</UL>

<P><STRONG><a name="[1c2]"></a>cs4224_get_die_from_slice</STRONG> (Thumb, 150 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_get_die_from_slice))
<BR><BR>[Stack]<UL><LI>Max Depth = 196<LI>Call Chain = cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hw_id
</UL>
<BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_ucode_data_prgm_image
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_manage_ucode_download
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_scratch_regs
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mon_volt_read_fixp
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mon_temp_read_fixp
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_pgm_reg_from_efuse
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>

<P><STRONG><a name="[187]"></a>cs4224_get_mseq_id</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_get_mseq_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 212<LI>Call Chain = cs4224_get_mseq_id &rArr; cs4224_line_rx_to_host_tx_dir &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_clear_mailbox
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_mseq_power_savings
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_enable_power_savings
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_mseq_is_stalled
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_stall
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_edc_mode
</UL>

<P><STRONG><a name="[1d9]"></a>cs4224_gpio_cfg_output</STRONG> (Thumb, 100 bytes, Stack size 12 bytes, cs4224_api.o(.text.cs4224_gpio_cfg_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = cs4224_gpio_cfg_output
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_synce
</UL>

<P><STRONG><a name="[1c3]"></a>cs4224_gpio_get_signal_source_port</STRONG> (Thumb, 328 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_gpio_get_signal_source_port))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = cs4224_gpio_get_signal_source_port &rArr; cs4224_is_hw_duplex &rArr; cs4224_is_hw_simplex &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_gpio_init
</UL>

<P><STRONG><a name="[1c4]"></a>cs4224_gpio_init</STRONG> (Thumb, 646 bytes, Stack size 56 bytes, cs4224_api.o(.text.cs4224_gpio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 284<LI>Call Chain = cs4224_gpio_init &rArr; cs4224_gpio_get_signal_source_port &rArr; cs4224_is_hw_duplex &rArr; cs4224_is_hw_simplex &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_gpio_get_signal_source_port
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_synce
</UL>

<P><STRONG><a name="[12d]"></a>cs4224_hard_reset</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_hard_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 364<LI>Call Chain = cs4224_hard_reset &rArr; cs4224_hard_reset_die &rArr; cs4224_reset_die_static_state &rArr; cs4224_reset_static_state_for_slice &rArr; cs4224_diags_reset_static_state &rArr; cs4224_is_hw_duplex &rArr; cs4224_is_hw_simplex &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset_die
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS4224_MAX_NUM_DIES
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri300
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri9
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri6
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri3
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri622
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri155
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_XGSpon
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_XGpon
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpon
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Gpon
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Epon
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_otu2f
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_otu2e
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_otu2
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_5ginfi
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_10ginfi
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc2g
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc4g
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc8g
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc10g
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc16g
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_12g
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_oc48
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_oc96
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_xaui
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_rxaui
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_1g
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_10g
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_15g
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_10gwan
</UL>

<P><STRONG><a name="[1c5]"></a>cs4224_hard_reset_die</STRONG> (Thumb, 548 bytes, Stack size 72 bytes, cs4224_api.o(.text.cs4224_hard_reset_die))
<BR><BR>[Stack]<UL><LI>Max Depth = 340<LI>Call Chain = cs4224_hard_reset_die &rArr; cs4224_reset_die_static_state &rArr; cs4224_reset_static_state_for_slice &rArr; cs4224_diags_reset_static_state &rArr; cs4224_is_hw_duplex &rArr; cs4224_is_hw_simplex &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_wait_for_eeprom_finished
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reset_die_static_state
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset
</UL>

<P><STRONG><a name="[aa]"></a>cs4224_hw_id</STRONG> (Thumb, 544 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_hw_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_dump_debug_info
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_simplex_mate_slice
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS4224_MAX_NUM_DIES
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS4224_MAX_NUM_SLICES
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_die_from_slice
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_adj_mseq
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_adj_pp
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>

<P><STRONG><a name="[191]"></a>cs4224_init_10g</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_init_10g))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = cs4224_init_10g &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_target_application_intf
</UL>

<P><STRONG><a name="[192]"></a>cs4224_init_15g</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_init_15g))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = cs4224_init_15g &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_target_application_intf
</UL>

<P><STRONG><a name="[18f]"></a>cs4224_init_5g</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_init_5g))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = cs4224_init_5g &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_target_application_intf
</UL>

<P><STRONG><a name="[190]"></a>cs4224_init_8g</STRONG> (Thumb, 184 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_init_8g))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = cs4224_init_8g &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_target_application_intf
</UL>

<P><STRONG><a name="[193]"></a>cs4224_init_8g_fc</STRONG> (Thumb, 184 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_init_8g_fc))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = cs4224_init_8g_fc &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_target_application_intf
</UL>

<P><STRONG><a name="[1c8]"></a>cs4224_init_ac_decoupling_caps</STRONG> (Thumb, 246 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_init_ac_decoupling_caps))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = cs4224_init_ac_decoupling_caps &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1c9]"></a>cs4224_init_alt_coarse_tuning</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_init_alt_coarse_tuning))
<BR><BR>[Stack]<UL><LI>Max Depth = 332<LI>Call Chain = cs4224_init_alt_coarse_tuning &rArr; cs4224_init_alt_coarse_tuning_intf &rArr; cs4224_force_low_lc_vco &rArr; cs4224_force_low_lc_vco_intf &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_alt_coarse_tuning_intf
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1ca]"></a>cs4224_init_alt_coarse_tuning_intf</STRONG> (Thumb, 350 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_init_alt_coarse_tuning_intf))
<BR><BR>[Stack]<UL><LI>Max Depth = 308<LI>Call Chain = cs4224_init_alt_coarse_tuning_intf &rArr; cs4224_force_low_lc_vco &rArr; cs4224_force_low_lc_vco_intf &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_force_low_lc_vco
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_need_low_lc_vco
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_ro_vco_tmp_thresh
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_lc_vco_tmp_thresh
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_cfg_side
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_alt_coarse_tuning
</UL>

<P><STRONG><a name="[1cc]"></a>cs4224_init_api_version</STRONG> (Thumb, 86 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_init_api_version))
<BR><BR>[Stack]<UL><LI>Max Depth = 244<LI>Call Chain = cs4224_init_api_version &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1cd]"></a>cs4224_init_driver_settings</STRONG> (Thumb, 200 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_init_driver_settings))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = cs4224_init_driver_settings &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_amp_config
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_driver_trace_loss_intf
</UL>

<P><STRONG><a name="[1ce]"></a>cs4224_init_driver_trace_loss</STRONG> (Thumb, 148 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_init_driver_trace_loss))
<BR><BR>[Stack]<UL><LI>Max Depth = 316<LI>Call Chain = cs4224_init_driver_trace_loss &rArr; cs4224_init_driver_trace_loss_intf &rArr; cs4224_init_driver_settings &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_driver_trace_loss_intf
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1cf]"></a>cs4224_init_driver_trace_loss_intf</STRONG> (Thumb, 310 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_init_driver_trace_loss_intf))
<BR><BR>[Stack]<UL><LI>Max Depth = 292<LI>Call Chain = cs4224_init_driver_trace_loss_intf &rArr; cs4224_init_driver_settings &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_driver_settings
</UL>
<BR>[Called By]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_driver_trace_loss
</UL>

<P><STRONG><a name="[1d0]"></a>cs4224_init_edc_mode_intf</STRONG> (Thumb, 7418 bytes, Stack size 160 bytes, cs4224_api.o(.text.cs4224_init_edc_mode_intf))
<BR><BR>[Stack]<UL><LI>Max Depth = 492<LI>Call Chain = cs4224_init_edc_mode_intf &rArr; cs4224_diags_set_phsel_mseq &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_set_phsel_mseq
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_pgm_reg_from_efuse
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_pre_equalizer
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_save_edc_mode
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_edc_mode_set
</UL>

<P><STRONG><a name="[1d3]"></a>cs4224_init_edc_mode_set</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_init_edc_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 516<LI>Call Chain = cs4224_init_edc_mode_set &rArr; cs4224_init_edc_mode_intf &rArr; cs4224_diags_set_phsel_mseq &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_edc_mode_intf
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1b7]"></a>cs4224_init_lc_vco_tmp_thresh</STRONG> (Thumb, 320 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_init_lc_vco_tmp_thresh))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = cs4224_init_lc_vco_tmp_thresh &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_alt_coarse_tuning_intf
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_fc_pre_an
</UL>

<P><STRONG><a name="[1d4]"></a>cs4224_init_mseq_dyn_reconfig</STRONG> (Thumb, 136 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_init_mseq_dyn_reconfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 292<LI>Call Chain = cs4224_init_mseq_dyn_reconfig &rArr; cs4224_init_mseq_dyn_reconfig_intf &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_mseq_dyn_reconfig_intf
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1d5]"></a>cs4224_init_mseq_dyn_reconfig_intf</STRONG> (Thumb, 200 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_init_mseq_dyn_reconfig_intf))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = cs4224_init_mseq_dyn_reconfig_intf &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_mseq_dyn_reconfig
</UL>

<P><STRONG><a name="[1b8]"></a>cs4224_init_ro_vco_tmp_thresh</STRONG> (Thumb, 316 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_init_ro_vco_tmp_thresh))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = cs4224_init_ro_vco_tmp_thresh &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_alt_coarse_tuning_intf
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_fc_pre_an
</UL>

<P><STRONG><a name="[194]"></a>cs4224_init_sub_rate_fc</STRONG> (Thumb, 128 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_init_sub_rate_fc))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = cs4224_init_sub_rate_fc &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_target_application_intf
</UL>

<P><STRONG><a name="[1d6]"></a>cs4224_init_synce</STRONG> (Thumb, 176 bytes, Stack size 64 bytes, cs4224_api.o(.text.cs4224_init_synce))
<BR><BR>[Stack]<UL><LI>Max Depth = 404<LI>Call Chain = cs4224_init_synce &rArr; cs4224_fracdiv_core_init &rArr; cs4224_mseq_stall &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_gpio_init
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_gpio_cfg_output
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fracdiv_core_init
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_pp_clock_monitor
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_pp_clk_mon_cfg_init
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1b9]"></a>cs4224_init_vco</STRONG> (Thumb, 234 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_init_vco))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = cs4224_init_vco &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_UDELAY
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_fc_pre_an
</UL>

<P><STRONG><a name="[1da]"></a>cs4224_is_eeprom_finished</STRONG> (Thumb, 190 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_is_eeprom_finished))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = cs4224_is_eeprom_finished &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_wait_for_eeprom_finished
</UL>

<P><STRONG><a name="[18a]"></a>cs4224_is_hw_duplex</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, cs4224_api.o(.text.cs4224_is_hw_duplex))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = cs4224_is_hw_duplex &rArr; cs4224_is_hw_simplex &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_restore_powered_down_regs
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_fec
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_polarity_inv
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_polarity_inv_points
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_cdr_fracn
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_clkdiv_ctrl
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_driver_trace_loss
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_alt_coarse_tuning
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_force_low_lc_vco
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_edc_mode_set
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_target_application
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_ac_decoupling_caps
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_gpio_get_signal_source_port
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_soft_reset
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_edc_mode_intf
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_reset_static_state
</UL>

<P><STRONG><a name="[182]"></a>cs4224_is_hw_simplex</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_is_hw_simplex))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = cs4224_is_hw_simplex &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hw_id
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_squelch
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_enable
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_cfg
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_set_pfd_mode
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_get_errors
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_config
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_enable
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_driver_settings
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_manage_ucode_download
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_check_rules
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_dump_debug_info
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_fec
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_driver_trace_loss
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_disable_tx_driver_if_req
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_edc_mode_set
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_ac_decoupling_caps
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_gpio_init
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fracdiv_core_init
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_get_addr_offset
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_clear_interrupts
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_squelch_driver
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_config
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_simplex_get_generator
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_simplex_get_checker
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_cfg_side
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_squelch_ctrl
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_edc_mode_intf
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_mseq_id
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>

<P><STRONG><a name="[1db]"></a>cs4224_kran_poll_an</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_kran_poll_an))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = cs4224_kran_poll_an &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_kran_wait_for_an
</UL>

<P><STRONG><a name="[79]"></a>cs4224_kran_wait_for_an</STRONG> (Thumb, 460 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_kran_wait_for_an))
<BR><BR>[Stack]<UL><LI>Max Depth = 292<LI>Call Chain = cs4224_kran_wait_for_an &rArr; cs4224_kran_poll_an &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_MDELAY
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_kran_poll_an
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_clear_interrupts
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cs4224_api.o(.text.cs4224_rules_set_default)
</UL>
<P><STRONG><a name="[18b]"></a>cs4224_line_rx_to_host_tx_dir</STRONG> (Thumb, 222 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_line_rx_to_host_tx_dir))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = cs4224_line_rx_to_host_tx_dir &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hw_id
</UL>
<BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_driver_settings
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_restore_powered_down_regs
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_fec
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_polarity_inv
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_polarity_inv_points
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_cdr_fracn
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_clkdiv_ctrl
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_driver_trace_loss
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_disable_tx_driver_if_req
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_alt_coarse_tuning
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_force_low_lc_vco
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_target_application
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_ac_decoupling_caps
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fracdiv_core_init
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_soft_reset
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_get_addr_offset
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_clear_interrupts
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_squelch_driver
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_simplex_get_generator
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_simplex_get_checker
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_cfg_side
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_edc_mode_intf
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_mseq_id
</UL>

<P><STRONG><a name="[17b]"></a>cs4224_lock</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, cs4224_api.o(.text.cs4224_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = cs4224_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_enable
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_cfg
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_get_errors
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_config
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_enable
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_driver_settings
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_ucode_data_prgm_image
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_ucode_data_prgm_image_broadcast
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_manage_ucode_download
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_scratch_regs
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_apply_post_ucode_dwld_workarounds
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_apply_workarounds
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_apply_workarounds_power_down
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fracdiv_cdr_init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_clkdiv_ctrl_intf
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_disable_tx_driver_if_req
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_alt_coarse_tuning_intf
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_sub_rate_fc
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_8g_fc
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_15g
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_10g
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_8g
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_5g
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_clear_mailbox
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset_die
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fracdiv_core_init
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_soft_reset_intf
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_demux_enable
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mux_enable
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_wait_for_an
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_vco
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_fc_pre_an
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_start_an
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_rate
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mon_volt_read_fixp
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mon_temp_read_fixp
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_config
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_set_bitswap
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_pgm_reg_from_efuse
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_edc_mode_intf
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_stall
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hw_id
</UL>

<P><STRONG><a name="[1dc]"></a>cs4224_manage_ucode_download</STRONG> (Thumb, 1136 bytes, Stack size 104 bytes, cs4224_api.o(.text.cs4224_manage_ucode_download))
<BR><BR>[Stack]<UL><LI>Max Depth = 380<LI>Call Chain = cs4224_manage_ucode_download &rArr; cs4224_mseq_stall &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_ucode_data_prgm_image
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_ucode_data_prgm_image_broadcast
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_apply_post_ucode_dwld_workarounds
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_apply_workarounds
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_apply_workarounds_power_down
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS4224_MAX_NUM_DIES
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS4224_MAX_NUM_SLICES
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_mseq_is_stalled
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_stall
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_die_from_slice
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[19f]"></a>cs4224_mon_temp_read_fixp</STRONG> (Thumb, 166 bytes, Stack size 48 bytes, cs4224_api.o(.text.cs4224_mon_temp_read_fixp))
<BR><BR>[Stack]<UL><LI>Max Depth = 276<LI>Call Chain = cs4224_mon_temp_read_fixp &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_monitor_sense_points
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_die_from_slice
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_temperature
</UL>

<P><STRONG><a name="[1a0]"></a>cs4224_mon_volt_read_fixp</STRONG> (Thumb, 276 bytes, Stack size 48 bytes, cs4224_api.o(.text.cs4224_mon_volt_read_fixp))
<BR><BR>[Stack]<UL><LI>Max Depth = 276<LI>Call Chain = cs4224_mon_volt_read_fixp &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_monitor_sense_points
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_die_from_slice
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_voltage_1p8
<LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_voltage_0p9
</UL>

<P><STRONG><a name="[19a]"></a>cs4224_mseq_enable_power_savings</STRONG> (Thumb, 532 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_mseq_enable_power_savings))
<BR><BR>[Stack]<UL><LI>Max Depth = 308<LI>Call Chain = cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_UDELAY
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_mseq_is_stalled
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_mseq_id
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_edc_mode
</UL>
<BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_cfg
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_set_pfd_mode
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_fec
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_soft_reset_intf
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_config
</UL>

<P><STRONG><a name="[1a9]"></a>cs4224_mseq_get_addr_offset</STRONG> (Thumb, 78 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_mseq_get_addr_offset))
<BR><BR>[Stack]<UL><LI>Max Depth = 212<LI>Call Chain = cs4224_mseq_get_addr_offset &rArr; cs4224_line_rx_to_host_tx_dir &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_set_phsel_mseq
</UL>

<P><STRONG><a name="[1e0]"></a>cs4224_mseq_squelch_ctrl</STRONG> (Thumb, 446 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_mseq_squelch_ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 316<LI>Call Chain = cs4224_mseq_squelch_ctrl &rArr; cs4224_mseq_stall &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_UDELAY
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_cfg_side
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_mseq_is_stalled
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_stall
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1a5]"></a>cs4224_mseq_stall</STRONG> (Thumb, 764 bytes, Stack size 48 bytes, cs4224_api.o(.text.cs4224_mseq_stall))
<BR><BR>[Stack]<UL><LI>Max Depth = 276<LI>Call Chain = cs4224_mseq_stall &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_UDELAY
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_stall_get_delay
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_mseq_id
</UL>
<BR>[Called By]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_set_pfd_mode
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_manage_ucode_download
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fracdiv_cdr_init
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fracdiv_core_init
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_soft_reset_intf
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_squelch_ctrl
</UL>

<P><STRONG><a name="[1e1]"></a>cs4224_mseq_stall_get_delay</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, cs4224_api.o(.text.cs4224_mseq_stall_get_delay))
<BR><BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_stall
</UL>

<P><STRONG><a name="[1b1]"></a>cs4224_mux_enable</STRONG> (Thumb, 168 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_mux_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = cs4224_mux_enable &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_fec
</UL>

<P><STRONG><a name="[1cb]"></a>cs4224_need_low_lc_vco</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_need_low_lc_vco))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = cs4224_need_low_lc_vco &rArr; cs4224_query_vco_rate
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_vco_rate
</UL>
<BR>[Called By]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_alt_coarse_tuning_intf
</UL>

<P><STRONG><a name="[1d2]"></a>cs4224_pgm_reg_from_efuse</STRONG> (Thumb, 924 bytes, Stack size 64 bytes, cs4224_api.o(.text.cs4224_pgm_reg_from_efuse))
<BR><BR>[Stack]<UL><LI>Max Depth = 292<LI>Call Chain = cs4224_pgm_reg_from_efuse &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_die_from_slice
</UL>
<BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_edc_mode_intf
</UL>

<P><STRONG><a name="[1d7]"></a>cs4224_pp_clk_mon_cfg_init</STRONG> (Thumb, 44 bytes, Stack size 4 bytes, cs4224_api.o(.text.cs4224_pp_clk_mon_cfg_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = cs4224_pp_clk_mon_cfg_init
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_synce
</UL>

<P><STRONG><a name="[1d8]"></a>cs4224_pp_clock_monitor</STRONG> (Thumb, 418 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_pp_clock_monitor))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = cs4224_pp_clock_monitor &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_cfg_side
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_synce
</UL>

<P><STRONG><a name="[1ad]"></a>cs4224_query_data_rate</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_query_data_rate))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = cs4224_query_data_rate &rArr; cs4224_query_vco_rate
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_vco_rate
</UL>
<BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_dump_debug_info
</UL>

<P><STRONG><a name="[19d]"></a>cs4224_query_edc_mode</STRONG> (Thumb, 370 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_query_edc_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 268<LI>Call Chain = cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_mseq_id
</UL>
<BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_edc_mode
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_set_phsel_mseq
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_enable_power_savings
</UL>

<P><STRONG><a name="[1df]"></a>cs4224_query_mseq_is_stalled</STRONG> (Thumb, 158 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_query_mseq_is_stalled))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = cs4224_query_mseq_is_stalled &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_mseq_id
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_manage_ucode_download
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_soft_reset_intf
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_enable_power_savings
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_squelch_ctrl
</UL>

<P><STRONG><a name="[1e3]"></a>cs4224_query_mseq_power_savings</STRONG> (Thumb, 140 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_query_mseq_power_savings))
<BR><BR>[Stack]<UL><LI>Max Depth = 268<LI>Call Chain = cs4224_query_mseq_power_savings &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_mseq_id
</UL>
<BR>[Called By]<UL><LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_soft_reset_intf
</UL>

<P><STRONG><a name="[1e2]"></a>cs4224_query_vco_rate</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_query_vco_rate))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = cs4224_query_vco_rate
</UL>
<BR>[Called By]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_need_low_lc_vco
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_data_rate
</UL>

<P><STRONG><a name="[9d]"></a>cs4224_reg_get</STRONG> (Thumb, 102 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_reg_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_read
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_read
</UL>
<BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Auto_Mode
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_ucode_data_prgm_image
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_ucode_data_prgm_image_broadcast
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_manage_ucode_download
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_apply_post_ucode_dwld_workarounds
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_apply_workarounds
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_apply_workarounds_power_down
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_eeprom_finished
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset_die
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_monitor_sense_points
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_pgm_reg_from_efuse
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hw_id
</UL>

<P><STRONG><a name="[13e]"></a>cs4224_reg_get_channel</STRONG> (Thumb, 182 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_reg_get_channel))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_die_from_slice
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_adj_mseq
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_adj_pp
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_register_can_read
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYNC
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_enable
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_cfg
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_set_pfd_mode
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_get_errors
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_config
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_enable
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_scratch_regs
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_fec
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_polarity_inv_points
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fracdiv_cdr_init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_clkdiv_ctrl_intf
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_mseq_dyn_reconfig
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_mseq_dyn_reconfig_intf
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_alt_coarse_tuning_intf
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_sub_rate_fc
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_8g_fc
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_8g
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_mseq_power_savings
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_kran_poll_an
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_kran_wait_for_an
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_ac_decoupling_caps
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fracdiv_core_init
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_soft_reset_intf
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_demux_enable
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mux_enable
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_fc_post_an
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_wait_for_an
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_vco
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_start_an
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_rate
<LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_pcs_status
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_rxlock
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_polarity_inversion
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_fracn
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_dividers
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_sku
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mon_volt_read_fixp
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_reset_count
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mon_temp_read_fixp
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_squelch_driver
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_config
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_set_bitswap
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_set_phsel_mseq
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_pgm_reg_from_efuse
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_enable_power_savings
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_squelch_ctrl
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_mseq_is_stalled
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_edc_mode_intf
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_stall
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_edc_mode
</UL>

<P><STRONG><a name="[17c]"></a>cs4224_reg_set</STRONG> (Thumb, 108 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_reg_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = cs4224_reg_set &rArr; mdio2_write &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio2_write
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mdio_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_ucode_data_prgm_image
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_ucode_data_prgm_image_broadcast
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_apply_post_ucode_dwld_workarounds
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_apply_workarounds
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_apply_workarounds_power_down
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset_die
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_monitor_sense_points
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>

<P><STRONG><a name="[188]"></a>cs4224_reg_set_channel</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_reg_set_channel))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_die_from_slice
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_adj_mseq
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_adj_pp
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_enable
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_cfg
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_set_pfd_mode
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_config
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_enable
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_driver_settings
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_scratch_regs
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_power_down
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_restore_powered_down_regs
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_fec
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_polarity_inv_points
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fracdiv_cdr_init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_clkdiv_ctrl_intf
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_mseq_dyn_reconfig_intf
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_disable_tx_driver_if_req
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_alt_coarse_tuning_intf
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_force_low_lc_vco_intf
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_sub_rate_fc
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_8g_fc
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_15g
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_10g
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_8g
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_5g
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_clear_mailbox
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_ac_decoupling_caps
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_gpio_init
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fracdiv_core_init
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_pp_clock_monitor
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_soft_reset_intf
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_demux_enable
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mux_enable
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_api_version
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_fc_post_an
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_set_host_sr
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_vco
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_ro_vco_tmp_thresh
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_lc_vco_tmp_thresh
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_fc_pre_an
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_start_an
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_rate
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_clear_interrupts
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_squelch_driver
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_config
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_set_bitswap
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_set_phsel_mseq
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_pgm_reg_from_efuse
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_pre_equalizer
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_save_edc_mode
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_enable_power_savings
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_squelch_ctrl
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_edc_mode_intf
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_stall
</UL>

<P><STRONG><a name="[1c7]"></a>cs4224_reset_die_static_state</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_reset_die_static_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 268<LI>Call Chain = cs4224_reset_die_static_state &rArr; cs4224_reset_static_state_for_slice &rArr; cs4224_diags_reset_static_state &rArr; cs4224_is_hw_duplex &rArr; cs4224_is_hw_simplex &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reset_static_state_for_slice
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS4224_MAX_NUM_SLICES
</UL>
<BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset_die
</UL>

<P><STRONG><a name="[1e9]"></a>cs4224_reset_static_state_for_slice</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, cs4224_api.o(.text.cs4224_reset_static_state_for_slice))
<BR><BR>[Stack]<UL><LI>Max Depth = 244<LI>Call Chain = cs4224_reset_static_state_for_slice &rArr; cs4224_diags_reset_static_state &rArr; cs4224_is_hw_duplex &rArr; cs4224_is_hw_simplex &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_reset_static_state
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reset_die_static_state
</UL>

<P><STRONG><a name="[1ea]"></a>cs4224_restore_powered_down_regs</STRONG> (Thumb, 404 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_restore_powered_down_regs))
<BR><BR>[Stack]<UL><LI>Max Depth = 244<LI>Call Chain = cs4224_restore_powered_down_regs &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[12e]"></a>cs4224_rules_set_default</STRONG> (Thumb, 1760 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_rules_set_default))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = cs4224_rules_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri300
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri9
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri6
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri3
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri622
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri155
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_XGSpon
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_XGpon
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpon
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Gpon
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Epon
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_otu2f
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_otu2e
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_otu2
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_5ginfi
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_10ginfi
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc2g
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc4g
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc8g
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc10g
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc16g
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_12g
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_oc48
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_oc96
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_xaui
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_rxaui
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_1g
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_10g
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_15g
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_10gwan
</UL>

<P><STRONG><a name="[1bb]"></a>cs4224_save_edc_mode</STRONG> (Thumb, 290 bytes, Stack size 32 bytes, cs4224_api.o(.text.cs4224_save_edc_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_rate
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_edc_mode_intf
</UL>

<P><STRONG><a name="[1b0]"></a>cs4224_simplex_mate_slice</STRONG> (Thumb, 202 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_simplex_mate_slice))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = cs4224_simplex_mate_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hw_id
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_fec
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_soft_reset_intf
</UL>

<P><STRONG><a name="[12f]"></a>cs4224_slice_enter_operational_state</STRONG> (Thumb, 1676 bytes, Stack size 56 bytes, cs4224_api.o(.text.cs4224_slice_enter_operational_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 612<LI>Call Chain = cs4224_slice_enter_operational_state &rArr; cs4224_fcan_init_fc_pre_an &rArr; cs4224_fcan_init_rate &rArr; cs4224_save_edc_mode &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_MDELAY
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_manage_ucode_download
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_check_rules
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_scratch_regs
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_dump_debug_info
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_restore_powered_down_regs
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_fec
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_enable_polarity_inv
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_cdr_fracn
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_clkdiv_ctrl
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_driver_trace_loss
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_mseq_dyn_reconfig
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_disable_tx_driver_if_req
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_alt_coarse_tuning
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_edc_mode_set
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_debug_dump_fcan_setup
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_config_target_application
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_clear_mailbox
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_eeprom_finished
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_ac_decoupling_caps
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_synce
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_soft_reset
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_api_version
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reset_static_state_for_slice
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_fc_post_an
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_wait_for_an
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_vco
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_fc_pre_an
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_enable_power_savings
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_squelch_ctrl
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_stall
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_die_from_slice
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri300
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri9
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri6
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri3
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri622
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri155
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_XGSpon
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_XGpon
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpon
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Gpon
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Epon
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_otu2f
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_otu2e
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_otu2
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_5ginfi
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_10ginfi
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc2g
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc4g
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc8g
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc10g
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc16g
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_12g
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_oc48
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_oc96
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_xaui
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_rxaui
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_1g
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_10g
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_15g
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_10gwan
</UL>

<P><STRONG><a name="[130]"></a>cs4224_slice_power_down</STRONG> (Thumb, 460 bytes, Stack size 16 bytes, cs4224_api.o(.text.cs4224_slice_power_down))
<BR><BR>[Stack]<UL><LI>Max Depth = 292<LI>Call Chain = cs4224_slice_power_down &rArr; cs4224_mseq_stall &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_stall
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri300
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri9
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri6
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri3
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri622
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpri155
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_XGSpon
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_XGpon
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Cpon
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Gpon
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_Epon
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_otu2f
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_otu2e
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_otu2
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_5ginfi
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_10ginfi
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc2g
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc4g
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc8g
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc10g
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_fc16g
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_12g
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_oc48
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_oc96
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_xaui
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_rxaui
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_1g
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_10g
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_15g
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rules_Set_10gwan
</UL>

<P><STRONG><a name="[1ec]"></a>cs4224_slice_soft_reset</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, cs4224_api.o(.text.cs4224_slice_soft_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 412<LI>Call Chain = cs4224_slice_soft_reset &rArr; cs4224_slice_soft_reset_intf &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_soft_reset_intf
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1f0]"></a>cs4224_slice_soft_reset_intf</STRONG> (Thumb, 1774 bytes, Stack size 88 bytes, cs4224_api.o(.text.cs4224_slice_soft_reset_intf))
<BR><BR>[Stack]<UL><LI>Max Depth = 396<LI>Call Chain = cs4224_slice_soft_reset_intf &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_mseq_power_savings
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_simplex_mate_slice
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_enable_power_savings
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_query_mseq_is_stalled
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_stall
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_soft_reset
</UL>

<P><STRONG><a name="[1a6]"></a>cs4224_squelch_driver</STRONG> (Thumb, 430 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_squelch_driver))
<BR><BR>[Stack]<UL><LI>Max Depth = 268<LI>Call Chain = cs4224_squelch_driver &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_simplex
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_squelch
</UL>

<P><STRONG><a name="[1ac]"></a>cs4224_translate_app_mode</STRONG> (Thumb, 278 bytes, Stack size 12 bytes, cs4224_api.o(.text.cs4224_translate_app_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = cs4224_translate_app_mode
</UL>
<BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_dump_debug_info
</UL>

<P><STRONG><a name="[1ae]"></a>cs4224_translate_edc_mode</STRONG> (Thumb, 300 bytes, Stack size 12 bytes, cs4224_api.o(.text.cs4224_translate_edc_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = cs4224_translate_edc_mode
</UL>
<BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_dump_debug_info
</UL>

<P><STRONG><a name="[1de]"></a>cs4224_ucode_data_prgm_image</STRONG> (Thumb, 344 bytes, Stack size 64 bytes, cs4224_api.o(.text.cs4224_ucode_data_prgm_image))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = cs4224_ucode_data_prgm_image &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_die_from_slice
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_manage_ucode_download
</UL>

<P><STRONG><a name="[1dd]"></a>cs4224_ucode_data_prgm_image_broadcast</STRONG> (Thumb, 888 bytes, Stack size 104 bytes, cs4224_api.o(.text.cs4224_ucode_data_prgm_image_broadcast))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = cs4224_ucode_data_prgm_image_broadcast &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_calc_crc16
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_manage_ucode_download
</UL>

<P><STRONG><a name="[17d]"></a>cs4224_unlock</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, cs4224_api.o(.text.cs4224_unlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = cs4224_unlock
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_enable
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_cfg
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_get_errors
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_config
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_enable
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_driver_settings
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_ucode_data_prgm_image
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_ucode_data_prgm_image_broadcast
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_manage_ucode_download
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_scratch_regs
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_apply_post_ucode_dwld_workarounds
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_apply_workarounds
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_apply_workarounds_power_down
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fracdiv_cdr_init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_clkdiv_ctrl_intf
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_disable_tx_driver_if_req
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_alt_coarse_tuning_intf
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_sub_rate_fc
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_8g_fc
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_15g
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_10g
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_8g
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_5g
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_clear_mailbox
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset_die
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fracdiv_core_init
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_soft_reset_intf
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_demux_enable
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mux_enable
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_wait_for_an
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_vco
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_fc_pre_an
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_start_an
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fcan_init_rate
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mon_volt_read_fixp
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mon_temp_read_fixp
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_config
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_set_bitswap
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_pgm_reg_from_efuse
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_edc_mode_intf
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_mseq_stall
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hw_id
</UL>

<P><STRONG><a name="[1ee]"></a>cs4224_update_cdr_fracn</STRONG> (Thumb, 126 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_update_cdr_fracn))
<BR><BR>[Stack]<UL><LI>Max Depth = 364<LI>Call Chain = cs4224_update_cdr_fracn &rArr; cs4224_fracdiv_cdr_init &rArr; cs4224_mseq_stall &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_fracdiv_cdr_init
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1ed]"></a>cs4224_update_clkdiv_ctrl</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_update_clkdiv_ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 292<LI>Call Chain = cs4224_update_clkdiv_ctrl &rArr; cs4224_update_clkdiv_ctrl_intf &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_clkdiv_ctrl_intf
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_line_rx_to_host_tx_dir
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_hw_duplex
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1f2]"></a>cs4224_update_clkdiv_ctrl_intf</STRONG> (Thumb, 348 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_update_clkdiv_ctrl_intf))
<BR><BR>[Stack]<UL><LI>Max Depth = 268<LI>Call Chain = cs4224_update_clkdiv_ctrl_intf &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_cfg_side
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_update_clkdiv_ctrl
</UL>

<P><STRONG><a name="[1d1]"></a>cs4224_update_pre_equalizer</STRONG> (Thumb, 314 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_update_pre_equalizer))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = cs4224_update_pre_equalizer &rArr; cs4224_reg_set_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_edc_mode_intf
</UL>

<P><STRONG><a name="[1eb]"></a>cs4224_update_scratch_regs</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, cs4224_api.o(.text.cs4224_update_scratch_regs))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = cs4224_update_scratch_regs &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get_channel
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_unlock
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_lock
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set_channel
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_get_die_from_slice
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_adj_pp
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_slice_enter_operational_state
</UL>

<P><STRONG><a name="[1c6]"></a>cs4224_wait_for_eeprom_finished</STRONG> (Thumb, 138 bytes, Stack size 40 bytes, cs4224_api.o(.text.cs4224_wait_for_eeprom_finished))
<BR><BR>[Stack]<UL><LI>Max Depth = 196<LI>Call Chain = cs4224_wait_for_eeprom_finished &rArr; cs4224_is_eeprom_finished &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_MDELAY
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_is_eeprom_finished
</UL>
<BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_hard_reset_die
</UL>

<P><STRONG><a name="[1f3]"></a>cs4343_diag_fixpattern_checker</STRONG> (Thumb, 186 bytes, Stack size 40 bytes, ber_test.o(.text.cs4343_diag_fixpattern_checker))
<BR><BR>[Stack]<UL><LI>Max Depth = 396<LI>Call Chain = cs4343_diag_fixpattern_checker &rArr; cs4224_diags_prbs_checker_enable &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_get_errors
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_config
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_checker_config
</UL>

<P><STRONG><a name="[1f4]"></a>cs4343_diag_fixpattern_generator</STRONG> (Thumb, 186 bytes, Stack size 48 bytes, ber_test.o(.text.cs4343_diag_fixpattern_generator))
<BR><BR>[Stack]<UL><LI>Max Depth = 404<LI>Call Chain = cs4343_diag_fixpattern_generator &rArr; cs4224_diags_fix_ptrn_generator_cfg &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_fix_ptrn_generator_cfg
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_set_pfd_mode
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_generator_config
</UL>

<P><STRONG><a name="[1f5]"></a>cs4343_diag_prbs15_checker</STRONG> (Thumb, 224 bytes, Stack size 40 bytes, cs4343_operation.o(.text.cs4343_diag_prbs15_checker))
<BR><BR>[Stack]<UL><LI>Max Depth = 396<LI>Call Chain = cs4343_diag_prbs15_checker &rArr; cs4224_diags_prbs_checker_enable &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_get_errors
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_config
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_checker_config
</UL>

<P><STRONG><a name="[1f6]"></a>cs4343_diag_prbs15_generator</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, cs4343_operation.o(.text.cs4343_diag_prbs15_generator))
<BR><BR>[Stack]<UL><LI>Max Depth = 388<LI>Call Chain = cs4343_diag_prbs15_generator &rArr; cs4224_diags_prbs_generator_set_pfd_mode &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_set_pfd_mode
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_config
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_generator_config
</UL>

<P><STRONG><a name="[1f7]"></a>cs4343_diag_prbs23_checker</STRONG> (Thumb, 224 bytes, Stack size 40 bytes, cs4343_operation.o(.text.cs4343_diag_prbs23_checker))
<BR><BR>[Stack]<UL><LI>Max Depth = 396<LI>Call Chain = cs4343_diag_prbs23_checker &rArr; cs4224_diags_prbs_checker_enable &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_get_errors
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_config
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_checker_config
</UL>

<P><STRONG><a name="[1f8]"></a>cs4343_diag_prbs23_generator</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, cs4343_operation.o(.text.cs4343_diag_prbs23_generator))
<BR><BR>[Stack]<UL><LI>Max Depth = 388<LI>Call Chain = cs4343_diag_prbs23_generator &rArr; cs4224_diags_prbs_generator_set_pfd_mode &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_set_pfd_mode
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_config
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_generator_config
</UL>

<P><STRONG><a name="[1f9]"></a>cs4343_diag_prbs31_checker</STRONG> (Thumb, 224 bytes, Stack size 40 bytes, cs4343_operation.o(.text.cs4343_diag_prbs31_checker))
<BR><BR>[Stack]<UL><LI>Max Depth = 396<LI>Call Chain = cs4343_diag_prbs31_checker &rArr; cs4224_diags_prbs_checker_enable &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_get_errors
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_config
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_checker_config
</UL>

<P><STRONG><a name="[1fa]"></a>cs4343_diag_prbs31_generator</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, cs4343_operation.o(.text.cs4343_diag_prbs31_generator))
<BR><BR>[Stack]<UL><LI>Max Depth = 388<LI>Call Chain = cs4343_diag_prbs31_generator &rArr; cs4224_diags_prbs_generator_set_pfd_mode &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_set_pfd_mode
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_config
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_generator_config
</UL>

<P><STRONG><a name="[1fb]"></a>cs4343_diag_prbs58_checker</STRONG> (Thumb, 222 bytes, Stack size 40 bytes, cs4343_operation.o(.text.cs4343_diag_prbs58_checker))
<BR><BR>[Stack]<UL><LI>Max Depth = 396<LI>Call Chain = cs4343_diag_prbs58_checker &rArr; cs4224_diags_prbs_checker_enable &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_get_errors
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_config
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_checker_config
</UL>

<P><STRONG><a name="[1fc]"></a>cs4343_diag_prbs58_generator</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, cs4343_operation.o(.text.cs4343_diag_prbs58_generator))
<BR><BR>[Stack]<UL><LI>Max Depth = 388<LI>Call Chain = cs4343_diag_prbs58_generator &rArr; cs4224_diags_prbs_generator_set_pfd_mode &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_set_pfd_mode
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_config
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_generator_config
</UL>

<P><STRONG><a name="[1fd]"></a>cs4343_diag_prbs7_checker</STRONG> (Thumb, 224 bytes, Stack size 40 bytes, cs4343_operation.o(.text.cs4343_diag_prbs7_checker))
<BR><BR>[Stack]<UL><LI>Max Depth = 396<LI>Call Chain = cs4343_diag_prbs7_checker &rArr; cs4224_diags_prbs_checker_enable &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_get_errors
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_config
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_checker_config
</UL>

<P><STRONG><a name="[1fe]"></a>cs4343_diag_prbs7_generator</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, cs4343_operation.o(.text.cs4343_diag_prbs7_generator))
<BR><BR>[Stack]<UL><LI>Max Depth = 388<LI>Call Chain = cs4343_diag_prbs7_generator &rArr; cs4224_diags_prbs_generator_set_pfd_mode &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_set_pfd_mode
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_config
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_generator_config
</UL>

<P><STRONG><a name="[1ff]"></a>cs4343_diag_prbs9_5_checker</STRONG> (Thumb, 224 bytes, Stack size 40 bytes, cs4343_operation.o(.text.cs4343_diag_prbs9_5_checker))
<BR><BR>[Stack]<UL><LI>Max Depth = 396<LI>Call Chain = cs4343_diag_prbs9_5_checker &rArr; cs4224_diags_prbs_checker_enable &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_get_errors
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_config
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_checker_config
</UL>

<P><STRONG><a name="[200]"></a>cs4343_diag_prbs9_5_generator</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, cs4343_operation.o(.text.cs4343_diag_prbs9_5_generator))
<BR><BR>[Stack]<UL><LI>Max Depth = 388<LI>Call Chain = cs4343_diag_prbs9_5_generator &rArr; cs4224_diags_prbs_generator_set_pfd_mode &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_set_pfd_mode
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_config
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_generator_config
</UL>

<P><STRONG><a name="[201]"></a>cs4343_diag_prbs9_checker</STRONG> (Thumb, 224 bytes, Stack size 40 bytes, cs4343_operation.o(.text.cs4343_diag_prbs9_checker))
<BR><BR>[Stack]<UL><LI>Max Depth = 396<LI>Call Chain = cs4343_diag_prbs9_checker &rArr; cs4224_diags_prbs_checker_enable &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_get_errors
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_enable
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_checker_config
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_checker_config
</UL>

<P><STRONG><a name="[202]"></a>cs4343_diag_prbs9_generator</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, cs4343_operation.o(.text.cs4343_diag_prbs9_generator))
<BR><BR>[Stack]<UL><LI>Max Depth = 388<LI>Call Chain = cs4343_diag_prbs9_generator &rArr; cs4224_diags_prbs_generator_set_pfd_mode &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_set_pfd_mode
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_prbs_generator_config
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prbs_generator_config
</UL>

<P><STRONG><a name="[203]"></a>ctlnetwork</STRONG> (Thumb, 114 bytes, Stack size 24 bytes, wizchip_conf.o(.text.ctlnetwork))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = ctlnetwork &rArr; wizchip_setnetinfo &rArr; WIZCHIP_WRITE_BUF
</UL>
<BR>[Calls]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_setnetinfo
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_getnetinfo
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_gettimeout
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_settimeout
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_getnetmode
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_setnetmode
</UL>
<BR>[Called By]<UL><LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;network_init
</UL>

<P><STRONG><a name="[20a]"></a>ctlwizchip</STRONG> (Thumb, 370 bytes, Stack size 40 bytes, wizchip_conf.o(.text.ctlwizchip))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = ctlwizchip &rArr; wizchip_init &rArr; wizchip_sw_reset &rArr; WIZCHIP_READ_BUF
</UL>
<BR>[Calls]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_getphylink
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_getphypmode
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_setphypmode
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_getphyconf
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_setphyconf
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_reset
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_getinterruptmask
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_setinterruptmask
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_getinterrupt
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_clrinterrupt
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_init
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_sw_reset
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;upd_connect
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;network_init
</UL>

<P><STRONG><a name="[217]"></a>draw_gang</STRONG> (Thumb, 340 bytes, Stack size 56 bytes, lcd_interface.o(.text.draw_gang))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = draw_gang &rArr; VIEWTECH_98 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_98
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_status
</UL>

<P><STRONG><a name="[218]"></a>erase_config_flash_sector</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, serial_net_config.o(.text.erase_config_flash_sector))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = erase_config_flash_sector &rArr; FLASH_EraseSector &rArr; FLASH_WaitForLastOperation &rArr; FLASH_GetStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_DataCacheCmd
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Lock
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_EraseSector
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Unlock
</UL>
<BR>[Called By]<UL><LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_network_config
</UL>

<P><STRONG><a name="[219]"></a>fix_crc_error</STRONG> (Thumb, 226 bytes, Stack size 64 bytes, lcd_interface.o(.text.fix_crc_error))
<BR><BR>[Stack]<UL><LI>Max Depth = 552<LI>Call Chain = fix_crc_error &rArr; fix_network_config_error &rArr; VIEWTECH_98 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fix_network_config_error
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;force_clear_flash_config
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_98
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;show_network_status
</UL>

<P><STRONG><a name="[21b]"></a>fix_network_config_error</STRONG> (Thumb, 362 bytes, Stack size 96 bytes, lcd_interface.o(.text.fix_network_config_error))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = fix_network_config_error &rArr; VIEWTECH_98 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_network_config
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_98
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fix_crc_error
</UL>

<P><STRONG><a name="[21a]"></a>force_clear_flash_config</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, serial_net_config.o(.text.force_clear_flash_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = force_clear_flash_config &rArr; FLASH_EraseSector &rArr; FLASH_WaitForLastOperation &rArr; FLASH_GetStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Lock
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_EraseSector
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ClearFlag
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Unlock
</UL>
<BR>[Called By]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fix_crc_error
</UL>

<P><STRONG><a name="[21e]"></a>id_read</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, lcd_interface.o(.text.id_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = id_read &rArr; EEpromReadStr
</UL>
<BR>[Calls]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEpromReadStr
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[235]"></a>is_special_ip_address</STRONG> (Thumb, 126 bytes, Stack size 12 bytes, serial_net_config.o(.text.is_special_ip_address))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = is_special_ip_address
</UL>
<BR>[Called By]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate_network_params
</UL>

<P><STRONG><a name="[220]"></a>load_network_config</STRONG> (Thumb, 122 bytes, Stack size 40 bytes, serial_net_config.o(.text.load_network_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = load_network_config &rArr; validate_network_params &rArr; validate_subnet_mask
</UL>
<BR>[Calls]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_config_from_flash
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;verify_config_integrity
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate_network_params
</UL>
<BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;show_network_status
</UL>

<P><STRONG><a name="[76]"></a>main</STRONG> (Thumb, 1146 bytes, Stack size 160 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 2200<LI>Call Chain = main &rArr; RUN_SHOW &rArr; Rate_Set &rArr; Si5340_Config &rArr; Si5340_WriteOneByteRMW &rArr; Si5340_WriteOneByte &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_TestRecTimeOut
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_03
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_30
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_Reso_30
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_40
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_20
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_Reso_02
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_02
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_Reso_00
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_00
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_Reso_10
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_10
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_01
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_E0
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XY_touch
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;upd_connect
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDC_Period
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_Config
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Freq_Set
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_Init
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IICDIV_Init
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_Out_Init
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Si5340_Address_Init
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SiCLK_Out_Init
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;POWER_Init
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;id_read
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_Init
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_Init
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Configuration
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Init
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[1e5]"></a>mdio2_read</STRONG> (Thumb, 132 bytes, Stack size 32 bytes, mdio_simulation.o(.text.mdio2_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_IDLE
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_Reg_Addr_Data_Read
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_Reg_Addr_Data_Write
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_TA
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_DEVAD
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_PRTAD
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_OP
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_ST
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_PRE
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get
</UL>

<P><STRONG><a name="[1e8]"></a>mdio2_write</STRONG> (Thumb, 134 bytes, Stack size 32 bytes, mdio_simulation.o(.text.mdio2_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = mdio2_write &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_IDLE
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_Reg_Addr_Data_Write
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_TA
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_DEVAD
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_PRTAD
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_OP
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_ST
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO2_PRE
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set
</UL>

<P><STRONG><a name="[1e4]"></a>mdio_read</STRONG> (Thumb, 132 bytes, Stack size 32 bytes, mdio_simulation.o(.text.mdio_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = mdio_read &rArr; MDIO_OP &rArr; MDC_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_IDLE
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_Reg_Addr_Data_Read
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_Reg_Addr_Data_Write
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_TA
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_DEVAD
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_PRTAD
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_OP
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_ST
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_PRE
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_get
</UL>

<P><STRONG><a name="[1e7]"></a>mdio_write</STRONG> (Thumb, 134 bytes, Stack size 32 bytes, mdio_simulation.o(.text.mdio_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = mdio_write &rArr; MDIO_OP &rArr; MDC_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_IDLE
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_Reg_Addr_Data_Write
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_TA
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_DEVAD
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_PRTAD
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_OP
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_ST
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_PRE
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MDIO_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_reg_set
</UL>

<P><STRONG><a name="[228]"></a>network_init</STRONG> (Thumb, 266 bytes, Stack size 48 bytes, udp.o(.text.network_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = network_init &rArr; ctlwizchip &rArr; wizchip_init &rArr; wizchip_sw_reset &rArr; WIZCHIP_READ_BUF
</UL>
<BR>[Calls]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printf
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlnetwork
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlwizchip
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;upd_connect
</UL>

<P><STRONG><a name="[10a]"></a>prbs_checker_config</STRONG> (Thumb, 220 bytes, Stack size 16 bytes, ber_test.o(.text.prbs_checker_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 412<LI>Call Chain = prbs_checker_config &rArr; cs4343_diag_fixpattern_checker &rArr; cs4224_diags_prbs_checker_enable &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_fixpattern_checker
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs58_checker
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs31_checker
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs23_checker
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs15_checker
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs9_5_checker
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs9_checker
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs7_checker
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>

<P><STRONG><a name="[106]"></a>prbs_generator_config</STRONG> (Thumb, 220 bytes, Stack size 16 bytes, ber_test.o(.text.prbs_generator_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 420<LI>Call Chain = prbs_generator_config &rArr; cs4343_diag_fixpattern_generator &rArr; cs4224_diags_fix_ptrn_generator_cfg &rArr; cs4224_mseq_enable_power_savings &rArr; cs4224_query_edc_mode &rArr; cs4224_reg_get_channel &rArr; cs4224_get_die_from_slice &rArr; cs4224_hw_id &rArr; cs4224_reg_get &rArr; mdio2_read &rArr; MDIO2_OP &rArr; MDC2_Period &rArr; MDIO_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_fixpattern_generator
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs58_generator
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs31_generator
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs23_generator
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs15_generator
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs9_5_generator
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs9_generator
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4343_diag_prbs7_generator
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>

<P><STRONG><a name="[221]"></a>read_config_from_flash</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, serial_net_config.o(.text.read_config_from_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = read_config_from_flash
</UL>
<BR>[Called By]<UL><LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_network_config
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_network_config
</UL>

<P><STRONG><a name="[232]"></a>reg_wizchip_cris_cbfunc</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, wizchip_conf.o(.text.reg_wizchip_cris_cbfunc))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = reg_wizchip_cris_cbfunc
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;upd_connect
</UL>

<P><STRONG><a name="[233]"></a>reg_wizchip_cs_cbfunc</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, wizchip_conf.o(.text.reg_wizchip_cs_cbfunc))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = reg_wizchip_cs_cbfunc
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;upd_connect
</UL>

<P><STRONG><a name="[234]"></a>reg_wizchip_spi_cbfunc</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, wizchip_conf.o(.text.reg_wizchip_spi_cbfunc))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = reg_wizchip_spi_cbfunc
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;upd_connect
</UL>

<P><STRONG><a name="[21c]"></a>save_network_config</STRONG> (Thumb, 246 bytes, Stack size 96 bytes, serial_net_config.o(.text.save_network_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = save_network_config &rArr; write_config_to_flash &rArr; FLASH_ProgramWord &rArr; FLASH_WaitForLastOperation &rArr; FLASH_GetStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_config_from_flash
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_config_to_flash
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;erase_config_flash_sector
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;verify_config_integrity
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_crc32
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate_network_params
<LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fix_network_config_error
</UL>

<P><STRONG><a name="[22d]"></a>show_network_status</STRONG> (Thumb, 702 bytes, Stack size 208 bytes, lcd_interface.o(.text.show_network_status))
<BR><BR>[Stack]<UL><LI>Max Depth = 760<LI>Call Chain = show_network_status &rArr; fix_crc_error &rArr; fix_network_config_error &rArr; VIEWTECH_98 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fix_crc_error
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_network_connectivity
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_getnetinfo
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_network_config
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_98
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sprintf
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_status
</UL>

<P><STRONG><a name="[230]"></a>socket</STRONG> (Thumb, 520 bytes, Stack size 24 bytes, socket.o(.text.socket))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = socket &rArr; close &rArr; WIZCHIP_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_network_connectivity
</UL>

<P><STRONG><a name="[22e]"></a>test_network_connectivity</STRONG> (Thumb, 238 bytes, Stack size 48 bytes, lcd_interface.o(.text.test_network_connectivity))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = test_network_connectivity &rArr; socket &rArr; close &rArr; WIZCHIP_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_getphylink
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_getnetinfo
</UL>
<BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;show_network_status
</UL>

<P><STRONG><a name="[171]"></a>tx8p</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, print.o(.text.tx8p))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_40
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_7C01
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_E7
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_98
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_A01
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_9B5A
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_70
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_E0
</UL>

<P><STRONG><a name="[105]"></a>tx_amp_config</STRONG> (Thumb, 428 bytes, Stack size 40 bytes, ber_test.o(.text.tx_amp_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = tx_amp_config &rArr; VIEWTECH_A01 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_init_driver_settings
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_A01
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RUN_SHOW
</UL>

<P><STRONG><a name="[225]"></a>upd_connect</STRONG> (Thumb, 116 bytes, Stack size 8 bytes, udp.o(.text.upd_connect))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = upd_connect &rArr; network_init &rArr; ctlwizchip &rArr; wizchip_init &rArr; wizchip_sw_reset &rArr; WIZCHIP_READ_BUF
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_MasterInit
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printf
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;network_init
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlwizchip
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reg_wizchip_spi_cbfunc
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reg_wizchip_cs_cbfunc
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reg_wizchip_cris_cbfunc
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_W5500
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;w5500_init
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d4]"></a>update_status</STRONG> (Thumb, 1966 bytes, Stack size 40 bytes, lcd_interface.o(.text.update_status))
<BR><BR>[Stack]<UL><LI>Max Depth = 800<LI>Call Chain = update_status &rArr; show_network_status &rArr; fix_crc_error &rArr; fix_network_config_error &rArr; VIEWTECH_98 &rArr; VIEWTECH_40 &rArr; tx8p &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;show_network_status
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_gang
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_A01
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_70
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_71
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_UDEFT
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Rate
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_More
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Timer
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_Pattern
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Menu_RTC
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_Reso_02
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_Reso_10
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XY_touch
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[223]"></a>validate_network_params</STRONG> (Thumb, 266 bytes, Stack size 48 bytes, serial_net_config.o(.text.validate_network_params))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = validate_network_params &rArr; validate_subnet_mask
</UL>
<BR>[Calls]<UL><LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate_subnet_mask
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_special_ip_address
</UL>
<BR>[Called By]<UL><LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_network_config
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_network_config
</UL>

<P><STRONG><a name="[236]"></a>validate_subnet_mask</STRONG> (Thumb, 102 bytes, Stack size 20 bytes, serial_net_config.o(.text.validate_subnet_mask))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = validate_subnet_mask
</UL>
<BR>[Called By]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate_network_params
</UL>

<P><STRONG><a name="[222]"></a>verify_config_integrity</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, serial_net_config.o(.text.verify_config_integrity))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = verify_config_integrity &rArr; calculate_crc32
</UL>
<BR>[Calls]<UL><LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_crc32
</UL>
<BR>[Called By]<UL><LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_network_config
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_network_config
</UL>

<P><STRONG><a name="[231]"></a>w5500_init</STRONG> (Thumb, 62 bytes, Stack size 24 bytes, w5500.o(.text.w5500_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = w5500_init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;upd_connect
</UL>

<P><STRONG><a name="[4]"></a>wizchip_bus_readbyte</STRONG> (Thumb, 12 bytes, Stack size 4 bytes, wizchip_conf.o(.text.wizchip_bus_readbyte))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = wizchip_bus_readbyte
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wizchip_conf.o(.data.WIZCHIP)
</UL>
<P><STRONG><a name="[5]"></a>wizchip_bus_writebyte</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, wizchip_conf.o(.text.wizchip_bus_writebyte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = wizchip_bus_writebyte
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wizchip_conf.o(.data.WIZCHIP)
</UL>
<P><STRONG><a name="[20d]"></a>wizchip_clrinterrupt</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, wizchip_conf.o(.text.wizchip_clrinterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = wizchip_clrinterrupt &rArr; WIZCHIP_WRITE
</UL>
<BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlwizchip
</UL>

<P><STRONG><a name="[0]"></a>wizchip_cris_enter</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, wizchip_conf.o(.text.wizchip_cris_enter))
<BR>[Address Reference Count : 2]<UL><LI> wizchip_conf.o(.text.reg_wizchip_cris_cbfunc)
<LI> wizchip_conf.o(.data.WIZCHIP)
</UL>
<P><STRONG><a name="[1]"></a>wizchip_cris_exit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, wizchip_conf.o(.text.wizchip_cris_exit))
<BR>[Address Reference Count : 2]<UL><LI> wizchip_conf.o(.text.reg_wizchip_cris_cbfunc)
<LI> wizchip_conf.o(.data.WIZCHIP)
</UL>
<P><STRONG><a name="[3]"></a>wizchip_cs_deselect</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, wizchip_conf.o(.text.wizchip_cs_deselect))
<BR>[Address Reference Count : 2]<UL><LI> wizchip_conf.o(.text.reg_wizchip_cs_cbfunc)
<LI> wizchip_conf.o(.data.WIZCHIP)
</UL>
<P><STRONG><a name="[2]"></a>wizchip_cs_select</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, wizchip_conf.o(.text.wizchip_cs_select))
<BR>[Address Reference Count : 2]<UL><LI> wizchip_conf.o(.text.reg_wizchip_cs_cbfunc)
<LI> wizchip_conf.o(.data.WIZCHIP)
</UL>
<P><STRONG><a name="[20e]"></a>wizchip_getinterrupt</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, wizchip_conf.o(.text.wizchip_getinterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = wizchip_getinterrupt &rArr; WIZCHIP_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlwizchip
</UL>

<P><STRONG><a name="[210]"></a>wizchip_getinterruptmask</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, wizchip_conf.o(.text.wizchip_getinterruptmask))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = wizchip_getinterruptmask &rArr; WIZCHIP_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlwizchip
</UL>

<P><STRONG><a name="[205]"></a>wizchip_getnetinfo</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, wizchip_conf.o(.text.wizchip_getnetinfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = wizchip_getnetinfo &rArr; WIZCHIP_READ_BUF
</UL>
<BR>[Calls]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ_BUF
</UL>
<BR>[Called By]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_network_connectivity
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;show_network_status
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlnetwork
</UL>

<P><STRONG><a name="[207]"></a>wizchip_getnetmode</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, wizchip_conf.o(.text.wizchip_getnetmode))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = wizchip_getnetmode &rArr; WIZCHIP_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlnetwork
</UL>

<P><STRONG><a name="[209]"></a>wizchip_gettimeout</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, wizchip_conf.o(.text.wizchip_gettimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = wizchip_gettimeout &rArr; WIZCHIP_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlnetwork
</UL>

<P><STRONG><a name="[20c]"></a>wizchip_init</STRONG> (Thumb, 302 bytes, Stack size 24 bytes, wizchip_conf.o(.text.wizchip_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = wizchip_init &rArr; wizchip_sw_reset &rArr; WIZCHIP_READ_BUF
</UL>
<BR>[Calls]<UL><LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_sw_reset
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlwizchip
</UL>

<P><STRONG><a name="[20f]"></a>wizchip_setinterruptmask</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, wizchip_conf.o(.text.wizchip_setinterruptmask))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = wizchip_setinterruptmask &rArr; WIZCHIP_WRITE
</UL>
<BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlwizchip
</UL>

<P><STRONG><a name="[204]"></a>wizchip_setnetinfo</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, wizchip_conf.o(.text.wizchip_setnetinfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = wizchip_setnetinfo &rArr; WIZCHIP_WRITE_BUF
</UL>
<BR>[Calls]<UL><LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_WRITE_BUF
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlnetwork
</UL>

<P><STRONG><a name="[206]"></a>wizchip_setnetmode</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, wizchip_conf.o(.text.wizchip_setnetmode))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = wizchip_setnetmode &rArr; WIZCHIP_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlnetwork
</UL>

<P><STRONG><a name="[208]"></a>wizchip_settimeout</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, wizchip_conf.o(.text.wizchip_settimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = wizchip_settimeout &rArr; WIZCHIP_WRITE
</UL>
<BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlnetwork
</UL>

<P><STRONG><a name="[7a]"></a>wizchip_spi_readbyte</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, wizchip_conf.o(.text.wizchip_spi_readbyte))
<BR>[Address Reference Count : 1]<UL><LI> wizchip_conf.o(.text.reg_wizchip_spi_cbfunc)
</UL>
<P><STRONG><a name="[7b]"></a>wizchip_spi_writebyte</STRONG> (Thumb, 10 bytes, Stack size 4 bytes, wizchip_conf.o(.text.wizchip_spi_writebyte))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = wizchip_spi_writebyte
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wizchip_conf.o(.text.reg_wizchip_spi_cbfunc)
</UL>
<P><STRONG><a name="[20b]"></a>wizchip_sw_reset</STRONG> (Thumb, 134 bytes, Stack size 72 bytes, wizchip_conf.o(.text.wizchip_sw_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = wizchip_sw_reset &rArr; WIZCHIP_READ_BUF
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ_BUF
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_WRITE
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_WRITE_BUF
</UL>
<BR>[Called By]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizchip_init
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlwizchip
</UL>

<P><STRONG><a name="[213]"></a>wizphy_getphyconf</STRONG> (Thumb, 182 bytes, Stack size 32 bytes, wizchip_conf.o(.text.wizphy_getphyconf))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = wizphy_getphyconf &rArr; WIZCHIP_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlwizchip
</UL>

<P><STRONG><a name="[216]"></a>wizphy_getphylink</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, wizchip_conf.o(.text.wizphy_getphylink))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = wizphy_getphylink &rArr; WIZCHIP_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_network_connectivity
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlwizchip
</UL>

<P><STRONG><a name="[215]"></a>wizphy_getphypmode</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, wizchip_conf.o(.text.wizphy_getphypmode))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = wizphy_getphypmode &rArr; WIZCHIP_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlwizchip
</UL>

<P><STRONG><a name="[211]"></a>wizphy_reset</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, wizchip_conf.o(.text.wizphy_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = wizphy_reset &rArr; WIZCHIP_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_setphypmode
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_setphyconf
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlwizchip
</UL>

<P><STRONG><a name="[212]"></a>wizphy_setphyconf</STRONG> (Thumb, 174 bytes, Stack size 16 bytes, wizchip_conf.o(.text.wizphy_setphyconf))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = wizphy_setphyconf &rArr; wizphy_reset &rArr; WIZCHIP_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_reset
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlwizchip
</UL>

<P><STRONG><a name="[214]"></a>wizphy_setphypmode</STRONG> (Thumb, 194 bytes, Stack size 16 bytes, wizchip_conf.o(.text.wizphy_setphypmode))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = wizphy_setphypmode &rArr; wizphy_reset &rArr; WIZCHIP_READ
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_READ
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wizphy_reset
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIZCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctlwizchip
</UL>

<P><STRONG><a name="[22b]"></a>write_config_to_flash</STRONG> (Thumb, 236 bytes, Stack size 48 bytes, serial_net_config.o(.text.write_config_to_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = write_config_to_flash &rArr; FLASH_ProgramWord &rArr; FLASH_WaitForLastOperation &rArr; FLASH_GetStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ProgramWord
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_DataCacheCmd
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Lock
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Unlock
</UL>
<BR>[Called By]<UL><LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_network_config
</UL>

<P><STRONG><a name="[239]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[253]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[254]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[255]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[229]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = printf
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;upd_connect
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;network_init
</UL>

<P><STRONG><a name="[23b]"></a>__0snprintf</STRONG> (Thumb, 48 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[256]"></a>__1snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[257]"></a>__2snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[258]"></a>__c89snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[19c]"></a>snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_dump_debug_info
<LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_pcs_status
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_rxlock
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_polarity_inversion
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_fracn
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_dividers
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_sku
<LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_voltage_1p8
<LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_voltage_0p9
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_edc_mode
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_reset_count
<LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cs4224_diags_format_temperature
</UL>

<P><STRONG><a name="[23c]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[259]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[25a]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[25b]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[170]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_40
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;show_network_status
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_A01
</UL>

<P><STRONG><a name="[132]"></a>__hardfp_ceil</STRONG> (Thumb, 252 bytes, Stack size 40 bytes, ceil.o(i.__hardfp_ceil))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = __hardfp_ceil &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
</UL>

<P><STRONG><a name="[131]"></a>__hardfp_floor</STRONG> (Thumb, 252 bytes, Stack size 40 bytes, floor.o(i.__hardfp_floor))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RunFreq
</UL>

<P><STRONG><a name="[25c]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[25d]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[25e]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[82]"></a>fputc</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, fputc.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = fputc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL><P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[173]"></a>DEC2BCD</STRONG> (Thumb, 36 bytes, Stack size 4 bytes, print.o(.text.DEC2BCD))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DEC2BCD
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VIEWTECH_E7
</UL>

<P><STRONG><a name="[fc]"></a>NVIC_SetPriority</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, nvic.o(.text.NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Configuration
</UL>

<P><STRONG><a name="[146]"></a>SysTick_Config</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, systick.o(.text.SysTick_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SysTick_Config &rArr; NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
</UL>

<P><STRONG><a name="[147]"></a>NVIC_SetPriority</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, systick.o(.text.NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Config
</UL>

<P><STRONG><a name="[14b]"></a>SetSysClock</STRONG> (Thumb, 386 bytes, Stack size 12 bytes, system_stm32f4xx.o(.text.SetSysClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SetSysClock
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[23e]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[23a]"></a>_printf_core</STRONG> (Thumb, 1744 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0snprintf
<LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[240]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[23f]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[83]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0snprintf)
</UL>
<P><STRONG><a name="[84]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
