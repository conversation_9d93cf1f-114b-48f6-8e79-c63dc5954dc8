# 以太网串口配置功能使用说明

## 概述

本功能允许通过串口命令动态配置以太网参数，包括IP地址、子网掩码、网关和DNS服务器等，配置会自动保存到Flash闪存中并应用到W5500以太网芯片。

## 硬件连接

- **串口**: UART1 (PA9-TX, PA10-RX)
- **波特率**: 115200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无

## 支持的命令

### 1. 查看当前网络配置
```
NET_CONFIG
```
返回当前的网络配置信息，包括IP地址、子网掩码、网关、DNS等。

### 2. 设置IP地址
```
SET_IP <ip_address>
```
示例：
```
SET_IP *************
```

### 3. 设置子网掩码
```
SET_MASK <subnet_mask>
```
示例：
```
SET_MASK *************
```

### 4. 设置网关
```
SET_GW <gateway>
```
示例：
```
SET_GW ***********
```

### 5. 设置DNS服务器
```
SET_DNS <dns_server>
```
示例：
```
SET_DNS *******
```

### 6. 应用网络配置
```
APPLY_NET
```
将当前设置的网络配置应用到W5500芯片并保存到Flash。

### 7. 重置网络配置
```
RESET_NET
```
恢复默认的网络配置。

## 使用流程

### 基本配置流程：

1. **连接串口调试工具**（如PuTTY、串口助手等）
2. **查看当前配置**：
   ```
   NET_CONFIG
   ```

3. **设置新的网络参数**：
   ```
   SET_IP *************
   SET_MASK *************
   SET_GW ***********
   SET_DNS *******
   ```

4. **应用配置**：
   ```
   APPLY_NET
   ```

5. **验证配置**：
   ```
   NET_CONFIG
   ```

## 默认配置

系统默认网络配置：
- **IP地址**: *************
- **子网掩码**: *************
- **网关**: ***********
- **DNS服务器**: *******
- **MAC地址**: 00:08:DC:01:02:03
- **DHCP模式**: 禁用（静态IP）

## 错误处理

系统会对输入的参数进行验证：

### IP地址验证
- 格式必须为 x.x.x.x
- 每个字段范围：0-255
- 不允许特殊地址（如0.0.0.0、***************等）

### 子网掩码验证
- 必须是连续的1后跟连续的0
- 支持标准子网掩码（如*************、***********等）

### 网关验证
- 必须与IP地址在同一网段
- 格式验证同IP地址

## 响应格式

### 成功响应
```
OK: <操作描述>
```

### 错误响应
```
ERROR: <错误描述>
```

### 配置信息响应
```
Network Configuration:
IP Address: *************
Subnet Mask: *************
Gateway: ***********
DNS Server: *******
MAC Address: 00:08:DC:01:02:03
DHCP Mode: Disabled
```

## 存储机制

- **存储位置**: STM32F429 Flash扇区11 (地址0x080E0000)
- **数据完整性**: 使用CRC32校验确保数据完整性
- **备份机制**: 支持配置备份和恢复
- **重试机制**: 写入失败时自动重试最多3次

## 注意事项

1. **配置生效**: 使用`APPLY_NET`命令后配置才会生效
2. **参数验证**: 系统会验证所有参数的有效性
3. **网络冲突**: 避免设置与现有网络冲突的IP地址
4. **Flash寿命**: Flash写入次数有限，避免频繁配置
5. **断电保护**: 配置保存在Flash中，断电后不会丢失

## 故障排除

### 1. 串口无响应
- 检查串口连接和参数设置
- 确认波特率为115200
- 检查TX/RX线是否正确连接

### 2. 配置保存失败
- 检查Flash是否有足够空间
- 确认Flash扇区未被写保护
- 重启设备后重试

### 3. 网络配置无效
- 验证IP地址格式
- 确认网络参数与实际网络环境匹配
- 检查W5500芯片连接

### 4. 参数验证失败
- 确认IP地址格式正确
- 检查子网掩码是否为标准格式
- 验证网关是否在同一网段

## 开发接口

如需在代码中调用相关功能，可使用以下API：

```c
// 加载网络配置
NetConfigError_t load_network_config(NetConfig_t* config);

// 保存网络配置
NetConfigError_t save_network_config(const NetConfig_t* config);

// 应用网络配置
NetConfigError_t apply_network_config(void);

// 验证IP地址
NetConfigError_t validate_ip_format(const char* ip_str);

// 解析串口命令
CommandType_t parse_serial_command(const char* input, SerialCommand_t* cmd);
```

## 版本信息

- **版本**: V1.0
- **日期**: 2024年
- **兼容性**: STM32F429 + W5500
- **依赖**: STM32F4xx HAL库、W5500驱动库
