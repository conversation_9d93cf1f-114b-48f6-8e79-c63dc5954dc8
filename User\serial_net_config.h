#ifndef SERIAL_NET_CONFIG_H
#define SERIAL_NET_CONFIG_H

#include <stdint.h>
#include <stddef.h>

/* Flash存储地址和魔数常量 */
#define NET_CONFIG_FLASH_ADDR       0x080E0000  // Flash存储地址 (896KB位置)
#define NET_CONFIG_MAGIC_NUMBER     0x5A5A5A5A  // 配置魔数
#define NET_CONFIG_VERSION          0x0001      // 配置版本
#define NET_CONFIG_MAX_RETRIES      3           // 存储重试次数
#define NET_CONFIG_HEADER_SIZE      16          // 配置头部大小

/* 串口配置常量 */
#define SERIAL_CMD_BUFFER_SIZE      256         // 命令缓冲区大小
#define SERIAL_PARAM_MAX_SIZE       64          // 参数最大长度
#define SERIAL_RESPONSE_MAX_SIZE    512         // 响应最大长度

/* 安全相关常量 */
#define AUTH_PASSWORD_MAX_LEN       32          // 密码最大长度
#define AUTH_MAX_RETRY_COUNT        3           // 最大密码重试次数
#define AUTH_LOCKOUT_TIME_MIN       30          // 锁定时间(分钟)
#define AUTH_SESSION_TIMEOUT_MIN    10          // 会话超时时间(分钟)
#define DEFAULT_ADMIN_PASSWORD      "admin123"  // 默认管理员密码

/* 网络配置结构体 */
typedef struct {
    uint8_t ip[4];          // IP地址
    uint8_t subnet[4];      // 子网掩码
    uint8_t gateway[4];     // 网关地址
    uint8_t dns[4];         // DNS服务器
    uint8_t mac[6];         // MAC地址
    uint8_t dhcp_mode;      // DHCP模式 (0=静态, 1=DHCP)
    uint8_t reserved[9];    // 保留字节
    uint32_t checksum;      // 配置校验和
} NetConfig_t;

/* 配置头部结构体 */
typedef struct {
    uint32_t magic;         // 魔数
    uint16_t version;       // 版本
    uint16_t length;        // 数据长度
    uint32_t crc32;         // CRC32校验
    uint32_t reserved;      // 保留
} NetConfigHeader_t;

/* 串口命令结构体 */
typedef struct {
    uint8_t cmd_type;       // 命令类型
    char param[SERIAL_PARAM_MAX_SIZE];  // 命令参数
    uint8_t param_count;    // 参数个数
    char params[4][32];     // 分离的参数数组(最多4个参数)
} SerialCommand_t;

/* 命令类型枚举 */
typedef enum {
    CMD_NET_CONFIG = 0x01,  // 查看网络配置
    CMD_SET_IP,             // 设置IP地址
    CMD_SET_MASK,           // 设置子网掩码
    CMD_SET_GW,             // 设置网关
    CMD_SET_DNS,            // 设置DNS
    CMD_APPLY_NET,          // 应用网络配置
    CMD_RESET_NET,          // 重置网络配置
    CMD_AUTH,               // 身份验证
    CMD_LOGOUT,             // 退出登录
    CMD_HELP,               // 帮助信息
    CMD_UNKNOWN = 0xFF      // 未知命令
} CommandType_t;

/* 错误代码枚举 */
typedef enum {
    NET_CONFIG_OK = 0,              // 操作成功
    NET_CONFIG_ERR_INVALID_IP,      // 无效IP地址
    NET_CONFIG_ERR_INVALID_MASK,    // 无效子网掩码
    NET_CONFIG_ERR_INVALID_GW,      // 无效网关地址
    NET_CONFIG_ERR_INVALID_DNS,     // 无效DNS地址
    NET_CONFIG_ERR_INVALID_CMD,     // 无效命令
    NET_CONFIG_ERR_INVALID_PARAM,   // 无效参数
    NET_CONFIG_ERR_AUTH_FAIL,       // 认证失败
    NET_CONFIG_ERR_AUTH_REQUIRED,   // 需要认证
    NET_CONFIG_ERR_AUTH_LOCKED,     // 认证被锁定
    NET_CONFIG_ERR_SESSION_TIMEOUT, // 会话超时
    NET_CONFIG_ERR_STORAGE_READ,    // 存储读取错误
    NET_CONFIG_ERR_STORAGE_WRITE,   // 存储写入错误
    NET_CONFIG_ERR_STORAGE_ERASE,   // 存储擦除错误
    NET_CONFIG_ERR_STORAGE_CRC,     // 存储CRC校验错误
    NET_CONFIG_ERR_NETWORK_APPLY,   // 网络配置应用错误
    NET_CONFIG_ERR_NETWORK_TEST,    // 网络连通性测试错误
    NET_CONFIG_ERR_TIMEOUT,         // 操作超时
    NET_CONFIG_ERR_BUSY,            // 系统忙
    NET_CONFIG_ERR_NOT_SUPPORTED,   // 不支持的操作
    NET_CONFIG_ERR_UNKNOWN = 0xFF,  // 未知错误
		NET_CONFIG_ERR_APPLY_FAILED,
} NetConfigError_t;

/* 认证状态枚举 */
typedef enum {
    AUTH_STATE_LOGGED_OUT = 0,      // 未登录
    AUTH_STATE_LOGGED_IN,           // 已登录
    AUTH_STATE_LOCKED               // 被锁定
} AuthState_t;

/* 网络配置状态枚举 */
typedef enum {
    NET_STATE_IDLE = 0,             // 空闲状态
    NET_STATE_CONFIGURING,          // 配置中
    NET_STATE_APPLYING,             // 应用配置中
    NET_STATE_TESTING,              // 测试连通性中
    NET_STATE_ERROR                 // 错误状态
} NetConfigState_t;

/* 会话管理结构体 */
typedef struct {
    AuthState_t auth_state;         // 认证状态
    uint32_t login_time;            // 登录时间戳
    uint32_t last_activity;         // 最后活动时间戳
    uint8_t retry_count;            // 密码重试次数
    uint32_t lockout_time;          // 锁定开始时间戳
} SessionInfo_t;

/* 系统状态结构体 */
typedef struct {
    NetConfigState_t net_state;     // 网络配置状态
    SessionInfo_t session;          // 会话信息
    NetConfig_t current_config;     // 当前网络配置
    NetConfig_t pending_config;     // 待应用的配置
    uint8_t config_changed;         // 配置是否已修改
} SystemState_t;

/* 默认网络配置 */
#define DEFAULT_IP_ADDR     {192, 168, 1, 100}
#define DEFAULT_SUBNET_MASK {255, 255, 255, 0}
#define DEFAULT_GATEWAY     {192, 168, 1, 1}
#define DEFAULT_DNS_SERVER  {8, 8, 8, 8}
#define DEFAULT_MAC_ADDR    {0x00, 0x08, 0xDC, 0x01, 0x02, 0x03}

/* 函数声明 */

/* 存储管理函数 */
NetConfigError_t save_network_config(const NetConfig_t* config);
NetConfigError_t load_network_config(NetConfig_t* config);
NetConfigError_t backup_current_config(void);
NetConfigError_t restore_default_config(void);
uint32_t calculate_crc32(const uint8_t* data, size_t length);
NetConfigError_t verify_config_integrity(const NetConfigHeader_t* header, const NetConfig_t* config);
NetConfigError_t erase_config_flash_sector(void);
NetConfigError_t write_config_to_flash(const NetConfigHeader_t* header, const NetConfig_t* config);
NetConfigError_t read_config_from_flash(NetConfigHeader_t* header, NetConfig_t* config);

/* 参数验证函数 */
NetConfigError_t validate_ip_format(const char* ip_str);
NetConfigError_t parse_ip_string(const char* ip_str, uint8_t ip_bytes[4]);
int is_special_ip_address(const uint8_t ip_bytes[4]);
NetConfigError_t validate_subnet_mask(const uint8_t mask_bytes[4]);
NetConfigError_t validate_network_params(const NetConfig_t* config);
int check_network_conflict(const NetConfig_t* config1, const NetConfig_t* config2);
NetConfigError_t validate_mac_address(const uint8_t mac_bytes[6]);

/* 串口命令解析函数 */
CommandType_t parse_serial_command(const char* input, SerialCommand_t* cmd);
void trim_whitespace(char* str);
int split_parameters(const char* param_str, char params[][32], int max_params);
CommandType_t identify_command(const char* cmd_str);

/* 命令响应格式化函数 */
int format_response(char* response_buffer, size_t buffer_size, NetConfigError_t error_code, const char* message);
int format_success_response(char* response_buffer, size_t buffer_size, const char* message);
int format_error_response(char* response_buffer, size_t buffer_size, NetConfigError_t error_code, const char* details);
int format_config_display_response(char* response_buffer, size_t buffer_size, const NetConfig_t* config);
const char* get_error_message(NetConfigError_t error_code);

#endif /* SERIAL_NET_CONFIG_H */