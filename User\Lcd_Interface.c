/************************************************************************************************
 *
 *工程名称：4*25G误码仪
 *
 *说	明：人机互动
 *
 *版	本：V1.00
 *
 *作	者：LHZ
 *
 *创建时间：2016年4月26日
 *
 *修改时间：2016年4月26日
 *
 s************************************************************************************************/
 
#include "Drivers.h"
#include "cs4224_api.h"
#include "Lcd_Interface.h"
#include "BER_Test.h"
#include "serial_net_config.h"
#include "wizchip_conf.h"
#include "socket.h"
int USB_biaoji = No_Order;	
int XY[2] = {0,0};					      /* 用来储存触碰屏幕的坐标 */

//u8 udp0 = 0x55;                //用来存储64位User Defined Pattern码型
//u8 udp8 = 0x55;
//u8 udp16 = 0x55;
//u8 udp24 = 0x55;
//u8 udp32 = 0x55;
//u8 udp40 = 0x55;
//u8 udp48 = 0x55;
//u8 udp56 = 0x55;

u8  udp[4][8] ={ {0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55},{0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55},{0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55},{0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55}};
u8  curchan=0;
	
u8 TX_FLAG = txOn;
u8 TX_Status = 0x0;
u8 TX_First = 0;
u8 TX_Rate = 0; 
	
u8 RUN_FLAG = noRuning;					      /* 用来标志按了开始测试没有，0为没按开始，1为按了 */
u8 RUN_FLAG1 = noRuning1;
u8 RUN_FLAG2 = noRuning2;
u8 RUN_FLAG3 = noRuning3;
u8 START_RUN = First_circle;		      /* 用来标志是第一次进入测试循环 */
u8 START_RUN1 = First1_circle;
u8 START_RUN2 = First2_circle;
u8 START_RUN3 = First3_circle;
int TIME_COUNT = 0;					          /* 用来标志测试中计时的变量 */
int TIME_COUNT0 = 0;	
int TIME_COUNT00 = 0;
int TIME_COUNT1 = 0;	
int TIME_COUNT11 = 0;
int TIME_COUNT2 = 0;	
int TIME_COUNT22 = 0;
int TIME_COUNT3 = 0;	
int TIME_COUNT33 = 0;
u8 Icon = NONE_PRESS ;							  /* 用来标志按了哪个按钮 */


u32 g_TamperData;
#define    E_Tamper    0x80F8000        //要写入FlasH数据的地址  STM32F103RBT6 128K ROM  最大地址到0x8020000；
u32 id=0  ;                    //产品序列号

 /*******************************************************************************
* Function Name  : id_read
* Description    : 将断电重启前设置的id序列号从FlasH中读取出来备用
* Input          : None
* Output         : None
* Return         : None
* Attention		 : None
*******************************************************************************/
void id_read(void)
{
 eeprom_read_block(&g_TamperData,E_Tamper,4) ;
 id = g_TamperData;
}



/*******************************************************************************
* Function Name  : XY_touch
* Description    : 人机交互程序
* Input          : None
* Output         : None
* Return         : None
* Attention		 : None
*******************************************************************************/
void XY_touch(int* XY)					/* 触屏程序 */
{
//  u8 i;
	
	XY[0]=xls[1]<<8;					/* X的坐标 */
	XY[0]+=xls[2];
	XY[1]=xls[3]<<8;					/* Y的坐标 */
	XY[1]+=xls[4];

if((RUN_FLAG == noRuning)&&(RUN_FLAG1 == noRuning1)	&&(RUN_FLAG2 == noRuning2) &&(RUN_FLAG3 == noRuning3))					/* 判断按了开始没有，0为没有 */
{
  if(Icon == NONE_PRESS)
  { 
   if(XY[0]>0 && XY[1]>236 && XY[0]<120  && XY[1]<272)		 /* 按了码型选择的按钮 */
	 {
	    
		if(xls[0]==0x01)                           //0x01是按下 0x03是抬起
		 {
		  VIEWTECH_71(72,0,236,120,272,0,236);    //码型按钮 高亮显示
		  Icon = MAXING;
		 }

//	   else if(xls[0]==0x03)										/* 手指离开时候的操作 */
//	   {
//		    Icon = NONE_PRESS;
//			  Menu_Pattern();
//		 }
	 }
	 else if(XY[0]>240 && XY[1]>236 && XY[0]<360  && XY[1]<272)			 /* 按了定时模式设置的按钮 */
	 {
		if(xls[0]==0x01)
		 {
		  VIEWTECH_71(73,240,236,360,272,240,236);              /*手指按下*/
		  Icon = DINGSHI;
		 }
//	    else if(xls[0]==0x03)												 /* 手指离开时的操作 */
//		 {
//		    Icon = NONE_PRESS;
//			  Menu_Timer();	 
//		 }
	 }
	 
   else if(XY[0]>360 && XY[1]>236 && XY[0]<480  && XY[1]<272)			 /* 按了 更多 的按钮 */
	 {
		if(xls[0]==0x01)
		 {
		  VIEWTECH_71(9,360,236,480,272,360,236);              /*手指按下*/
		  Icon = GENGDUO;
		 }
//	    else if(xls[0]==0x03)												 /* 手指离开时的操作 */
//		 {
//		    Icon = NONE_PRESS;
//			  Menu_More();
//		 }
	 }
	 else if(XY[0]>120 && XY[1]>236 && XY[0]<240  && XY[1]<272)			 /* 按了速率选择的按钮 */
	 {
		if(xls[0]==0x01)
		 {
		  VIEWTECH_71(5,120,236,240,272,120,236);
		  Icon = SULV;
		 }
//	    else if(xls[0]==0x03)												 /* 手指离开时的操作 */
//		 {
//		    Icon = NONE_PRESS;
//	  		Menu_Rate();		
//		 }
	 }
		
		else if(XY[0]>675 && XY[1]>0 && XY[0]<790  && XY[1]<55 && xls[0]==0x03 )			 /* 按了实时时间显示设置的按钮 */
	  {
      Menu_RTC();  			
		}
		else if(XY[0]>273 && XY[1]>50 && XY[0]<310  && XY[1]<73)		  	/* 按了开始检测按钮 */
		{
			 if(xls[0]==0x01)
			 {       
			    VIEWTECH_71(11,273,50,310,73,273,50);              //下沉效果
			  	Icon = KAISHI; 
			 }
		}
		else if(XY[0]>273 && XY[1]>96 && XY[0]<310  && XY[1]<119)		  	/* 按了开始检测按钮 */
		{
			 if(xls[0]==0x01)
			 {       
			    VIEWTECH_71(11,273,96,310,119,273,96);              //下沉效果
			  	Icon = KAISHI1; 
			 }
		}
		else if(XY[0]>273 && XY[1]>142 && XY[0]<310  && XY[1]<165)		  	/* 按了开始检测按钮 */
		{
			 if(xls[0]==0x01)
			 {       
			    VIEWTECH_71(11,273,142,310,165,273,142);              //下沉效果
			  	Icon = KAISHI2; 
			 }
		}
		else if(XY[0]>273 && XY[1]>188 && XY[0]<310  && XY[1]<210)		  	/* 按了开始检测按钮 */
		{
			 if(xls[0]==0x01)
			 {       
			    VIEWTECH_71(11,273,188,310,210,273,188);             //下沉效果
			  	Icon = KAISHI3; 
			 }
		}
		
		else if(XY[0]>5 && XY[1]>46 && XY[0]<27  && XY[1]<71)		 /* 按罷 TX0 */
	 {	    
		if(xls[0]==0x01)                           //0x01是按下 0x03是抬起
		 {
//		   VIEWTECH_71(11,5,95,53,129,5,95); 
			 Icon = TX0SW ;		  
		 }
	 }	 
		else if(XY[0]>5 && XY[1]>92 && XY[0]<27  && XY[1]<117)	 /* 按罷 TX1 */
	 {	    
		if(xls[0]==0x01)                           //0x01是按下 0x03是抬起
		 {
//		   VIEWTECH_71(11,5,175,53,209,5,175); 
			 Icon = TX1SW ;		  
		 }
	 }
	 		else if(XY[0]>5 && XY[1]>138 && XY[0]<27  && XY[1]<163)			 /* 按罷 TX2 */
	 {	    
		if(xls[0]==0x01)                           //0x01是按下 0x03是抬起
		 {
//		   VIEWTECH_71(11,5,255,53,289,5,255); 
			 Icon = TX2SW ;		  
		 }
	 }
	 		else if(XY[0]>5 && XY[1]>184 && XY[0]<27  && XY[1]<209)		 /* 按罷 TX3 */
	 {	    
		if(xls[0]==0x01)                           //0x01是按下 0x03是抬起
		 {
//		   VIEWTECH_71(11,5,335,53,369,5,335);
			 Icon = TX3SW ;		  
		 }
	 }	 	
  }
  else                                                //Icon ≠ NONE_PRESS;         即按钮放开的操作
  {
	 if(Icon == TX0SW)
	 {
		 if(xls[0]==0x03)
		 {
			 VIEWTECH_71(2,5,46,27,71,5,46);
			 Icon = NONE_PRESS;
			 TX_Status^=1;
			 //TX_First=0;
		 }
		 update_status();
	 }
	 else	 if(Icon == TX1SW)
	 {
		 if(xls[0]==0x03)
		 {
			 VIEWTECH_71(2,5,92,27,117,5,92);
			 Icon = NONE_PRESS;
			 TX_Status^=2;
			 //TX_First=0;
		 }
		 update_status();
	 }
	 	 else	 if(Icon == TX2SW)
	 {
		 if(xls[0]==0x03)
		 {
			 VIEWTECH_71(2,5,138,27,163,5,138);
			 Icon = NONE_PRESS;
			 TX_Status^=4;
			 //TX_First=0;
		 }
		 update_status();
	 }
	 	 else	 if(Icon == TX3SW)
	 {
		 if(xls[0]==0x03)
		 {
			 VIEWTECH_71(2,5,184,27,209,5,184);
			 Icon = NONE_PRESS;
			 TX_Status^=8;
			 //TX_First=0;
		 }
		 update_status();
	 }
		else if(Icon == MAXING)
	 {
	   	if(xls[0]==0x03)										/* 手指松开 */
		 {
		  Icon = NONE_PRESS;
			Menu_Pattern();
		 }
	 }	   
	  else if(Icon == DINGSHI)
	  {
	   	if(xls[0]==0x03)												 /* 手指离开时的操作 */
		  {
		   	Icon = NONE_PRESS;
			  Menu_Timer();
		  }
	  }	 
	  else if(Icon == GENGDUO)	 /* 按了 更多 的按钮 */
	  {
	    if(xls[0]==0x03)												 /* 手指离开时的操作 */
	  	{
		    Icon = NONE_PRESS;
			  Menu_More();				
		  }
	  }	 
      else if(Icon == SULV)
	   {
		 if(xls[0]==0x03)												 /* 手指离开时的操作 */
		  {
		    Icon = NONE_PRESS;
	  		Menu_Rate();
		   }
	   } 
	    else if(Icon == KAISHI)
		  {
		   	if(xls[0]==0x03)
			 {
			  Icon = NONE_PRESS;
				VIEWTECH_71(12,273,50,359,73,273,50);    //on/off
				 RUN_FLAG = Runing; 
				TIME_COUNT0 = -1;
				 TIME_COUNT00 =-1;
				 TIME_COUNT = -1;
			 }		   
		 }
			else if(Icon == KAISHI1)
		  {
		   	if(xls[0]==0x03)
			 {
			  Icon = NONE_PRESS;
				VIEWTECH_71(12,273,96,359,119,273,96);    //on/off
				 RUN_FLAG1 = Runing1; 
					TIME_COUNT = -1;
				 TIME_COUNT1 =-1;
				 TIME_COUNT11 =-1;
			 }		   
		 }
			else if(Icon == KAISHI2)
		  {
		   	if(xls[0]==0x03)
			 {
			  Icon = NONE_PRESS;
				VIEWTECH_71(12,273,142,359,165,273,142);   //on/off
				 RUN_FLAG2 = Runing2; 
					TIME_COUNT = -1;
				 TIME_COUNT2 =-1;
				 TIME_COUNT22 =-1;
			 }		   
		 }
			else if(Icon == KAISHI3)
		  {
		   	if(xls[0]==0x03)
			 {
			  Icon = NONE_PRESS;
					VIEWTECH_71(12,273,188,359,210,273,188);    //on/off
				 RUN_FLAG3 = Runing3; 
				TIME_COUNT = -1;
				 TIME_COUNT3 =-1;
				 TIME_COUNT33 =-1;
			 }		   
		 }
   }
 }

else if((RUN_FLAG == Runing)||(RUN_FLAG1 == Runing1)||(RUN_FLAG2 == Runing2)||(RUN_FLAG3 == Runing3))
 {
	   if(Icon == NONE_PRESS)
	 	 {
			 if(XY[0]>322 && XY[1]>50 && XY[0]<359 && XY[1]<73)	   	/* 如果是运行后只能这里按开始检测按钮 */
			  {	
			   if(xls[0]==0x01)
				 {
				   VIEWTECH_71(11,322,50,359,73,322,50);     //如果是按下停止按钮
					  Icon = TINGZHI; 
				 }
			  }
				else if(XY[0]>273 && XY[1]>50 && XY[0]<310 && XY[1]<73)
				{
				 if(xls[0]==0x01)
				 {
				    VIEWTECH_71(13,273,50,310,73,273,50);      //如果是按下run按钮
					  Icon = RERUN; 
				 }
				}
				else if(XY[0]>322 && XY[1]>96 && XY[0]<359 && XY[1]<119)	   	/* 如果是运行后只能这里按开始检测按钮 */
			  {	
			   if(xls[0]==0x01)
				 {
				    VIEWTECH_71(11,322,96,359,119,322,96);      //如果是按下停止按钮
					  Icon = TINGZHI1; 
				 }
			  }
				else if(XY[0]>273 && XY[1]>96 && XY[0]<310 && XY[1]<119)
				{
				 if(xls[0]==0x01)
				 {
				    VIEWTECH_71(13,273,96,310,119,273,96);      //如果是按下run按钮
					  Icon = RERUN1; 
				 }
				}
				else if(XY[0]>322 && XY[1]>142 && XY[0]<359 && XY[1]<165)	   	/* 如果是运行后只能这里按开始检测按钮 */
			  {	
			   if(xls[0]==0x01)
				 {
				    VIEWTECH_71(11,322,142,359,165,322,142);      //如果是按下停止按钮
					  Icon = TINGZHI2; 
				 }
			  }
				else if(XY[0]>273 && XY[1]>142 && XY[0]<310 && XY[1]<165)
				{
				 if(xls[0]==0x01)
				 {
				    VIEWTECH_71(13,273,142,310,165,273,142);      //如果是按下run按钮
					  Icon = RERUN2; 
				 }
				}
				else if(XY[0]>322 && XY[1]>188 && XY[0]<359 && XY[1]<210)	   	/* 如果是运行后只能这里按开始检测按钮 */
			  {	
			   if(xls[0]==0x01)
				 {
				   VIEWTECH_71(11,322,188,359,210,322,188);     //如果是按下停止按钮
					  Icon = TINGZHI3; 
				 }
			  }
				else if(XY[0]>273 && XY[1]>188 && XY[0]<310 && XY[1]<210)
				{
				 if(xls[0]==0x01)
				 {
				    VIEWTECH_71(13,273,188,310,210,273,188);     //如果是按下run按钮
					  Icon = RERUN3; 
				 }
				}
		 }
	   else 
		 {
			 if(Icon == TINGZHI)	   	/* 如果是运行后只能这里按开始检测按钮 */
		  {
			 if(xls[0]==0x03)
			 {
			    Icon = NONE_PRESS;
				 VIEWTECH_71(25,273,50,359,73,273,50);  //ch2  run  stop				 
				  VIEWTECH_71(11,402,62,468,77,402,62);	                /* 显示暂停状态  */

				START_RUN = First_circle;
	      START_SYNC1 = SYNC1_First;
				RUN_FLAG = noRuning;
			 }
		  }
			else if(Icon == RERUN )
			{
				if(RUN_FLAG==Runing)
				{
					if(xls[0]==0x03)
				 {
			    Icon = NONE_PRESS;         
				  VIEWTECH_71(12,273,50,310,73,273,50);		  
					 
				START_RUN = First_circle;
	    START_SYNC1 = SYNC1_First;				 
			TIME_COUNT0 = -1;	
					 TIME_COUNT00 = -1;	
				BEC_Value_ch[0] = 0;		//将误码数清零，以便下次累加
					 BEC_Value_real[0] = 0;
				 }	
				}
				else
				{
					if(xls[0]==0x03)
				 {
							Icon = NONE_PRESS;
							VIEWTECH_71(12,273,50,359,73,273,50);    //on/off
					 RUN_FLAG = Runing; 
					TIME_COUNT0 =-1;
					 TIME_COUNT00 =-1;
				 }
				}			
			}
			else if(Icon == TINGZHI1)	   	/* 如果是运行后只能这里按开始检测按钮 */
		  {
			 if(xls[0]==0x03)
			 {
			    Icon = NONE_PRESS;
				 VIEWTECH_71(25,273,96,359,119,273,96);  //ch1  run  stop				 //25页开始停止按钮
				  VIEWTECH_71(11,402,108,468,123,402,108);	//11页暂停

//					VIEWTECH_98 (265,417,0x23,0x90,3,0x2e9f,0x39c8,HGANG1,3);
//				  Para_Ini();	
				START_RUN1 = First1_circle;
	      START_SYNC2 = SYNC2_First;
				RUN_FLAG1 = noRuning1;
			 }
		  }
			else if(Icon == RERUN1 )
			{
				if(RUN_FLAG1==Runing1)
				{
				if(xls[0]==0x03)
			 {
			    Icon = NONE_PRESS;         
				  VIEWTECH_71(12,273,96,310,119,273,96);		  
				 
		  START_RUN1 = First1_circle;
	    START_SYNC2 = SYNC2_First;	 
		  TIME_COUNT1 = -1;
			TIME_COUNT11 =-1;	
			BEC_Value_ch[1] = 0;		//将误码数清零，以便下次累加
				 BEC_Value_real[1] = 0;
			 }
		 }
     else	
		 {
			 if(xls[0]==0x03)
			 {
							Icon = NONE_PRESS;
							VIEWTECH_71(12,273,96,359,119,273,96);    //on/off
				 RUN_FLAG1 = Runing1; 
//				 TIME_COUNT = 0;
				 TIME_COUNT1 =-1;
				 TIME_COUNT11 =-1;
			 }
		 }			 
			}
			else if(Icon == TINGZHI2)	   	/* 如果是运行后只能这里按开始检测按钮 */
		  {
			 if(xls[0]==0x03)
			 {
			    Icon = NONE_PRESS;
				VIEWTECH_71(25,273,142,359,165,273,142);  //ch2  run  stop				 
				  VIEWTECH_71(11,402,154,468,169,402,154);	                /* 显示暂停状态  */
//					VIEWTECH_98 (265,417,0x23,0x90,3,0x2e9f,0x39c8,HGANG1,3);
//				  Para_Ini();	
				START_RUN2 = First2_circle;
	      START_SYNC3 = SYNC3_First;
				RUN_FLAG2 = noRuning2;
			 }
		  }
			else if(Icon == RERUN2 )
			{
				if(RUN_FLAG2==Runing2)
				{
				if(xls[0]==0x03)
			 {
			    Icon = NONE_PRESS;         
				  VIEWTECH_71(12,273,142,310,165,273,142);		  
			START_RUN2 = First2_circle;
	      START_SYNC3 = SYNC3_First;	 
		  TIME_COUNT2 = -1;
			TIME_COUNT22 =-1;		
			BEC_Value_ch[2] = 0;		//将误码数清零，以便下次累加
				 BEC_Value_real[2] = 0;
			 }
		 }
			else
			{
							if(xls[0]==0x03)
						 {
							Icon = NONE_PRESS;
							VIEWTECH_71(12,273,142,359,165,273,142);    //on/off
							 RUN_FLAG2 = Runing2; 
			//				 TIME_COUNT = 0;
							 TIME_COUNT2 =-1;
							 TIME_COUNT22 =-1;
						 }		
			}	
			}
			else if(Icon == TINGZHI3)	   	/* 如果是运行后只能这里按开始检测按钮 */
		  {
			 if(xls[0]==0x03)
			 {
			    Icon = NONE_PRESS;
				VIEWTECH_71(25,273,188,359,210,273,188);  //ch1  run  stop				 //25页开始停止按钮
				  VIEWTECH_71(11,402,200,468,215,402,200);	//11页暂停
//					VIEWTECH_98 (265,417,0x23,0x90,3,0x2e9f,0x39c8,HGANG1,3);
//				  Para_Ini();	
				START_RUN3 = First3_circle;
	      START_SYNC4 = SYNC4_First;
				RUN_FLAG3 = noRuning3;
			 }
		  }
			else if(Icon == RERUN3 )
			{
				if(RUN_FLAG3==Runing3)
				{
				if(xls[0]==0x03)
			 {
			    Icon = NONE_PRESS;         
				 VIEWTECH_71(12,273,188,310,210,273,188);	  
				 
		  START_RUN3 = First3_circle;
	      START_SYNC4 = SYNC4_First;	 
		  TIME_COUNT3 = -1;
			TIME_COUNT33 =-1;		
			BEC_Value_ch[3] = 0;		//将误码数清零，以便下次累加
				 BEC_Value_real[3] = 0;
			 }
		 }
			else
			{
						if(xls[0]==0x03)
						 {
							Icon = NONE_PRESS;
							VIEWTECH_71(12,273,188,359,210,273,188);    //on/off
							 RUN_FLAG3 = Runing3; 
			//				 TIME_COUNT = 0;
							 TIME_COUNT3 =-1;
							 TIME_COUNT33 =-1;
						 }	
			}	
			}
		}
	}
}



/*******************************************************************************
* Function Name  : Menu_UDP
* Description    : User Defined Pattern 用户码型自定义界面操作
* Input          : None
* Output         : None
* Return         : None
* Attention		 : None
*******************************************************************************/
void Menu_UDP(void)
{
		u8 udp_temp[8]={0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0};
   	udp_temp[0] = udp[curchan][0];
		udp_temp[1] = udp[curchan][1];
		udp_temp[2] = udp[curchan][2];
		udp_temp[3] = udp[curchan][3];
		udp_temp[4] = udp[curchan][4];
		udp_temp[5] = udp[curchan][5];
		udp_temp[6] = udp[curchan][6];
		udp_temp[7] = udp[curchan][7];
		
		uint32_t udp0_lcd = 0;
		uint32_t udp8_lcd = 0;
		uint32_t udp16_lcd = 0;
		uint32_t udp24_lcd = 0;
		
		uint32_t udp32_lcd = 0;
		uint32_t udp40_lcd = 0;
		uint32_t udp48_lcd = 0;
		uint32_t udp56_lcd = 0;
		u8 cover1 = 0;
		u8 cover2 = 0;
		u8 cover3 = 0;
		u8 cover4 = 0;
		
		VIEWTECH_70 (27);		
		User_defined_pattern();		
		while(1)					
	{
		 xls[1]=0;									 /* 坐标清0 */
		 xls[2]=0;
		 XY[0]=0;
		 XY[1]=0;

		while((((XY[0]>74 && XY[0]<207 && XY[1]>62 && XY[1]<217)==0)&&((XY[0]>1 && XY[0]<479 && XY[1]>229 && XY[1]<272)==0))==1)		/* 再次取坐标 */ 
		 {
		XY[0]=xls[1]<<8;
		XY[0]+=xls[2];
		XY[1]=xls[3]<<8;
		XY[1]+=xls[4];
		Delay_Ms(10);
		 }   	
		 
			if(XY[0]>74 && XY[1]>62 && XY[0]<207  && XY[1]<90 && xls[0]==0x03)		 //0~15位编码
		 {
			 VIEWTECH_7C01 (210,66,16,0x00);
			 VIEWTECH_71(27,74,62,207,90,74,62);      //点击输入框，框变空白
//			 VIEWTECH_7C04 (450,70,16,0x40,133,118,0x22,0x90,3,0x001f,0x39c8);   //调出小键盘
			 while(1)
			 {

				 if(biaoji == Virtual_keyboard)
				 { 
					 if(xls[3]>=48 && xls[3]<=49 && xls[4]>=48 && xls[4]<=49 && xls[5]>=48 && xls[5]<=49 && xls[6]>=48 && xls[6]<=49 && xls[7]>=48 && xls[7]<=49 && 
							xls[8]>=48 && xls[8]<=49 && xls[9]>=48 && xls[9]<=49 && xls[10]>=48 && xls[10]<=49 && xls[11]>=48 && xls[11]<=49 && xls[12]>=48 && xls[12]<=49 && 
							xls[13]>=48 && xls[13]<=49 && xls[14]>=48 && xls[14]<=49 && xls[15]>=48 && xls[15]<=49 && xls[16]>=48 && xls[16]<=49 && xls[17]>=48 && xls[17]<=49 && xls[18]>=48 && xls[18]<=49 ) //判断是否是二进制0和1
						{ 
							if(xls[2]==16)                              /*如果不是输入16位，显示输入错误*/
							{
							 udp_temp[0] = (xls[3]-48)*128+(xls[4]-48)*64+(xls[5]-48)*32+(xls[6]-48)*16+(xls[7]-48)*8+(xls[8]-48)*4+(xls[9]-48)*2+(xls[10]-48);
							 udp_temp[1] = (xls[11]-48)*128+(xls[12]-48)*64+(xls[13]-48)*32+(xls[14]-48)*16+(xls[15]-48)*8+(xls[16]-48)*4+(xls[17]-48)*2+(xls[18]-48);							
               cover1 = 1;
 
								udp0_lcd = ((udp_temp[0]&0x01)*1+((udp_temp[0]>>1)&0x01)*10+((udp_temp[0]>>2)&0x01)*100+((udp_temp[0]>>3)&0x01)*1000
					   +((udp_temp[0]>>4)&0x01)*10000+((udp_temp[0]>>5)&0x01)*100000+((udp_temp[0]>>6)&0x01)*1000000+((udp_temp[0]>>7)&0x01)*10000000);
							//VIEWTECH_A01 (133,118,0x80e2,0x001f,0xffff,udp0_lcd);	
								
								udp8_lcd = ((udp_temp[1]&0x01)*1+((udp_temp[1]>>1)&0x01)*10+((udp_temp[1]>>2)&0x01)*100+((udp_temp[1]>>3)&0x01)*1000
		         +((udp_temp[1]>>4)&0x01)*10000+((udp_temp[1]>>5)&0x01)*100000+((udp_temp[1]>>6)&0x01)*1000000+((udp_temp[1]>>7)&0x01)*10000000);			
								//VIEWTECH_A01 (240,118,0x80e2,0x001f,0xffff,udp8_lcd);	 
									
								//VIEWTECH_71(27,450,130,800,400,450,130); 
								
								VIEWTECH_70 (27);		
								User_defined_pattern();
								if(cover1==1)
								{
									VIEWTECH_A01 (76,66,0x80e1,0x001f,0xffff,udp0_lcd);
								  VIEWTECH_A01 (140,66,0x80e1,0x001f,0xffff,udp8_lcd);	
								}	
                if(cover2==1)
								{
									VIEWTECH_A01 (76,108,0x80e1,0x001f,0xffff,udp16_lcd);
								  VIEWTECH_A01 (140,108,0x80e1,0x001f,0xffff,udp24_lcd);
								}
                 if(cover3==1)
								 {
									 VIEWTECH_A01 (76,150,0x80e1,0x001f,0xffff,udp32_lcd);
								   VIEWTECH_A01 (140,150,0x80e1,0x001f,0xffff,udp40_lcd);
								 }
								if(cover4 == 1)
								{
									VIEWTECH_A01 (76,192,0x80e1,0x001f,0xffff,udp48_lcd);
								  VIEWTECH_A01 (140,192,0x80e1,0x001f,0xffff,udp56_lcd);
								}	
								
							 biaoji = No_touch;
							}
							else
							{
								VIEWTECH_70 (27);		
								User_defined_pattern();
								if(cover1==1)
								{
									VIEWTECH_A01 (76,66,0x80e1,0x001f,0xffff,udp0_lcd);
								  VIEWTECH_A01 (140,66,0x80e1,0x001f,0xffff,udp8_lcd);	
								}	
                if(cover2==1)
								{
									VIEWTECH_A01 (76,108,0x80e1,0x001f,0xffff,udp16_lcd);
								  VIEWTECH_A01 (140,108,0x80e1,0x001f,0xffff,udp24_lcd);
								}
                 if(cover3==1)
								 {
									 VIEWTECH_A01 (76,150,0x80e1,0x001f,0xffff,udp32_lcd);
								   VIEWTECH_A01 (140,150,0x80e1,0x001f,0xffff,udp40_lcd);
								 }
								if(cover4 == 1)
								{
									VIEWTECH_A01 (76,192,0x80e1,0x001f,0xffff,udp48_lcd);
								  VIEWTECH_A01 (140,192,0x80e1,0x001f,0xffff,udp56_lcd);
								}	
								VIEWTECH_71(27,74,62,207,90,74,62);
							  VIEWTECH_98 (76,66,0x21,0x90,1,0xf800,0xffff,ERR,12);		
							}
							
						}
					else
						{
							VIEWTECH_70 (27);		
							User_defined_pattern();
							if(cover1==1)
								{
									VIEWTECH_A01 (76,66,0x80e1,0x001f,0xffff,udp0_lcd);
								  VIEWTECH_A01 (140,66,0x80e1,0x001f,0xffff,udp8_lcd);	
								}	
                if(cover2==1)
								{
									VIEWTECH_A01 (76,108,0x80e1,0x001f,0xffff,udp16_lcd);
								  VIEWTECH_A01 (140,108,0x80e1,0x001f,0xffff,udp24_lcd);
								}
                 if(cover3==1)
								 {
									 VIEWTECH_A01 (76,150,0x80e1,0x001f,0xffff,udp32_lcd);
								   VIEWTECH_A01 (140,150,0x80e1,0x001f,0xffff,udp40_lcd);
								 }
								if(cover4 == 1)
								{
									VIEWTECH_A01 (76,192,0x80e1,0x001f,0xffff,udp48_lcd);
								  VIEWTECH_A01 (140,192,0x80e1,0x001f,0xffff,udp56_lcd);
								}	
							VIEWTECH_71(27,74,62,207,90,74,62);
							VIEWTECH_98 (76,66,0x21,0x90,1,0xf800,0xffff,ERR,12);		 /*如果输入的不是0和1，显示输入错误*/
						}
					break;
				 }
			 }
		 }
			else if(XY[0]>74 && XY[1]>104 && XY[0]<207  && XY[1]<132 && xls[0]==0x03 )		      //16~31位编码
		 {
			 VIEWTECH_7C01 (210,66,16,0x00);
			 VIEWTECH_71(27,74,104,207,132,74,104);
//			 VIEWTECH_7C04 (450,70,16,0x40,133,190,0x22,0x90,3,0x001f,0x39c8);
			 while(1)
			 {

				 if(biaoji == Virtual_keyboard)
				 { 
					 if(xls[3]>=48 && xls[3]<=49 && xls[4]>=48 && xls[4]<=49 && xls[5]>=48 && xls[5]<=49 && xls[6]>=48 && xls[6]<=49 && xls[7]>=48 && xls[7]<=49 && 
							xls[8]>=48 && xls[8]<=49 && xls[9]>=48 && xls[9]<=49 && xls[10]>=48 && xls[10]<=49 && xls[11]>=48 && xls[11]<=49 && xls[12]>=48 && xls[12]<=49 && 
							xls[13]>=48 && xls[13]<=49 && xls[14]>=48 && xls[14]<=49 && xls[15]>=48 && xls[15]<=49 && xls[16]>=48 && xls[16]<=49 && xls[17]>=48 && xls[17]<=49 && xls[18]>=48 && xls[18]<=49 ) //判断是否是二进制0和1
						{ 
							if(xls[2]==16)
							{
							 udp_temp[2] = (xls[3]-48)*128+(xls[4]-48)*64+(xls[5]-48)*32+(xls[6]-48)*16+(xls[7]-48)*8+(xls[8]-48)*4+(xls[9]-48)*2+(xls[10]-48);
							 udp_temp[3] = (xls[11]-48)*128+(xls[12]-48)*64+(xls[13]-48)*32+(xls[14]-48)*16+(xls[15]-48)*8+(xls[16]-48)*4+(xls[17]-48)*2+(xls[18]-48);
               cover2=1;
								udp16_lcd = ((udp_temp[2]&0x01)*1+((udp_temp[2]>>1)&0x01)*10+((udp_temp[2]>>2)&0x01)*100+((udp_temp[2]>>3)&0x01)*1000
					   +((udp_temp[2]>>4)&0x01)*10000+((udp_temp[2]>>5)&0x01)*100000+((udp_temp[2]>>6)&0x01)*1000000+((udp_temp[2]>>7)&0x01)*10000000);					
						//VIEWTECH_A01 (133,219,0x80e2,0x001f,0xffff,udp16_lcd);	 					
					
						udp24_lcd = ((udp_temp[3]&0x01)*1+((udp_temp[3]>>1)&0x01)*10+((udp_temp[3]>>2)&0x01)*100+((udp_temp[3]>>3)&0x01)*1000
					    +((udp_temp[3]>>4)&0x01)*10000+((udp_temp[3]>>5)&0x01)*100000+((udp_temp[3]>>6)&0x01)*1000000+((udp_temp[3]>>7)&0x01)*10000000);					
								//VIEWTECH_A01 (240,219,0x80e2,0x001f,0xffff,udp24_lcd);	
								VIEWTECH_70 (27);		
								User_defined_pattern();
								if(cover1==1)
								{
									VIEWTECH_A01 (76,66,0x80e1,0x001f,0xffff,udp0_lcd);
								  VIEWTECH_A01 (140,66,0x80e1,0x001f,0xffff,udp8_lcd);	
								}	
                if(cover2==1)
								{
									VIEWTECH_A01 (76,108,0x80e1,0x001f,0xffff,udp16_lcd);
								  VIEWTECH_A01 (140,108,0x80e1,0x001f,0xffff,udp24_lcd);
								}
                 if(cover3==1)
								 {
									 VIEWTECH_A01 (76,150,0x80e1,0x001f,0xffff,udp32_lcd);
								   VIEWTECH_A01 (140,150,0x80e1,0x001f,0xffff,udp40_lcd);
								 }
								if(cover4 == 1)
								{
									VIEWTECH_A01 (76,192,0x80e1,0x001f,0xffff,udp48_lcd);
								  VIEWTECH_A01 (140,192,0x80e1,0x001f,0xffff,udp56_lcd);
								}	
								
							 biaoji = No_touch;
							}
							else
							{
								VIEWTECH_70 (27);		
								User_defined_pattern();
								if(cover1==1)
								{
									VIEWTECH_A01 (76,66,0x80e1,0x001f,0xffff,udp0_lcd);
								  VIEWTECH_A01 (140,66,0x80e1,0x001f,0xffff,udp8_lcd);	
								}	
                if(cover2==1)
								{
									VIEWTECH_A01 (76,108,0x80e1,0x001f,0xffff,udp16_lcd);
								  VIEWTECH_A01 (140,108,0x80e1,0x001f,0xffff,udp24_lcd);
								}
                 if(cover3==1)
								 {
									 VIEWTECH_A01 (76,150,0x80e1,0x001f,0xffff,udp32_lcd);
								   VIEWTECH_A01 (140,150,0x80e1,0x001f,0xffff,udp40_lcd);
								 }
								if(cover4 == 1)
								{
									VIEWTECH_A01 (76,192,0x80e1,0x001f,0xffff,udp48_lcd);
								  VIEWTECH_A01 (140,192,0x80e1,0x001f,0xffff,udp56_lcd);
								}	
								VIEWTECH_71(27,74,104,207,132,74,104);
							 VIEWTECH_98 (76,108,0x21,0x90,1,0xf800,0xffff,ERR,12);		 /*输入错误*/
							}
							
						}
					else
						{
							VIEWTECH_70 (27);		
								User_defined_pattern();
							if(cover1==1)
								{
									VIEWTECH_A01 (76,66,0x80e1,0x001f,0xffff,udp0_lcd);
								  VIEWTECH_A01 (140,66,0x80e1,0x001f,0xffff,udp8_lcd);	
								}	
                if(cover2==1)
								{
									VIEWTECH_A01 (76,108,0x80e1,0x001f,0xffff,udp16_lcd);
								  VIEWTECH_A01 (140,108,0x80e1,0x001f,0xffff,udp24_lcd);
								}
                 if(cover3==1)
								 {
									 VIEWTECH_A01 (76,150,0x80e1,0x001f,0xffff,udp32_lcd);
								   VIEWTECH_A01 (140,150,0x80e1,0x001f,0xffff,udp40_lcd);
								 }
								if(cover4 == 1)
								{
									VIEWTECH_A01 (76,192,0x80e1,0x001f,0xffff,udp48_lcd);
								  VIEWTECH_A01 (140,192,0x80e1,0x001f,0xffff,udp56_lcd);
								}	
							VIEWTECH_71(27,74,104,207,132,74,104);
							VIEWTECH_98 (76,108,0x21,0x90,1,0xf800,0xffff,ERR,12);		 /*输入错误*/
						}
					break;
				 }
			 }
		 }
		else if(XY[0]>74 && XY[1]>146 && XY[0]<207  && XY[1]<174 && xls[0]==0x03 )		  //32~47位编码
		 {
			 VIEWTECH_7C01 (210,66,16,0x00);
			 VIEWTECH_71(27,74,146,207,174,74,146);
//			 VIEWTECH_7C04 (450,70,16,0x40,133,264,0x22,0x90,3,0x001f,0x39c8);
			 while(1)
			 {   
				 if(biaoji == Virtual_keyboard)
				 { 
					 if(xls[3]>=48 && xls[3]<=49 && xls[4]>=48 && xls[4]<=49 && xls[5]>=48 && xls[5]<=49 && xls[6]>=48 && xls[6]<=49 && xls[7]>=48 && xls[7]<=49 && 
							xls[8]>=48 && xls[8]<=49 && xls[9]>=48 && xls[9]<=49 && xls[10]>=48 && xls[10]<=49 && xls[11]>=48 && xls[11]<=49 && xls[12]>=48 && xls[12]<=49 && 
							xls[13]>=48 && xls[13]<=49 && xls[14]>=48 && xls[14]<=49 && xls[15]>=48 && xls[15]<=49 && xls[16]>=48 && xls[16]<=49 && xls[17]>=48 && xls[17]<=49 && xls[18]>=48 && xls[18]<=49 ) //判断是否是二进制0和1
						{ 
							if(xls[2]==16)
							{
							 udp_temp[4] = (xls[3]-48)*128+(xls[4]-48)*64+(xls[5]-48)*32+(xls[6]-48)*16+(xls[7]-48)*8+(xls[8]-48)*4+(xls[9]-48)*2+(xls[10]-48);
							 udp_temp[5] = (xls[11]-48)*128+(xls[12]-48)*64+(xls[13]-48)*32+(xls[14]-48)*16+(xls[15]-48)*8+(xls[16]-48)*4+(xls[17]-48)*2+(xls[18]-48);
								cover3=1;
								udp32_lcd = ((udp_temp[4]&0x01)*1+((udp_temp[4]>>1)&0x01)*10+((udp_temp[4]>>2)&0x01)*100+((udp_temp[4]>>3)&0x01)*1000
					   +((udp_temp[4]>>4)&0x01)*10000+((udp_temp[4]>>5)&0x01)*100000+((udp_temp[4]>>6)&0x01)*1000000+((udp_temp[4]>>7)&0x01)*10000000);					
						//VIEWTECH_A01 (133,219,0x80e2,0x001f,0xffff,udp16_lcd);	 					
					
						udp40_lcd = ((udp_temp[5]&0x01)*1+((udp_temp[5]>>1)&0x01)*10+((udp_temp[5]>>2)&0x01)*100+((udp_temp[5]>>3)&0x01)*1000
					    +((udp_temp[5]>>4)&0x01)*10000+((udp_temp[5]>>5)&0x01)*100000+((udp_temp[5]>>6)&0x01)*1000000+((udp_temp[5]>>7)&0x01)*10000000);					
								//VIEWTECH_A01 (240,219,0x80e2,0x001f,0xffff,udp24_lcd);	
								VIEWTECH_70 (27);		
								User_defined_pattern();
								if(cover1==1)
								{
									VIEWTECH_A01 (76,66,0x80e1,0x001f,0xffff,udp0_lcd);
								  VIEWTECH_A01 (140,66,0x80e1,0x001f,0xffff,udp8_lcd);	
								}	
                if(cover2==1)
								{
									VIEWTECH_A01 (76,108,0x80e1,0x001f,0xffff,udp16_lcd);
								  VIEWTECH_A01 (140,108,0x80e1,0x001f,0xffff,udp24_lcd);
								}
                 if(cover3==1)
								 {
									 VIEWTECH_A01 (76,150,0x80e1,0x001f,0xffff,udp32_lcd);
								   VIEWTECH_A01 (140,150,0x80e1,0x001f,0xffff,udp40_lcd);
								 }
								if(cover4 == 1)
								{
									VIEWTECH_A01 (76,192,0x80e1,0x001f,0xffff,udp48_lcd);
								  VIEWTECH_A01 (140,192,0x80e1,0x001f,0xffff,udp56_lcd);
								}	
								
							 biaoji = No_touch;
							}
							else
							{
								VIEWTECH_70 (27);		
								User_defined_pattern();
								if(cover1==1)
								{
									VIEWTECH_A01 (76,66,0x80e1,0x001f,0xffff,udp0_lcd);
								  VIEWTECH_A01 (140,66,0x80e1,0x001f,0xffff,udp8_lcd);	
								}	
                if(cover2==1)
								{
									VIEWTECH_A01 (76,108,0x80e1,0x001f,0xffff,udp16_lcd);
								  VIEWTECH_A01 (140,108,0x80e1,0x001f,0xffff,udp24_lcd);
								}
                 if(cover3==1)
								 {
									 VIEWTECH_A01 (76,150,0x80e1,0x001f,0xffff,udp32_lcd);
								   VIEWTECH_A01 (140,150,0x80e1,0x001f,0xffff,udp40_lcd);
								 }
								if(cover4 == 1)
								{
									VIEWTECH_A01 (76,192,0x80e1,0x001f,0xffff,udp48_lcd);
								  VIEWTECH_A01 (140,192,0x80e1,0x001f,0xffff,udp56_lcd);
								}	
								 VIEWTECH_71(27,74,146,207,174,74,146);
							 VIEWTECH_98 (76,150,0x21,0x90,1,0xf800,0xffff,ERR,12);		 /*输入错误*/
							}
							
						}
					else
						{
							VIEWTECH_70 (27);		
								User_defined_pattern();
							if(cover1==1)
								{
									VIEWTECH_A01 (76,66,0x80e1,0x001f,0xffff,udp0_lcd);
								  VIEWTECH_A01 (140,66,0x80e1,0x001f,0xffff,udp8_lcd);	
								}	
                if(cover2==1)
								{
									VIEWTECH_A01 (76,108,0x80e1,0x001f,0xffff,udp16_lcd);
								  VIEWTECH_A01 (140,108,0x80e1,0x001f,0xffff,udp24_lcd);
								}
                 if(cover3==1)
								 {
									 VIEWTECH_A01 (76,150,0x80e1,0x001f,0xffff,udp32_lcd);
								   VIEWTECH_A01 (140,150,0x80e1,0x001f,0xffff,udp40_lcd);
								 }
								if(cover4 == 1)
								{
									VIEWTECH_A01 (76,192,0x80e1,0x001f,0xffff,udp48_lcd);
								  VIEWTECH_A01 (140,192,0x80e1,0x001f,0xffff,udp56_lcd);
								}	
								 VIEWTECH_71(27,74,146,207,174,74,146);
							VIEWTECH_98 (76,150,0x21,0x90,1,0xf800,0xffff,ERR,12);		 /*输入错误*/
						}
					break;
				}
			}
		}
		 else if(XY[0]>74 && XY[1]>188 && XY[0]<207  && XY[1]<220 && xls[0]==0x03 )		 //48~63位编码
		 {
			 VIEWTECH_7C01 (210,66,16,0x00);
			 VIEWTECH_71(27,74,188,207,220,74,188);
//			 VIEWTECH_7C04 (450,70,16,0x40,133,338,0x22,0x90,3,0x001f,0x39c8);
			 while(1)
			 {

				 if(biaoji == Virtual_keyboard)
				 { 
					 if(xls[3]>=48 && xls[3]<=49 && xls[4]>=48 && xls[4]<=49 && xls[5]>=48 && xls[5]<=49 && xls[6]>=48 && xls[6]<=49 && xls[7]>=48 && xls[7]<=49 && 
							xls[8]>=48 && xls[8]<=49 && xls[9]>=48 && xls[9]<=49 && xls[10]>=48 && xls[10]<=49 && xls[11]>=48 && xls[11]<=49 && xls[12]>=48 && xls[12]<=49 && 
							xls[13]>=48 && xls[13]<=49 && xls[14]>=48 && xls[14]<=49 && xls[15]>=48 && xls[15]<=49 && xls[16]>=48 && xls[16]<=49 && xls[17]>=48 && xls[17]<=49 && xls[18]>=48 && xls[18]<=49 ) //判断是否是二进制0和1
						{ 
							if(xls[2]==16)
							{
							 udp_temp[6] = (xls[3]-48)*128+(xls[4]-48)*64+(xls[5]-48)*32+(xls[6]-48)*16+(xls[7]-48)*8+(xls[8]-48)*4+(xls[9]-48)*2+(xls[10]-48);
							 udp_temp[7] = (xls[11]-48)*128+(xls[12]-48)*64+(xls[13]-48)*32+(xls[14]-48)*16+(xls[15]-48)*8+(xls[16]-48)*4+(xls[17]-48)*2+(xls[18]-48);
								cover4 = 1;
								udp48_lcd = ((udp_temp[6]&0x01)*1+((udp_temp[6]>>1)&0x01)*10+((udp_temp[6]>>2)&0x01)*100+((udp_temp[6]>>3)&0x01)*1000
					   +((udp_temp[6]>>4)&0x01)*10000+((udp_temp[6]>>5)&0x01)*100000+((udp_temp[6]>>6)&0x01)*1000000+((udp_temp[6]>>7)&0x01)*10000000);					
						//VIEWTECH_A01 (133,219,0x80e2,0x001f,0xffff,udp16_lcd);	 					
					
						udp56_lcd = ((udp_temp[7]&0x01)*1+((udp_temp[7]>>1)&0x01)*10+((udp_temp[7]>>2)&0x01)*100+((udp_temp[7]>>3)&0x01)*1000
					    +((udp_temp[7]>>4)&0x01)*10000+((udp_temp[7]>>5)&0x01)*100000+((udp_temp[7]>>6)&0x01)*1000000+((udp_temp[7]>>7)&0x01)*10000000);					
								//VIEWTECH_A01 (240,219,0x80e2,0x001f,0xffff,udp24_lcd);	
								VIEWTECH_70 (27);		
								User_defined_pattern();
								if(cover1==1)
								{
									VIEWTECH_A01 (76,66,0x80e1,0x001f,0xffff,udp0_lcd);
								  VIEWTECH_A01 (140,66,0x80e1,0x001f,0xffff,udp8_lcd);	
								}	
                if(cover2==1)
								{
									VIEWTECH_A01 (76,108,0x80e1,0x001f,0xffff,udp16_lcd);
								  VIEWTECH_A01 (140,108,0x80e1,0x001f,0xffff,udp24_lcd);
								}
                 if(cover3==1)
								 {
									 VIEWTECH_A01 (76,150,0x80e1,0x001f,0xffff,udp32_lcd);
								   VIEWTECH_A01 (140,150,0x80e1,0x001f,0xffff,udp40_lcd);
								 }
								if(cover4 == 1)
								{
									VIEWTECH_A01 (76,192,0x80e1,0x001f,0xffff,udp48_lcd);
								  VIEWTECH_A01 (140,192,0x80e1,0x001f,0xffff,udp56_lcd);
								}	
								
							 biaoji = No_touch;
							}
							else
							{
								VIEWTECH_70 (27);		
								User_defined_pattern();
								if(cover1==1)
								{
									VIEWTECH_A01 (76,66,0x80e1,0x001f,0xffff,udp0_lcd);
								  VIEWTECH_A01 (140,66,0x80e1,0x001f,0xffff,udp8_lcd);	
								}	
                if(cover2==1)
								{
									VIEWTECH_A01 (76,108,0x80e1,0x001f,0xffff,udp16_lcd);
								  VIEWTECH_A01 (140,108,0x80e1,0x001f,0xffff,udp24_lcd);
								}
                 if(cover3==1)
								 {
									 VIEWTECH_A01 (76,150,0x80e1,0x001f,0xffff,udp32_lcd);
								   VIEWTECH_A01 (140,150,0x80e1,0x001f,0xffff,udp40_lcd);
								 }
								if(cover4 == 1)
								{
									VIEWTECH_A01 (76,192,0x80e1,0x001f,0xffff,udp48_lcd);
								  VIEWTECH_A01 (140,192,0x80e1,0x001f,0xffff,udp56_lcd);
								}	
								VIEWTECH_71(27,74,188,207,220,74,188);
							 VIEWTECH_98 (76,192,0x21,0x90,1,0xf800,0xffff,ERR,12);		 /*输入错误*/
							}
							
						}
					else
						{
							VIEWTECH_70 (27);		
								User_defined_pattern();
							if(cover1==1)
								{
									VIEWTECH_A01 (76,66,0x80e1,0x001f,0xffff,udp0_lcd);
								  VIEWTECH_A01 (140,66,0x80e1,0x001f,0xffff,udp8_lcd);	
								}	
                if(cover2==1)
								{
									VIEWTECH_A01 (76,108,0x80e1,0x001f,0xffff,udp16_lcd);
								  VIEWTECH_A01 (140,108,0x80e1,0x001f,0xffff,udp24_lcd);
								}
                 if(cover3==1)
								 {
									 VIEWTECH_A01 (76,150,0x80e1,0x001f,0xffff,udp32_lcd);
								   VIEWTECH_A01 (140,150,0x80e1,0x001f,0xffff,udp40_lcd);
								 }
								if(cover4 == 1)
								{
									VIEWTECH_A01 (76,192,0x80e1,0x001f,0xffff,udp48_lcd);
								  VIEWTECH_A01 (140,192,0x80e1,0x001f,0xffff,udp56_lcd);
								}	
								VIEWTECH_71(27,74,188,207,220,74,188);
							VIEWTECH_98 (76,192,0x21,0x90,1,0xf800,0xffff,ERR,12);		 /*输入错误*/
						}
					break;
				 }
			 }
		 }
		 //else if(XY[0]>659 && XY[1]>406 && XY[0]<791  && XY[1]<465 )	
			else if(XY[0]>395 && XY[1]>230 && XY[0]<476  && XY[1]<266 )			 
		 { 
			 VIEWTECH_71(28,395,230,476,266,395,230);
			 udp[curchan][0] = udp_temp[0];
			 udp[curchan][1] = udp_temp[1];
			 udp[curchan][2] = udp_temp[2];
			 udp[curchan][3] = udp_temp[3];
			 udp[curchan][4] = udp_temp[4];
		 	 udp[curchan][5] = udp_temp[5];	
			 udp[curchan][6] = udp_temp[6];	
			 udp[curchan][7] = udp_temp[7];				 
			 Delay_Ms(250);	
			 break;
		 }
		 //else if(XY[0]>14 && XY[1]>406 && XY[0]<146  && XY[1]<465 )		 
		 else if(XY[0]>8 && XY[1]>230 && XY[0]<90  && XY[1]<266 )
		 { 
			 VIEWTECH_71(28,8,230,90,266,8,230);
			 Delay_Ms(250);	
			 break;
		 }
	}
}

/*******************************************************************************
* Function Name  : Menu_Pattern
* Description    : 码型选择界面操作
* Input          : None
* Output         : None
* Return         : None
* Attention		 : None
*******************************************************************************/
void Menu_Pattern(void)					/* 码型选择界面 */
{  
	   u8 pattern_init_ch1, pattern_init_ch2,  pattern_init_ch3,  pattern_init_ch4;   
	
	       VIEWTECH_70 (3);						        	/* 切换到码型选择界面*/	
	       switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(4,1,32,62,78,1,32);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(4,1,78,62,124,1,78);  /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(4,1,124,62,170,1,124);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 3:
					VIEWTECH_71(4,1,170,62,218,1,170);   /* 上一轮选中的码型变色*/
				   break; 
				 }
 		    
				switch(pattern[curchan])
				{
				 case PRBS7:
				 VIEWTECH_71(4,85,54,189,95,85,54);   /* 上一轮选中的码型变色*/
				 break;
		
				 case PRBS9:
				 VIEWTECH_71(4,206,54,310,95,206,54);
				 break;
		
				 case PRBS15:
				 VIEWTECH_71(4,326,54,429,95,326,54);
				 break;
				
				 case PRBS23:
				 VIEWTECH_71(4,85,108,189,149,85,108);
				 break;
				 
				 case PRBS31:
				 VIEWTECH_71(4,206,108,310,149,206,108);
				 break;
				 
				 case PRBS58://
				 VIEWTECH_71(4,326,108,429,149,326,108);
				 break;
							
				 case PRBS9_5://
				 VIEWTECH_71(4,85,164,189,205,85,164);
				 break;
				 
				 case USER_DEF://USER_DEF:
				 VIEWTECH_71(4,206,164,310,205,206,164);
				 break;				 
				}
				pattern_init_ch1 = pattern[0];
				pattern_init_ch2 = pattern[1];
				pattern_init_ch3 = pattern[2];
				pattern_init_ch4 = pattern[3];
				
			 while(1)
			 {
				 xls[1]=0;									 /* 坐标清0 */
				 xls[2]=0;
				 XY[0]=0;
				 XY[1]=0;
		
//		      while((((XY[0]>1 && XY[0]<799 && XY[1]>1 && XY[1]<380)==0)&&((XY[0]>1 && XY[0]<799 && XY[1]>418 && XY[1]<479)==0))==1)		/* 再次取坐标 */ 
				while(((XY[0]>1 && XY[0]<479 && XY[1]>1 && XY[1]<272)==0))		/* 再次取坐标 */ 
			     {
					XY[0]=xls[1]<<8;
					XY[0]+=xls[2];
					XY[1]=xls[3]<<8;
					XY[1]+=xls[4];
					Delay_Ms(10);
			     }   	
		
				if(XY[0]>0 && XY[1]>32 && XY[0]<62  && XY[1]<218 )		  /* 按了通道选择 */
				{
					if(XY[0]>0 && XY[1]>32 && XY[0]<62  && XY[1]<78 )
					{
						curchan = 0;
					}
					else if(XY[0]>0 && XY[1]>78 && XY[0]<62  && XY[1]<124 )
					{
						curchan = 1;
					}
					else if(XY[0]>0 && XY[1]>124 && XY[0]<62  && XY[1]<170 )
					{
						curchan = 2;
					}
					else if(XY[0]>0 && XY[1]>170 && XY[0]<62  && XY[1]<218 )
					{
						curchan = 3;
					}
					Delay_Ms(200);
//					Menu_Pattern();
						VIEWTECH_70 (3);						        	/* 切换到码型选择界面*/	
	       switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(4,1,32,62,78,1,32);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(4,1,78,62,124,1,78);  /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(4,1,124,62,170,1,124);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 3:
					VIEWTECH_71(4,1,170,62,218,1,170);   /* 上一轮选中的码型变色*/
				   break; 
				 }
					
					switch(pattern[curchan])
				{
				 case PRBS7:
				 VIEWTECH_71(4,85,54,189,95,85,54);   /* 上一轮选中的码型变色*/
				 break;
		
				 case PRBS9:
				 VIEWTECH_71(4,206,54,310,95,206,54);
				 break;
		
				 case PRBS15:
				 VIEWTECH_71(4,326,54,429,95,326,54);
				 break;
				
				 case PRBS23:
				 VIEWTECH_71(4,85,108,189,149,85,108);
				 break;
				 
				 case PRBS31:
				 VIEWTECH_71(4,206,108,310,149,206,108);
				 break;
				 
				 case PRBS58://
				 VIEWTECH_71(4,326,108,429,149,326,108);
				 break;
							
				 case PRBS9_5://
				 VIEWTECH_71(4,85,164,189,205,85,164);
				 break;
				 
				 case USER_DEF://USER_DEF:
				 VIEWTECH_71(4,206,164,310,205,206,164);
				 break;				 
				}
				}
				
				
				else if(XY[0]>85 && XY[1]>54 && XY[0]<189  && XY[1]<95 )		  /* 按了PRBS7 */
				{
				    //PRBS7
					VIEWTECH_70 (3);						        	/* 切换到码型选择界面*/	
	       switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(4,1,32,62,78,1,32);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(4,1,78,62,124,1,78);  /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(4,1,124,62,170,1,124);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 3:
					VIEWTECH_71(4,1,170,62,218,1,170);   /* 上一轮选中的码型变色*/
				   break; 
				 }
					VIEWTECH_71(4,85,54,189,95,85,54);                     /* 选中的码型变色*/
					pattern[curchan] = PRBS7;
					Delay_Ms(250);
				 //break;
				}
				else if(XY[0]>206 && XY[1]>54 && XY[0]<310  && XY[1]<95 )	  /* 按了PRBS9 */
				{
				    //PRBS9												
				VIEWTECH_70 (3);						        	/* 切换到码型选择界面*/	
	       switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(4,1,32,62,78,1,32);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(4,1,78,62,124,1,78);  /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(4,1,124,62,170,1,124);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 3:
					VIEWTECH_71(4,1,170,62,218,1,170);   /* 上一轮选中的码型变色*/
				   break; 
				 }					
					VIEWTECH_71(4,206,54,310,95,206,54);
				  pattern[curchan] = PRBS9;
					Delay_Ms(250);
          //break;				 
				}
				else if(XY[0]>326 && XY[1]>54 && XY[0]<429  && XY[1]<95 )	  /* 按了PRBS15 */
				{
				    //PRBS15
				VIEWTECH_70 (3);						        	/* 切换到码型选择界面*/	
	       switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(4,1,32,62,78,1,32);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(4,1,78,62,124,1,78);  /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(4,1,124,62,170,1,124);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 3:
					VIEWTECH_71(4,1,170,62,218,1,170);   /* 上一轮选中的码型变色*/
				   break; 
				 }					
			  	VIEWTECH_71(4,326,54,429,95,326,54);
		      pattern[curchan] = PRBS15;
				Delay_Ms(250);
				 //break;
				}
				else if(XY[0]>85 && XY[1]>108 && XY[0]<189  && XY[1]<149 )	  /* 按了PRBS23 */
				{
				    //PRBS23
					VIEWTECH_70 (3);						        	/* 切换到码型选择界面*/	
	       switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(4,1,32,62,78,1,32);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(4,1,78,62,124,1,78);  /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(4,1,124,62,170,1,124);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 3:
					VIEWTECH_71(4,1,170,62,218,1,170);   /* 上一轮选中的码型变色*/
				   break; 
				 }
					VIEWTECH_71(4,85,108,189,149,85,108);
		      pattern[curchan] = PRBS23;
					Delay_Ms(250);	
          //break;				 
				}
				else if(XY[0]>206 && XY[1]>108 && XY[0]<310  && XY[1]<149 )	  /* 按了PRBS31 */
				{
				    //PRBS31												
					VIEWTECH_70 (3);						        	/* 切换到码型选择界面*/	
	       switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(4,1,32,62,78,1,32);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(4,1,78,62,124,1,78);  /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(4,1,124,62,170,1,124);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 3:
					VIEWTECH_71(4,1,170,62,218,1,170);   /* 上一轮选中的码型变色*/
				   break; 
				 }
					VIEWTECH_71(4,206,108,310,149,206,108);
				  pattern[curchan] = PRBS31;
					Delay_Ms(250);
				 //break;
				}
				else if(XY[0]>326 && XY[1]>108 && XY[0]<429  && XY[1]<149 )	  /* 按了SQU_WAVE */
				{
				    //SQU_WAVE	->prbs58								
					VIEWTECH_70 (3);						        	/* 切换到码型选择界面*/	
	       switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(4,1,32,62,78,1,32);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(4,1,78,62,124,1,78);  /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(4,1,124,62,170,1,124);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 3:
					VIEWTECH_71(4,1,170,62,218,1,170);   /* 上一轮选中的码型变色*/
				   break; 
				 }
					VIEWTECH_71(4,326,108,429,149,326,108);
				  pattern[curchan] = PRBS58	;
					Delay_Ms(250);
					//break;				 
				}
			
				else if(XY[0]>85 && XY[1]>164 && XY[0]<189  && XY[1]<205 )	  /* 按了USER_DEF */
				{
				    //USER_DEF ->prbs9.5
				VIEWTECH_70 (3);						        	/* 切换到码型选择界面*/	
	       switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(4,1,32,62,78,1,32);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(4,1,78,62,124,1,78);  /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(4,1,124,62,170,1,124);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 3:
					VIEWTECH_71(4,1,170,62,218,1,170);   /* 上一轮选中的码型变色*/
				   break; 
				 }
					VIEWTECH_71(4,85,164,189,205,85,164);
		      pattern[curchan] = PRBS9_5;
					Delay_Ms(250);	
					//break;
				}	
				else if(XY[0]>206 && XY[1]>164 && XY[0]<310  && XY[1]<205 )	  /* 按了USER_DEF */
				{
				    //USER_DEF 
					VIEWTECH_70 (3);						        	/* 切换到码型选择界面*/	
	       switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(4,1,32,62,78,1,32);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(4,1,78,62,124,1,78);  /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(4,1,124,62,170,1,124);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 3:
					VIEWTECH_71(4,1,170,62,218,1,170);   /* 上一轮选中的码型变色*/
				   break; 
				 }
					VIEWTECH_71(4,206,164,310,205,206,164);
		      pattern[curchan] = USER_DEF;
				Delay_Ms(250);
					Menu_UDP();
					
				  VIEWTECH_70 (3);						        	/* 切换到码型选择界面*/	
	       switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(4,1,32,62,78,1,32);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(4,1,78,62,124,1,78);  /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(4,1,124,62,170,1,124);   /* 上一轮选中的码型变色*/
				   break; 
					 
					 case 3:
					VIEWTECH_71(4,1,170,62,218,1,170);   /* 上一轮选中的码型变色*/
				   break; 
				 }
 		    
				switch(pattern[curchan])
				{
				 case PRBS7:
				 VIEWTECH_71(4,85,54,189,95,85,54);   /* 上一轮选中的码型变色*/
				 break;
		
				 case PRBS9:
				 VIEWTECH_71(4,206,54,310,95,206,54);
				 break;
		
				 case PRBS15:
				 VIEWTECH_71(4,326,54,429,95,326,54);
				 break;
				
				 case PRBS23:
				 VIEWTECH_71(4,85,108,189,149,85,108);
				 break;
				 
				 case PRBS31:
				 VIEWTECH_71(4,206,108,310,149,206,108);
				 break;
				 
				 case PRBS58://
				 VIEWTECH_71(4,326,108,429,149,326,108);
				 break;
							
				 case PRBS9_5://
				 VIEWTECH_71(4,85,164,189,205,85,164);
				 break;
				 
				 case USER_DEF://USER_DEF:
				 VIEWTECH_71(4,206,164,310,205,206,164);
				 break;				 
				}
				 //break;
				}
				else if(XY[0]>395 && XY[1]>230 && XY[0]<476  && XY[1]<266 )
				  {
						//确定     
					 VIEWTECH_71(4,395,230,476,266,395,230);
					 Delay_Ms(250);  
           break;						
					}
					else if(XY[0]>8 && XY[1]>230 && XY[0]<90  && XY[1]<266 )
				  {
						//取消         
					 VIEWTECH_71(4,8,230,90,266,8,230);
					 pattern[0] = pattern_init_ch1;
						pattern[1] = pattern_init_ch2;
						pattern[2] = pattern_init_ch3;
						pattern[3] = pattern_init_ch4;
					 Delay_Ms(250);  
           break;						
					}	
				else      /*再次按了码型，返回主界面*/
				{				   
					Delay_Ms(150);	
					//break;
				}
								
			}			
       update_status();  //主界面显示实时的速率码型和定时模式
		 }

/*******************************************************************************
* Function Name  : Menu_Timer
* Description    : 定时模式页面操作
* Input          : None
* Output         : None
* Return         : None
* Attention		 : None
*******************************************************************************/
void Menu_Timer(void)	 
{
	u32 timeset_init_ch0,timeset_init_ch1,timeset_init_ch2,timeset_init_ch3;
	u8  mode_init_ch0,mode_init_ch1,mode_init_ch2,mode_init_ch3;
	
	VIEWTECH_70(7);
	switch(curchan)
	 {
		 case 0:
		 {
			 VIEWTECH_71(8,1,32,62,78,1,32);   //ch0
			  switch(mode[0])
			{
			 case FREE_RUN:
			 VIEWTECH_71(8,85,54,189,95,85,54);
			 break;
	
			 case TIMER_EN:
			 if(timer_set[0] == half_min)
			 VIEWTECH_71(8,206,54,310,95,206,54);
			 else if(timer_set[0] == two_min)
			 VIEWTECH_71(8,326,54,429,95,326,54);
			 else if(timer_set[0] == ten_min)
			 VIEWTECH_71(8,85,108,189,149,85,108);
			 else if(timer_set[0] == thirty_min)
			 VIEWTECH_71(8,206,108,310,149,206,108);
			 else
				 VIEWTECH_71(8,326,108,429,149,326,108);					 
			 break;				 
			}	
		 }
		 break; 
		 
		 case 1:
		 {
			 VIEWTECH_71(8,1,78,62,124,1,78);   //ch1
			  switch(mode[1])
			{
			 case FREE_RUN:
			 VIEWTECH_71(8,85,54,189,95,85,54);
			 break;
	
			 case TIMER_EN:
			 if(timer_set[1] == half_min)
			 VIEWTECH_71(8,206,54,310,95,206,54);
			 else if(timer_set[1] == two_min)
			 VIEWTECH_71(8,326,54,429,95,326,54);
			 else if(timer_set[1]== ten_min)
			 VIEWTECH_71(8,85,108,189,149,85,108);
			 else if(timer_set[1] == thirty_min)
			 VIEWTECH_71(8,206,108,310,149,206,108);
			 else
				 VIEWTECH_71(8,545,194,712,260,545,194);					 
			 break;				 
			}
		 }
		 break; 
		 
		 case 2:
		 {
			 VIEWTECH_71(8,1,124,62,170,1,124);   //ch2
			 switch(mode[2])
			{
			 case FREE_RUN:
			 VIEWTECH_71(8,85,54,189,95,85,54);
			 break;
	
			 case TIMER_EN:
			 if(timer_set[2] == half_min)
			 VIEWTECH_71(8,206,54,310,95,206,54);
			 else if(timer_set[2] == two_min)
			 VIEWTECH_71(8,326,54,429,95,326,54);
			 else if(timer_set[2] == ten_min)
			 VIEWTECH_71(8,85,108,189,149,85,108);
			 else if(timer_set[2] == thirty_min)
			 VIEWTECH_71(8,206,108,310,149,206,108);
			 else
				 VIEWTECH_71(8,326,108,429,149,326,108);					 
			 break;				 
			}
		 }
		 break; 
		 
		 case 3:
		 {
			 VIEWTECH_71(8,1,170,62,218,1,170);   //ch3
			 switch(mode[3])
			{
			 case FREE_RUN:
			 VIEWTECH_71(8,85,54,189,95,85,54);
			 break;
	
			 case TIMER_EN:
			 if(timer_set[3] == half_min)
			 VIEWTECH_71(8,206,54,310,95,206,54);
			 else if(timer_set[3] == two_min)
			 VIEWTECH_71(8,326,54,429,95,326,54);
			 else if(timer_set[3] == ten_min)
			 VIEWTECH_71(8,85,108,189,149,85,108);
			 else if(timer_set[3] == thirty_min)
			 VIEWTECH_71(8,206,108,310,149,206,108);
			 else
				 VIEWTECH_71(8,326,108,429,149,326,108);					 
			 break;				 
			}		 
		 }
		 break; 
	 }
	 
	  mode_init_ch0=mode[0]; 
		mode_init_ch1=mode[1];
		mode_init_ch2=mode[2];
		mode_init_ch3=mode[3];

		timeset_init_ch0=timer_set[0];
		timeset_init_ch1=timer_set[1];
		timeset_init_ch2=timer_set[2];
		timeset_init_ch3=timer_set[3];	 
		

	 while(1)
	 {
		 xls[1]=0;									 /* 坐标清0 */
		 xls[2]=0;
		 XY[0]=0;
		 XY[1]=0;
		 while(((XY[0]>1 && XY[0]<799 && XY[1]>1 && XY[1]<479)==0))		/* 再次取坐标 */ 
		 {
		XY[0]=xls[1]<<8;
		XY[0]+=xls[2];
		XY[1]=xls[3]<<8;
		XY[1]+=xls[4];
		Delay_Ms(10);
		 }
		if(XY[0]>0 && XY[1]>32 && XY[0]<62  && XY[1]<218 )		  /* 按了通道选择 */
		{
			if(XY[0]>0 && XY[1]>32 && XY[0]<62  && XY[1]<78 )
			{
				curchan = 0;
				VIEWTECH_70 (7);
				VIEWTECH_71(8,1,32,62,78,1,32);   //ch0
			  switch(mode[0])
			{
			 case FREE_RUN:
			 VIEWTECH_71(8,85,54,189,95,85,54);
			 break;
	
			 case TIMER_EN:
			 if(timer_set[0] == half_min)
			 VIEWTECH_71(8,206,54,310,95,206,54);
			 else if(timer_set[0] == two_min)
			 VIEWTECH_71(8,326,54,429,95,326,54);
			 else if(timer_set[0] == ten_min)
			 VIEWTECH_71(8,85,108,189,149,85,108);
			 else if(timer_set[0] == thirty_min)
			 VIEWTECH_71(8,206,108,310,149,206,108);
			 else
				 VIEWTECH_71(8,326,108,429,149,326,108);					 
			 break;				 
			}
			}
			else if(XY[0]>0 && XY[1]>78 && XY[0]<62  && XY[1]<124 )
			{
				curchan = 1;
				VIEWTECH_70 (7);
				VIEWTECH_71(8,1,78,62,124,1,78);   //ch1
			  switch(mode[1])
				{
			 case FREE_RUN:
			 VIEWTECH_71(8,85,54,189,95,85,54);
				 break;
		
				 case TIMER_EN:
				 if(timer_set[1] == half_min)
			 VIEWTECH_71(8,206,54,310,95,206,54);
			 else if(timer_set[1] == two_min)
			 VIEWTECH_71(8,326,54,429,95,326,54);
			 else if(timer_set[1]== ten_min)
			 VIEWTECH_71(8,85,108,189,149,85,108);
				 else if(timer_set[1] == thirty_min)
				 VIEWTECH_71(8,206,108,310,149,206,108);
				 else
					 VIEWTECH_71(8,326,108,429,149,326,108);					 
				 break;				 
				}
			}
			else if(XY[0]>0 && XY[1]>124 && XY[0]<62  && XY[1]<170 )
			{
				curchan = 2;
				VIEWTECH_70 (7);
				VIEWTECH_71(8,1,124,62,170,1,124);   //ch2
			 switch(mode[2])
			{
			 case FREE_RUN:
			 VIEWTECH_71(8,85,54,189,95,85,54);
			 break;
	
			 case TIMER_EN:
			 if(timer_set[2] == half_min)
			 VIEWTECH_71(8,206,54,310,95,206,54);
			 else if(timer_set[2] == two_min)
			 VIEWTECH_71(8,326,54,429,95,326,54);
			 else if(timer_set[2] == ten_min)
			 VIEWTECH_71(8,85,108,189,149,85,108);
			 else if(timer_set[2] == thirty_min)
			 VIEWTECH_71(8,206,108,310,149,206,108);
			 else
				 VIEWTECH_71(8,326,108,429,149,326,108);					 
			 break;				 
			}
			}
			else if(XY[0]>0 && XY[1]>170 && XY[0]<62  && XY[1]<218 )
			{
				curchan = 3;
				VIEWTECH_70 (7);
				VIEWTECH_71(8,1,170,62,218,1,170);   //ch3
			  switch(mode[3])
			{
			 case FREE_RUN:
			 VIEWTECH_71(8,85,54,189,95,85,54);
			 break;
	
			 case TIMER_EN:
			 if(timer_set[3] == half_min)
			 VIEWTECH_71(8,206,54,310,95,206,54);
			 else if(timer_set[3] == two_min)
			 VIEWTECH_71(8,326,54,429,95,326,54);
			 else if(timer_set[3] == ten_min)
			 VIEWTECH_71(8,85,108,189,149,85,108);
			 else if(timer_set[3] == thirty_min)
			 VIEWTECH_71(8,206,108,310,149,206,108);
			 else
				 VIEWTECH_71(8,326,108,429,149,326,108);					 
			 break;				 
			}
			}
			Delay_Ms(200);
		}
		else if(XY[0]>85 && XY[1]>54 && XY[0]<189  && XY[1]<95 )		  /* 按了wudingshi */
		{
				//wudingshi
			VIEWTECH_70 (7);	                                     
			switch(curchan)
		 {
			 case 0:
			    VIEWTECH_71(8,1,32,62,78,1,32);  /* 上一轮选中变色*/
			 break; 
			 
			 case 1:
				VIEWTECH_71(8,1,78,62,124,1,78);   /* 上一轮选中变色*/
			 break; 
			 
			 case 2:
				VIEWTECH_71(8,1,124,62,170,1,124);   /* 上一轮选中变色*/
			 break; 
			 
			 case 3:
				VIEWTECH_71(8,1,170,62,218,1,170);   /* 上一轮选中变色*/
			 break; 
		 }
			VIEWTECH_71(8,85,54,189,95,85,54);                     //wudingshi xuanzhong
			mode[curchan]=FREE_RUN;
			Delay_Ms(10);
		}
		else if(XY[0]>206 && XY[1]>54 && XY[0]<310  && XY[1]<95 )	 
		{
				//30miao											
			VIEWTECH_70 (7);	
			switch(curchan)
		 {
			 case 0:
			    VIEWTECH_71(8,1,32,62,78,1,32);  /* 上一轮选中变色*/
			 break; 
			 
			 case 1:
				VIEWTECH_71(8,1,78,62,124,1,78);   /* 上一轮选中变色*/
			 break; 
			 
			 case 2:
				VIEWTECH_71(8,1,124,62,170,1,124);   /* 上一轮选中变色*/
			 break; 
			 
			 case 3:
				VIEWTECH_71(8,1,170,62,218,1,170);   /* 上一轮选中变色*/
			 break;
		 }					
			VIEWTECH_71(8,206,54,310,95,206,54);
			mode[curchan] = TIMER_EN;
		  timer_set[curchan]=half_min;
			Delay_Ms(10);		
		}
		else if(XY[0]>326 && XY[1]>54 && XY[0]<429  && XY[1]<95 )	  
		{
				//2fenzhong
			VIEWTECH_70 (7);	
			switch(curchan)
		 {
			 case 0:
			    VIEWTECH_71(8,1,32,62,78,1,32);  /* 上一轮选中变色*/
			 break; 
			 
			 case 1:
				VIEWTECH_71(8,1,78,62,124,1,78);   /* 上一轮选中变色*/
			 break; 
			 
			 case 2:
				VIEWTECH_71(8,1,124,62,170,1,124);   /* 上一轮选中变色*/
			 break; 
			 
			 case 3:
				VIEWTECH_71(8,1,170,62,218,1,170);   /* 上一轮选中变色*/
			 break;
		 }					
			VIEWTECH_71(8,326,54,429,95,326,54);
			mode[curchan] = TIMER_EN;
		  timer_set[curchan]=two_min;
			Delay_Ms(10);		
		}
		else if(XY[0]>85 && XY[1]>108 && XY[0]<189  && XY[1]<149 )	  
		{
				//10fenzhong
			VIEWTECH_70 (7);	
			switch(curchan)
		 {
			case 0:
			    VIEWTECH_71(8,1,32,62,78,1,32);  /* 上一轮选中变色*/
			 break; 
			 
			 case 1:
				VIEWTECH_71(8,1,78,62,124,1,78);   /* 上一轮选中变色*/
			 break; 
			 
			 case 2:
				VIEWTECH_71(8,1,124,62,170,1,124);   /* 上一轮选中变色*/
			 break; 
			 
			 case 3:
				VIEWTECH_71(8,1,170,62,218,1,170);   /* 上一轮选中变色*/
			 break;
		 }
			VIEWTECH_71(8,85,108,189,149,85,108);
			mode[curchan] = TIMER_EN;
		  timer_set[curchan]=ten_min;
			Delay_Ms(10);		
		}
		else if(XY[0]>206 && XY[1]>108 && XY[0]<310  && XY[1]<149 )	  
		{
				//30fenzhong												
			VIEWTECH_70 (7);	
			switch(curchan)
		 {
			 case 0:
			    VIEWTECH_71(8,1,32,62,78,1,32);  /* 上一轮选中变色*/
			 break; 
			 
			 case 1:
				VIEWTECH_71(8,1,78,62,124,1,78);   /* 上一轮选中变色*/
			 break; 
			 
			 case 2:
				VIEWTECH_71(8,1,124,62,170,1,124);   /* 上一轮选中变色*/
			 break; 
			 
			 case 3:
				VIEWTECH_71(8,1,170,62,218,1,170);   /* 上一轮选中变色*/
			 break; 
		 }
			VIEWTECH_71(8,206,108,310,149,206,108);
			mode[curchan] = TIMER_EN;
		  timer_set[curchan]=thirty_min;
			Delay_Ms(10);		
		}
		else if(XY[0]>326 && XY[1]>108 && XY[0]<429  && XY[1]<149 )	  
		{
				//zidingyi									
			VIEWTECH_70 (7);	
			switch(curchan)
		 {
			 case 0:
			    VIEWTECH_71(8,1,32,62,78,1,32);  /* 上一轮选中变色*/
			 break; 
			 
			 case 1:
				VIEWTECH_71(8,1,78,62,124,1,78);   /* 上一轮选中变色*/
			 break; 
			 
			 case 2:
				VIEWTECH_71(8,1,124,62,170,1,124);   /* 上一轮选中变色*/
			 break; 
			 
			 case 3:
				VIEWTECH_71(8,1,170,62,218,1,170);   /* 上一轮选中变色*/
			 break; 
		 }
			VIEWTECH_71(8,326,108,429,149,326,108);
			mode[curchan] = TIMER_EN;
		  Menu_UDEFT();
		  VIEWTECH_70(7);
	switch(curchan)
	 {
		 case 0:
		 {
			 VIEWTECH_71(8,1,32,62,78,1,32);   //ch0
			  switch(mode[0])
			{
			 case FREE_RUN:
			 VIEWTECH_71(8,85,54,189,95,85,54);
			 break;
	
			 case TIMER_EN:
			 if(timer_set[0] == half_min)
			 VIEWTECH_71(8,206,54,310,95,206,54);
			 else if(timer_set[0] == two_min)
			 VIEWTECH_71(8,326,54,429,95,326,54);
			 else if(timer_set[0] == ten_min)
			 VIEWTECH_71(8,85,108,189,149,85,108);
			 else if(timer_set[0] == thirty_min)
			 VIEWTECH_71(8,206,108,310,149,206,108);
			 else
				 VIEWTECH_71(8,326,108,429,149,326,108);					 
			 break;				 
			}	
		 }
		 break; 
		 
		 case 1:
		 {
			 VIEWTECH_71(8,1,78,62,124,1,78);   //ch1
			  switch(mode[1])
			{
			 case FREE_RUN:
			 VIEWTECH_71(8,85,54,189,95,85,54);
			 break;
	
			 case TIMER_EN:
			 if(timer_set[1] == half_min)
			 VIEWTECH_71(8,206,54,310,95,206,54);
			 else if(timer_set[1] == two_min)
			 VIEWTECH_71(8,326,54,429,95,326,54);
			 else if(timer_set[1]== ten_min)
			 VIEWTECH_71(8,85,108,189,149,85,108);
			 else if(timer_set[1] == thirty_min)
			 VIEWTECH_71(8,206,108,310,149,206,108);
			 else
				 VIEWTECH_71(8,545,194,712,260,545,194);					 
			 break;				 
			}
		 }
		 break; 
		 
		 case 2:
		 {
			 VIEWTECH_71(8,1,124,62,170,1,124);   //ch2
			 switch(mode[2])
			{
			 case FREE_RUN:
			 VIEWTECH_71(8,85,54,189,95,85,54);
			 break;
	
			 case TIMER_EN:
			 if(timer_set[2] == half_min)
			 VIEWTECH_71(8,206,54,310,95,206,54);
			 else if(timer_set[2] == two_min)
			 VIEWTECH_71(8,326,54,429,95,326,54);
			 else if(timer_set[2] == ten_min)
			 VIEWTECH_71(8,85,108,189,149,85,108);
			 else if(timer_set[2] == thirty_min)
			 VIEWTECH_71(8,206,108,310,149,206,108);
			 else
				 VIEWTECH_71(8,326,108,429,149,326,108);					 
			 break;				 
			}
		 }
		 break; 
		 
		 case 3:
		 {
			 VIEWTECH_71(8,1,170,62,218,1,170);   //ch3
			 switch(mode[3])
			{
			 case FREE_RUN:
			 VIEWTECH_71(8,85,54,189,95,85,54);
			 break;
	
			 case TIMER_EN:
			 if(timer_set[3] == half_min)
			 VIEWTECH_71(8,206,54,310,95,206,54);
			 else if(timer_set[3] == two_min)
			 VIEWTECH_71(8,326,54,429,95,326,54);
			 else if(timer_set[3] == ten_min)
			 VIEWTECH_71(8,85,108,189,149,85,108);
			 else if(timer_set[3] == thirty_min)
			 VIEWTECH_71(8,206,108,310,149,206,108);
			 else
				 VIEWTECH_71(8,326,108,429,149,326,108);					 
			 break;				 
			}		 
		 }
		 break; 
	 }
			Delay_Ms(10);		
		}
		else if(XY[0]>395 && XY[1]>230 && XY[0]<476  && XY[1]<266 )
		{
			//确定     
		 VIEWTECH_71(8,395,230,476,266,395,230);
		 Delay_Ms(250);  
		 break;						
		}
		else if(XY[0]>8 && XY[1]>230 && XY[0]<90  && XY[1]<266 )
		{
			//取消         
		 VIEWTECH_71(8,8,230,90,266,8,230);
		 mode[0] = mode_init_ch0;
		 mode[1] = mode_init_ch1;
		 mode[2] = mode_init_ch2;
		 mode[3] = mode_init_ch3;
		 timer_set[0] = timeset_init_ch0;
		 timer_set[1] = timeset_init_ch1;
		 timer_set[2] = timeset_init_ch2;
		 timer_set[3] = timeset_init_ch3;
		 Delay_Ms(250);  
		 break;						
		}
		else
		{
			Delay_Ms(10);
		}	
		 
	 }
	 update_status();
	}
//void Menu_Timer(void)	 
//{
//		 VIEWTECH_70(7);
//	
//		    switch(mode)
//				{
//				 case FREE_RUN:
//				 VIEWTECH_71(8,75,97,246,165,75,97);
//				 break;
//		
//				 case TIMER_EN:
//				 if(timer_set == half_min)
//				 VIEWTECH_71(8,299,97,470,165,299,97);
//				 else if(timer_set == two_min)
//				 VIEWTECH_71(8,530,97,701,165,530,97);
//				 else if(timer_set == ten_min)
//				 VIEWTECH_71(8,75,210,246,278,75,210);
//				 else if(timer_set == thirty_min)
//				 VIEWTECH_71(8,299,210,470,278,299,210);
//				 else
//				 VIEWTECH_71(8,530,210,701,278,530,210);
//				 break; 
//					 
//				}	
//	        
//		 
//			 	 xls[1]=0;
//				 xls[2]=0;
//				 XY[0]=0;
//				 XY[1]=0;
//		
//		       while((((XY[0]>1 && XY[0]<799 && XY[1]>1 && XY[1]<358)==0)&&((XY[0]>1 && XY[0]<799 && XY[1]>418 && XY[1]<479)==0))==1)		/* 再次取坐标 */ 
//			     {
//					XY[0]=xls[1]<<8;
//					XY[0]+=xls[2];
//					XY[1]=xls[3]<<8;
//					XY[1]+=xls[4];
//					Delay_Ms(10);
//			     }   	
//		
//		 	if(XY[0]>75 && XY[1]>97 && XY[0]<246  && XY[1]<165 )
//				{
//				    //无定时
//					VIEWTECH_70(7);
//					VIEWTECH_71(8,75,97,246,165,75,97);
//					mode = FREE_RUN;
//					Delay_Ms(250);
//				}
//			else if(XY[0]>299 && XY[1]>97 && XY[0]<470  && XY[1]<165 )
//				{
//				    //30秒
//					VIEWTECH_70(7);
//					VIEWTECH_71(8,299,97,470,165,299,97);
//				  mode = TIMER_EN;
//					timer_set = half_min;
//					Delay_Ms(250);		
//				}
//			else if(XY[0]>530 && XY[1]>97 && XY[0]<701  && XY[1]<165 )
//				{
//				    //2分钟
//					VIEWTECH_70(7);
//					VIEWTECH_71(8,530,97,701,165,530,97);
//				  mode = TIMER_EN;
//					timer_set = two_min;
//					Delay_Ms(250);		
//				}
//			else if(XY[0]>75 && XY[1]>210 && XY[0]<246  && XY[1]<278 )
//				{
//				    //10分钟
//					VIEWTECH_70(7);
//					VIEWTECH_71(8,75,210,246,278,75,210);
//				  mode = TIMER_EN;
//					timer_set = ten_min;
//					Delay_Ms(250);		
//				}
//			else if(XY[0]>299 && XY[1]>210 && XY[0]<470  && XY[1]<278 )
//				{
//				    //30分钟
//					VIEWTECH_70(7);
//					VIEWTECH_71(8,299,210,470,278,299,210);
//				  mode = TIMER_EN;
//					timer_set = thirty_min;
//					Delay_Ms(250);		
//				}
//			else if(XY[0]>530 && XY[1]>210 && XY[0]<701  && XY[1]<278 )
//			{
//					//自定义定时
//				VIEWTECH_70(7);
//				VIEWTECH_71(8,530,210,701,278,530,210);
//				mode = TIMER_EN;
//				 Menu_UDEFT();		
//			}
//			else 
//				{
//				  Delay_Ms(150);		
//				}

//		  update_status(); 	  

//	}				

//通道0的eqpst的减按钮坐标
#define CH0_EQPST_M_X1	83
#define CH0_EQPST_M_Y1	79
#define CH0_EQPST_M_X2	138
#define CH0_EQPST_M_Y3	107
//加减按钮横向距离
#define MINUS_PLUS_X	108
//两个通道上下间距像素点
#define CH_CH_Y	37
//eqpst和eqpre间距像素点
#define EQPST_EQPRE_X	195
	
//EQ调节的坐标
const u16 EQXY[16][4] = {
	//eqpst
	{CH0_EQPST_M_X1, 					CH0_EQPST_M_Y1, 	CH0_EQPST_M_X2, 					CH0_EQPST_M_Y3},
	{CH0_EQPST_M_X1 + MINUS_PLUS_X, 	CH0_EQPST_M_Y1, 	CH0_EQPST_M_X2 + MINUS_PLUS_X, 		CH0_EQPST_M_Y3},
	
	{CH0_EQPST_M_X1, 					CH0_EQPST_M_Y1 + CH_CH_Y, 	CH0_EQPST_M_X2, 					CH0_EQPST_M_Y3 + CH_CH_Y},
	{CH0_EQPST_M_X1 + MINUS_PLUS_X, 	CH0_EQPST_M_Y1 + CH_CH_Y, 	CH0_EQPST_M_X2 + MINUS_PLUS_X, 		CH0_EQPST_M_Y3 + CH_CH_Y},
	
	{CH0_EQPST_M_X1, 					CH0_EQPST_M_Y1 + CH_CH_Y * 2, 	CH0_EQPST_M_X2, 					CH0_EQPST_M_Y3 + CH_CH_Y * 2},
	{CH0_EQPST_M_X1 + MINUS_PLUS_X, 	CH0_EQPST_M_Y1 + CH_CH_Y * 2, 	CH0_EQPST_M_X2 + MINUS_PLUS_X, 		CH0_EQPST_M_Y3 + CH_CH_Y * 2},
	
	{CH0_EQPST_M_X1, 					CH0_EQPST_M_Y1 + CH_CH_Y * 3, 	CH0_EQPST_M_X2, 					CH0_EQPST_M_Y3 + CH_CH_Y * 3},
	{CH0_EQPST_M_X1 + MINUS_PLUS_X, 	CH0_EQPST_M_Y1 + CH_CH_Y * 3, 	CH0_EQPST_M_X2 + MINUS_PLUS_X, 		CH0_EQPST_M_Y3 + CH_CH_Y * 3},
	
	//eqpre
	{CH0_EQPST_M_X1 + EQPST_EQPRE_X, 					CH0_EQPST_M_Y1, 	CH0_EQPST_M_X2 + EQPST_EQPRE_X, 					CH0_EQPST_M_Y3},
	{CH0_EQPST_M_X1 + MINUS_PLUS_X + EQPST_EQPRE_X, 	CH0_EQPST_M_Y1, 	CH0_EQPST_M_X2 + MINUS_PLUS_X + EQPST_EQPRE_X, 		CH0_EQPST_M_Y3},
	
	{CH0_EQPST_M_X1 + EQPST_EQPRE_X, 					CH0_EQPST_M_Y1 + CH_CH_Y, 	CH0_EQPST_M_X2 + EQPST_EQPRE_X, 					CH0_EQPST_M_Y3 + CH_CH_Y},
	{CH0_EQPST_M_X1 + MINUS_PLUS_X + EQPST_EQPRE_X, 	CH0_EQPST_M_Y1 + CH_CH_Y, 	CH0_EQPST_M_X2 + MINUS_PLUS_X + EQPST_EQPRE_X, 		CH0_EQPST_M_Y3 + CH_CH_Y},
	
	{CH0_EQPST_M_X1 + EQPST_EQPRE_X, 					CH0_EQPST_M_Y1 + CH_CH_Y * 2, 	CH0_EQPST_M_X2 + EQPST_EQPRE_X, 					CH0_EQPST_M_Y3 + CH_CH_Y * 2},
	{CH0_EQPST_M_X1 + MINUS_PLUS_X + EQPST_EQPRE_X, 	CH0_EQPST_M_Y1 + CH_CH_Y * 2, 	CH0_EQPST_M_X2 + MINUS_PLUS_X + EQPST_EQPRE_X, 		CH0_EQPST_M_Y3 + CH_CH_Y * 2},
	
	{CH0_EQPST_M_X1 + EQPST_EQPRE_X, 					CH0_EQPST_M_Y1 + CH_CH_Y * 3, 	CH0_EQPST_M_X2 + EQPST_EQPRE_X, 					CH0_EQPST_M_Y3 + CH_CH_Y * 3},
	{CH0_EQPST_M_X1 + MINUS_PLUS_X + EQPST_EQPRE_X, 	CH0_EQPST_M_Y1 + CH_CH_Y * 3, 	CH0_EQPST_M_X2 + MINUS_PLUS_X + EQPST_EQPRE_X, 		CH0_EQPST_M_Y3 + CH_CH_Y * 3}
};

/*******************************************************************************
* Function Name  : Menu_EQDisValue
* Description    : EQ参数显示
* Input          : None
* Output         : None
* Return         : None
* Attention		 : None
*******************************************************************************/
void Menu_EQDisValue(EQSet *EQ_Ch_Tmp)
{
	u8 i = 0;
	for(i = 0; i < 4; i++)
	{
		VIEWTECH_71(45,141,77+37*i,189,109+37*i,141,77+37*i);	
		if(EQ_Ch_Tmp[i].Eqpst >= 10)
		   VIEWTECH_A01 (EQXY[i * 2][0] + 70,EQXY[i * 2][1],0x20e2,0x000a,0xffff,EQ_Ch_Tmp[i].Eqpst);
		else
			VIEWTECH_A01 (EQXY[i * 2][0] + 76,EQXY[i * 2][1],0x10e2,0x000a,0xffff,EQ_Ch_Tmp[i].Eqpst);
	}
	for(i = 0; i < 4; i++)
	{
		VIEWTECH_71(45,336,77+37*i,384,109+37*i,336,77+37*i);	
		if(EQ_Ch_Tmp[i].Eqpre >= 10)	
			VIEWTECH_A01 (EQXY[i * 2 + 8][0] + 70,EQXY[i * 2 + 8][1],0x20e2,0x000a,0xffff,EQ_Ch_Tmp[i].Eqpre);
		else
			VIEWTECH_A01 (EQXY[i * 2 + 8][0] + 76,EQXY[i * 2 + 8][1],0x10e2,0x000a,0xffff,EQ_Ch_Tmp[i].Eqpre);
	}
}

/*******************************************************************************
* Function Name  : Menu_EQSet
* Description    : EQ调节
* Input          : None
* Output         : None
* Return         : None
* Attention		 : None
*******************************************************************************/
void Menu_EQSet(EQSet *EQ_Ch_Tmp, u8 KeyState)
{
	u8 i = 0, flag = 0;
	for(i = 0; i < 16; i++)
	{
		if(XY[0]>EQXY[i][0] && XY[1]>EQXY[i][1] && XY[0]<EQXY[i][2]  && XY[1]<EQXY[i][3] )
			break;
	}
	flag = i;
	switch(flag)
	{
		//eqpst
		case 0:
		case 2:
		case 4:
		case 6:
			if(KeyState==0x03)//抬起									 
			{
				VIEWTECH_71(45,EQXY[i][0],EQXY[i][1],EQXY[i][2],EQXY[i][3],EQXY[i][0],EQXY[i][1]);
				if(EQ_Ch_Tmp[i/2].Eqpst != 0)
					EQ_Ch_Tmp[i/2].Eqpst--;
				VIEWTECH_71(45,141,77+37*(i/2),189,109+37*(i/2),141,77+37*(i/2));	
		if(EQ_Ch_Tmp[i/2].Eqpst >= 10)
		   VIEWTECH_A01 (EQXY[i][0] + 70,EQXY[i][1],0x20e2,0x000a,0xffff,EQ_Ch_Tmp[i/2].Eqpst);
		else
				VIEWTECH_A01 (EQXY[i][0] + 76,EQXY[i][1],0x10e2,0x000a,0xffff,EQ_Ch_Tmp[i/2].Eqpst);
			}
			else if(KeyState==0x01)//按下
			{
				VIEWTECH_71(46,EQXY[i][0],EQXY[i][1],EQXY[i][2],EQXY[i][3],EQXY[i][0],EQXY[i][1]);
			}	
			break;
		case 1:
		case 3:
		case 5:
		case 7:
			if(KeyState==0x03)//抬起									 
			{
				VIEWTECH_71(45,EQXY[i][0],EQXY[i][1],EQXY[i][2],EQXY[i][3],EQXY[i][0],EQXY[i][1]);
				if(EQ_Ch_Tmp[i/2].Eqpst != 63)
					EQ_Ch_Tmp[i/2].Eqpst++;
				VIEWTECH_71(45,141,77+37*(i/2),189,109+37*(i/2),141,77+37*(i/2));	
		if(EQ_Ch_Tmp[i/2].Eqpst >= 10)
		   VIEWTECH_A01 (EQXY[i-1][0] + 70,EQXY[i-1][1],0x20e2,0x000a,0xffff,EQ_Ch_Tmp[i/2].Eqpst);
		else
			VIEWTECH_A01 (EQXY[i-1][0] + 76,EQXY[i-1][1],0x10e2,0x000a,0xffff,EQ_Ch_Tmp[i/2].Eqpst);
			}
			else if(KeyState==0x01)//按下
			{
				VIEWTECH_71(46,EQXY[i][0],EQXY[i][1],EQXY[i][2],EQXY[i][3],EQXY[i][0],EQXY[i][1]);
			}
			break;
			
		//eqpre
		case 8:
		case 10:
		case 12:
		case 14:
			if(KeyState==0x03)//抬起									 
			{
				VIEWTECH_71(45,EQXY[i][0],EQXY[i][1],EQXY[i][2],EQXY[i][3],EQXY[i][0],EQXY[i][1]);
				if(EQ_Ch_Tmp[(i-8)/2].Eqpre != 0)
					EQ_Ch_Tmp[(i-8)/2].Eqpre--;
				VIEWTECH_71(45,336,77+37*((i-8)/2),384,109+37*((i-8)/2),336,77+37*((i-8)/2));	
				if(EQ_Ch_Tmp[(i-8)/2].Eqpre >= 10)
					VIEWTECH_A01 (EQXY[i][0] + 70,EQXY[i][1],0x20e2,0x000a,0xffff,EQ_Ch_Tmp[(i-8)/2].Eqpre);
				else
					VIEWTECH_A01 (EQXY[i][0] + 76,EQXY[i][1],0x10e2,0x000a,0xffff,EQ_Ch_Tmp[(i-8)/2].Eqpre);
			}
			else if(KeyState==0x01)//按下
			{
				VIEWTECH_71(46,EQXY[i][0],EQXY[i][1],EQXY[i][2],EQXY[i][3],EQXY[i][0],EQXY[i][1]);
			}
			break;
		case 9:
		case 11:
		case 13:
		case 15:
			if(KeyState==0x03)//抬起									 
			{
				VIEWTECH_71(45,EQXY[i][0],EQXY[i][1],EQXY[i][2],EQXY[i][3],EQXY[i][0],EQXY[i][1]);
				if(EQ_Ch_Tmp[(i-8)/2].Eqpre != 31)
					EQ_Ch_Tmp[(i-8)/2].Eqpre++;
				VIEWTECH_71(45,336,77+37*((i-9)/2),384,109+37*((i-9)/2),336,77+37*((i-9)/2));	
				if(EQ_Ch_Tmp[(i-8)/2].Eqpre >= 10)
					VIEWTECH_A01 (EQXY[i-1][0] + 70,EQXY[i-1][1],0x20e2,0x000a,0xffff,EQ_Ch_Tmp[(i-9)/2].Eqpre);
				else 
					VIEWTECH_A01 (EQXY[i-1][0] + 76,EQXY[i-1][1],0x10e2,0x000a,0xffff,EQ_Ch_Tmp[(i-9)/2].Eqpre);
			}
			else if(KeyState==0x01)//按下
			{
				VIEWTECH_71(46,EQXY[i][0],EQXY[i][1],EQXY[i][2],EQXY[i][3],EQXY[i][0],EQXY[i][1]);
			}
			break;
	}
}

/*******************************************************************************
* Function Name  : Menu_More
* Description    : 更多页面操作
* Input          : None
* Output         : None
* Return         : None
* Attention		 : None
*******************************************************************************/
void Menu_More(void)
{	
	 u8 swing_ch_initial1, swing_ch_initial2, swing_ch_initial3, swing_ch_initial4;    //摆幅初始值，按取消按钮时用到
	 u8 polarity_ch_initial1, polarity_ch_initial2, polarity_ch_initial3, polarity_ch_initial4;
//	 u8 clockdiv_ch_initial;
	 EQSet EQ_Ch_Tmp[4] = {0,};
	
	 VIEWTECH_70(9);      
	
		        switch(option)
				{
				 case swing:
				  VIEWTECH_71(10,70,47,220,88,70,47);
				 break;
		
				 case polarity:
				  VIEWTECH_71(10,262,47,412,88,262,47);
				 break;
				 
				 case about:
				  VIEWTECH_71(10,70,110,220,152,70,110);
				 break;
		
				 case help:
				  VIEWTECH_71(10,262,110,412,152,262,110);
				 break;
				 
				 case clockdiv:
				  VIEWTECH_71(10,70,173,220,214,70,173);
				 break;
				 
				 case EQ:
				  VIEWTECH_71(10,262,173,412,214,262,173);
				 break;
				}	
	        
		 
			 	 xls[1]=0;
				 xls[2]=0;
				 XY[0]=0;
				 XY[1]=0;
		
		       while((((XY[0]>1 && XY[0]<480 && XY[1]>1 && XY[1]<214)==0)&&((XY[0]>1 && XY[0]<480 && XY[1]>236 && XY[1]<272)==0))==1)		/* 再次取坐标 */ 
			     {
					XY[0]=xls[1]<<8;
					XY[0]+=xls[2];
					XY[1]=xls[3]<<8;
					XY[1]+=xls[4];
					Delay_Ms(10);
			     }   	
		
        if(XY[0]>70 && XY[1]>47 && XY[0]<220  && XY[1]<88 )
				{
				    //输出幅值
					VIEWTECH_70(9);
					VIEWTECH_71(10,70,47,220,88,70,47);
					option = swing;
					Delay_Ms(250);
					VIEWTECH_70(29);
					
					switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(30,1,32,62,78,1,32);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(30,1,78,62,124,1,78);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(30,1,124,62,170,1,124);  /* 上一轮选中的变色*/
				   break; 
					 
					 case 3:
						VIEWTECH_71(30,1,170,62,218,1,170);  /* 上一轮选中的变色*/
				   break; 
				 }
					swing_ch_initial1 = swing_ch[0];    //保存初值，按取消时用
					swing_ch_initial2 = swing_ch[1];
				 swing_ch_initial3 = swing_ch[2];
				 swing_ch_initial4 = swing_ch[3];
					switch(swing_ch[curchan])
				{
				 case wushimv:
				 VIEWTECH_71(30,86,79,186,109,86,79);  /* 上一轮选中的幅值变色*/
				 break;
				 
				 case yibaimv:
				 VIEWTECH_71(30,206,79,306,109,206,79);  /* 上一轮选中的幅值变色*/
				 break;

				 case yibaiwumv:
				 VIEWTECH_71(30,326,79,426,109,326,79);  /* 上一轮选中的幅值变色*/
				 break;
				 
				 case erbaimv:
				 VIEWTECH_71(30,86,115,186,145,86,115);  /* 上一轮选中的幅值变色*/
				 break;
				 
				 case erbaiwumv:
				 VIEWTECH_71(30,206,115,306,145,206,115);  /* 上一轮选中的幅值变色*/
				 break;
				 
				 case sanbaimv:
				 VIEWTECH_71(30,326,115,426,145,326,115);  /* 上一轮选中的幅值变色*/
				 break;
				 
				 case sanbaiwumv:
				 VIEWTECH_71(30,86,150,186,180,86,150);  /* 上一轮选中的幅值变色*/
				 break;
				 
				 case sibaimv:
				 VIEWTECH_71(30,206,150,306,180,206,150);  /* 上一轮选中的幅值变色*/
				 break;
				 
				 case sibaiwumv:
				 VIEWTECH_71(30,326,150,426,180,326,150);  /* 上一轮选中的幅值变色*/
				 break;
				 
				 case wubaimv:
				 VIEWTECH_71(30,86,185,186,216,86,185);  /* 上一轮选中的幅值变色*/
				 break;
				 
				 case wubaiwumv:
				 VIEWTECH_71(30,206,185,306,216,206,185);  /* 上一轮选中的幅值变色*/
				 break;
				 
				 case liubaimv:
				 VIEWTECH_71(30,326,185,426,216,326,185);  /* 上一轮选中的幅值变色*/
				 break;				 				 
				}
					
				while(1)
				{	
					
					xls[1]=0;
			  	xls[2]=0;
				  XY[0]=0;
				  XY[1]=0;
		
		       while((XY[0]>1 && XY[0]<480 && XY[1]>1 && XY[1]<272)==0)		/* 再次取坐标 */ 
			     {
					 XY[0]=xls[1]<<8;
					 XY[0]+=xls[2];
				   XY[1]=xls[3]<<8;
					 XY[1]+=xls[4];
					 Delay_Ms(10);
			     }   	
					 
				if(XY[0]>0 && XY[1]>32 && XY[0]<62  && XY[1]<218 )		  /* 按了通道选择 */
				{
					if(XY[0]>0 && XY[1]>32 && XY[0]<62  && XY[1]<78 )
					{
						curchan = 0;
					}
					else if(XY[0]>0 && XY[1]>78 && XY[0]<62  && XY[1]<124 )
					{
						curchan = 1;
					}
					else if(XY[0]>0 && XY[1]>124 && XY[0]<62  && XY[1]<170 )
					{
						curchan = 2;
					}
					else if(XY[0]>0 && XY[1]>170 && XY[0]<62  && XY[1]<218 )
					{
						curchan = 3;
					}
					Delay_Ms(200);
					
						VIEWTECH_70(29);
								switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(30,1,32,62,78,1,32);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(30,1,78,62,124,1,78);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(30,1,124,62,170,1,124);  /* 上一轮选中的变色*/
				   break; 
					 
					 case 3:
						VIEWTECH_71(30,1,170,62,218,1,170);  /* 上一轮选中的变色*/
				   break; 
				 }
						switch(swing_ch[curchan])
					{
					 case wushimv:
					 VIEWTECH_71(30,86,79,186,109,86,79);  /* 上一轮选中的幅值变色*/
					 break;
					 
					 case yibaimv:
					 VIEWTECH_71(30,206,79,306,109,206,79);  /* 上一轮选中的幅值变色*/
					 break;

					 case yibaiwumv:
					 VIEWTECH_71(30,326,79,426,109,326,79);  /* 上一轮选中的幅值变色*/
					 break;
					 
					 case erbaimv:
					 VIEWTECH_71(30,86,115,186,145,86,115);  /* 上一轮选中的幅值变色*/
					 break;
					 
					 case erbaiwumv:
					 VIEWTECH_71(30,206,115,306,145,206,115);  /* 上一轮选中的幅值变色*/
					 break;
					 
					 case sanbaimv:
					 VIEWTECH_71(30,326,115,426,145,326,115);  /* 上一轮选中的幅值变色*/
					 break;
					 
					 case sanbaiwumv:
					 VIEWTECH_71(30,86,150,186,180,86,150);  /* 上一轮选中的幅值变色*/
					 break;
					 
					 case sibaimv:
					 VIEWTECH_71(30,206,150,306,180,206,150);  /* 上一轮选中的幅值变色*/
					 break;
					 
					 case sibaiwumv:
					 VIEWTECH_71(30,326,150,426,180,326,150);  /* 上一轮选中的幅值变色*/
					 break;
					 
					 case wubaimv:
					 VIEWTECH_71(30,86,185,186,216,86,185);  /* 上一轮选中的幅值变色*/
					 break;
					 
					 case wubaiwumv:
					 VIEWTECH_71(30,206,185,306,216,206,185);  /* 上一轮选中的幅值变色*/
					 break;
					 
					 case liubaimv:
					 VIEWTECH_71(30,326,185,426,216,326,185);  /* 上一轮选中的幅值变色*/
					 break;				 				 
					}
				}
					 
					else if(XY[0]>86 && XY[1]>79 && XY[0]<186  && XY[1]<109 )
				  {
						//50mv
           VIEWTECH_70(29);
							switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(30,1,32,62,78,1,32);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(30,1,78,62,124,1,78);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(30,1,124,62,170,1,124);  /* 上一轮选中的变色*/
				   break; 
					 
					 case 3:
						VIEWTECH_71(30,1,170,62,218,1,170);  /* 上一轮选中的变色*/
				   break; 
				 }
					 VIEWTECH_71(30,86,79,186,109,86,79);  /* 上一轮选中的幅值变色*/
					 swing_ch[curchan] = wushimv;
					 Delay_Ms(250);   			
					}
					else if(XY[0]>206 && XY[1]>79 && XY[0]<306  && XY[1]<109 )
				  {
						//100mv
           VIEWTECH_70(29);
							switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(30,1,32,62,78,1,32);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(30,1,78,62,124,1,78);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(30,1,124,62,170,1,124);  /* 上一轮选中的变色*/
				   break; 
					 
					 case 3:
						VIEWTECH_71(30,1,170,62,218,1,170);  /* 上一轮选中的变色*/
				   break; 
				 }
					 VIEWTECH_71(30,206,79,306,109,206,79);  /* 上一轮选中的幅值变色*/
					 swing_ch[curchan] = yibaimv;
					 Delay_Ms(250);   			
					}
					else if(XY[0]>326 && XY[1]>79 && XY[0]<426  && XY[1]<109 )
				  {
						//150mv
           VIEWTECH_70(29);
							switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(30,1,32,62,78,1,32);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(30,1,78,62,124,1,78);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(30,1,124,62,170,1,124);  /* 上一轮选中的变色*/
				   break; 
					 
					 case 3:
						VIEWTECH_71(30,1,170,62,218,1,170);  /* 上一轮选中的变色*/
				   break; 
				 }
					 VIEWTECH_71(30,326,79,426,109,326,79);  /* 上一轮选中的幅值变色*/
					 swing_ch[curchan] = yibaiwumv;
					 Delay_Ms(250);   			
					}
				  else if(XY[0]>86 && XY[1]>115 && XY[0]<186  && XY[1]<145 )
				  {
						//200mv
           VIEWTECH_70(29);
							switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(30,1,32,62,78,1,32);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(30,1,78,62,124,1,78);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(30,1,124,62,170,1,124);  /* 上一轮选中的变色*/
				   break; 
					 
					 case 3:
						VIEWTECH_71(30,1,170,62,218,1,170);  /* 上一轮选中的变色*/
				   break; 
				 }
					 VIEWTECH_71(30,86,115,186,145,86,115); 
					 swing_ch[curchan] = erbaimv;
					 Delay_Ms(250);          
					}
				  else if(XY[0]>206 && XY[1]>115 && XY[0]<306  && XY[1]<145 )
				  {
						//250mv
           VIEWTECH_70(29);
							switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(30,1,32,62,78,1,32);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(30,1,78,62,124,1,78);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(30,1,124,62,170,1,124);  /* 上一轮选中的变色*/
				   break; 
					 
					 case 3:
						VIEWTECH_71(30,1,170,62,218,1,170);  /* 上一轮选中的变色*/
				   break; 
				 }
					 VIEWTECH_71(30,206,115,306,145,206,115); 
					 swing_ch[curchan] = erbaiwumv;
					 Delay_Ms(250);          
					}
				  else if(XY[0]>326 && XY[1]>115 && XY[0]<426  && XY[1]<145 )
				  {
						//300mv
           VIEWTECH_70(29);
							switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(30,1,32,62,78,1,32);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(30,1,78,62,124,1,78);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(30,1,124,62,170,1,124);  /* 上一轮选中的变色*/
				   break; 
					 
					 case 3:
						VIEWTECH_71(30,1,170,62,218,1,170);  /* 上一轮选中的变色*/
				   break; 
				 }
					 VIEWTECH_71(30,326,115,426,145,326,115); 
					 swing_ch[curchan] = sanbaimv;
					 Delay_Ms(250);          
					}
					else if(XY[0]>86 && XY[1]>150 && XY[0]<186  && XY[1]<180 )
				  {
						//350mv
           VIEWTECH_70(29);
							switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(30,1,32,62,78,1,32);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(30,1,78,62,124,1,78);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(30,1,124,62,170,1,124);  /* 上一轮选中的变色*/
				   break; 
					 
					 case 3:
						VIEWTECH_71(30,1,170,62,218,1,170);  /* 上一轮选中的变色*/
				   break; 
				 }
					 VIEWTECH_71(30,86,150,186,180,86,150);
					 swing_ch[curchan] = sanbaiwumv;
					 Delay_Ms(250);         					
					}
					else if(XY[0]>206 && XY[1]>150 && XY[0]<306  && XY[1]<180 )
				  {
						//400mv
           VIEWTECH_70(29);
							switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(30,1,32,62,78,1,32);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(30,1,78,62,124,1,78);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(30,1,124,62,170,1,124);  /* 上一轮选中的变色*/
				   break; 
					 
					 case 3:
						VIEWTECH_71(30,1,170,62,218,1,170);  /* 上一轮选中的变色*/
				   break; 
				 }
					 VIEWTECH_71(30,206,150,306,180,206,150);
					 swing_ch[curchan] = sibaimv;
					 Delay_Ms(250);         					
					}
					else if(XY[0]>326 && XY[1]>150 && XY[0]<426  && XY[1]<180 )
				  {
						//450mv
           VIEWTECH_70(29);
							switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(30,1,32,62,78,1,32);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(30,1,78,62,124,1,78);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(30,1,124,62,170,1,124);  /* 上一轮选中的变色*/
				   break; 
					 
					 case 3:
						VIEWTECH_71(30,1,170,62,218,1,170);  /* 上一轮选中的变色*/
				   break; 
				 }
					 VIEWTECH_71(30,326,150,426,180,326,150);
					 swing_ch[curchan] = sibaiwumv;
					 Delay_Ms(250);         					
					}
					else if(XY[0]>86 && XY[1]>185 && XY[0]<186  && XY[1]<216 )
				  {
						//500mv
           VIEWTECH_70(29);
							switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(30,1,32,62,78,1,32);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(30,1,78,62,124,1,78);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(30,1,124,62,170,1,124);  /* 上一轮选中的变色*/
				   break; 
					 
					 case 3:
						VIEWTECH_71(30,1,170,62,218,1,170);  /* 上一轮选中的变色*/
				   break; 
				 }
					 VIEWTECH_71(30,86,185,186,216,86,185);
					 swing_ch[curchan] = wubaimv;
					 Delay_Ms(250);         					
					}
					else if(XY[0]>206 && XY[1]>185 && XY[0]<306  && XY[1]<216 )
				  {
						//550mv
           VIEWTECH_70(29);
							switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(30,1,32,62,78,1,32);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(30,1,78,62,124,1,78);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(30,1,124,62,170,1,124);  /* 上一轮选中的变色*/
				   break; 
					 
					 case 3:
						VIEWTECH_71(30,1,170,62,218,1,170);  /* 上一轮选中的变色*/
				   break; 
				 }
					 VIEWTECH_71(30,206,185,306,216,206,185);
					 swing_ch[curchan] = wubaiwumv;
					 Delay_Ms(250);         					
					}
					else if(XY[0]>326 && XY[1]>185 && XY[0]<426  && XY[1]<216 )
				  {
						//600mv
           VIEWTECH_70(29);
							switch(curchan)
				 {
					 case 0:
						VIEWTECH_71(30,1,32,62,78,1,32);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 1:
						VIEWTECH_71(30,1,78,62,124,1,78);   /* 上一轮选中的变色*/
				   break; 
					 
					 case 2:
						VIEWTECH_71(30,1,124,62,170,1,124);  /* 上一轮选中的变色*/
				   break; 
					 
					 case 3:
						VIEWTECH_71(30,1,170,62,218,1,170);  /* 上一轮选中的变色*/
				   break; 
				 }
					 VIEWTECH_71(30,326,185,426,216,326,185);
					 swing_ch[curchan] = liubaimv;
					 Delay_Ms(250);         					
					}					
					else if(XY[0]>395 && XY[1]>230 && XY[0]<476  && XY[1]<266 )
				  {
						//确定
     
					 VIEWTECH_71(30,395,230,476,266,395,230);
					 Delay_Ms(250);  
           break;						
					}
					else if(XY[0]>8 && XY[1]>230 && XY[0]<90  && XY[1]<266 )
				  {
						//取消
         
					 VIEWTECH_71(30,8,230,90,266,8,230);
					 swing_ch[0] = swing_ch_initial1;
						swing_ch[1] = swing_ch_initial2;
						swing_ch[2] = swing_ch_initial3;
						swing_ch[3] = swing_ch_initial4;
					 Delay_Ms(250);  
           break;						
					}														 
				}
			}
			else if(XY[0]>262 && XY[1]>47 && XY[0]<412  && XY[1]<88 )
				{
				    //输出极性翻转
					VIEWTECH_70(9);
					VIEWTECH_71(10,262,47,412,88,262,47);
					option = polarity;
					Delay_Ms(250);
					VIEWTECH_70(31);
					polarity_ch_initial1 = polarity_ch[0];
					polarity_ch_initial2 = polarity_ch[1];
					polarity_ch_initial3 = polarity_ch[2];
					polarity_ch_initial4 = polarity_ch[3];
					
					switch(polarity_ch[0])
				{
				case normal_pn:
				 VIEWTECH_71(32,134,58,254,88,134,58);  /* 上一轮选中的极性变色*/
				 break;
		
				 case invert_pn:
				 VIEWTECH_71(32,282,58,403,88,282,58);
				 break;		
				}
				switch(polarity_ch[1])
				{
				 case normal_pn:
				 VIEWTECH_71(32,134,98,254,128,134,98);  /* 上一轮选中的极性变色*/
				 break;
		
				 case invert_pn:
				 VIEWTECH_71(32,282,98,403,128,282,98);
				 break;		
				}
				switch(polarity_ch[2])
				{
				 case normal_pn:
				 VIEWTECH_71(32,134,138,254,168,134,138);  /* 上一轮选中的极性变色*/
				 break;
		
				 case invert_pn:
				 VIEWTECH_71(32,282,138,403,168,282,138);
				 break;		
				}
				switch(polarity_ch[3])
				{
				case normal_pn:
				 VIEWTECH_71(32,134,178,254,208,134,178);  /* 上一轮选中的极性变色*/
				 break;
		
				 case invert_pn:
				 VIEWTECH_71(32,282,178,403,208,282,178);
				 break;			
				}
					 
				while(1)	
				{
					xls[1]=0;
			  	xls[2]=0;
				  XY[0]=0;
				  XY[1]=0;
		
		       while((XY[0]>1 && XY[0]<479 && XY[1]>1 && XY[1]<272)==0)		/* 再次取坐标 */ 
			     {
					XY[0]=xls[1]<<8;
					XY[0]+=xls[2];
					XY[1]=xls[3]<<8;
					XY[1]+=xls[4];
					Delay_Ms(10);
			     }   	
					 
					 if(XY[0]>134 && XY[1]>58 && XY[0]<254  && XY[1]<88 )
				  {
						//正常
           VIEWTECH_71(31,282,58,403,88,282,58);
					 VIEWTECH_71(32,134,58,254,88,134,58);
						curchan=0;
					 polarity_ch[curchan] = normal_pn;
					 Delay_Ms(250);						 	 
					}
					else if(XY[0]>282 && XY[1]>58 && XY[0]<403  && XY[1]<88 )
				  {
						//翻转
           VIEWTECH_71(31,134,58,254,88,134,58);
					 VIEWTECH_71(32,282,58,403,88,282,58);
						curchan=0;
					 polarity_ch[curchan] = invert_pn;
					 Delay_Ms(250);         					
					}
					else if(XY[0]>134 && XY[1]>98 && XY[0]<254  && XY[1]<128 )
				  {
						//正常
           VIEWTECH_71(31,282,98,403,128,282,98);
					 VIEWTECH_71(32,134,98,254,128,134,98);
					curchan=1;
					 polarity_ch[curchan] = normal_pn;
					 Delay_Ms(250);						 	 
					}
					else if(XY[0]>282 && XY[1]>98 && XY[0]<403  && XY[1]<128 )
				  {
						//翻转
           VIEWTECH_71(31,134,98,254,128,134,98);
					 VIEWTECH_71(32,282,98,403,128,282,98);
						curchan=1;
					 polarity_ch[curchan] = invert_pn;
					 Delay_Ms(250);         					
					}
				else if(XY[0]>134 && XY[1]>138 && XY[0]<254  && XY[1]<168 )
				  {
						//正常
           VIEWTECH_71(31,282,138,403,168,282,138);
					 VIEWTECH_71(32,134,138,254,168,134,138);
						curchan=2;
					 polarity_ch[curchan] = normal_pn;
					 Delay_Ms(250);						 	 
					}
					else if(XY[0]>282 && XY[1]>138 && XY[0]<403  && XY[1]<168 )
				  {
						//翻转
           VIEWTECH_71(31,134,138,254,168,134,138);
					 VIEWTECH_71(32,282,138,403,168,282,138);
						curchan=2;
					 polarity_ch[curchan] = invert_pn;
					 Delay_Ms(250);         					
					}
				else if(XY[0]>134 && XY[1]>178 && XY[0]<254  && XY[1]<208 )
				  {
						//正常
           VIEWTECH_71(31,282,178,403,208,282,178);
					 VIEWTECH_71(32,134,178,254,208,134,178);
						curchan=3;
					 polarity_ch[curchan] = normal_pn;
					 Delay_Ms(250);						 	 
					}
					else if(XY[0]>282 && XY[1]>178 && XY[0]<403  && XY[1]<208 )
				  {
						//翻转
           VIEWTECH_71(31,134,178,254,208,134,178);
					 VIEWTECH_71(32,282,178,403,208,282,178);
						curchan=3;
					 polarity_ch[curchan] = invert_pn;
					 Delay_Ms(250);         					
					}
					
					else if(XY[0]>395 && XY[1]>230 && XY[0]<476  && XY[1]<266 )
				  {
						//确定
 
					 VIEWTECH_71(32,395,230,476,266,395,230);
					 Delay_Ms(250);  
           break;						
					}
					else if(XY[0]>8 && XY[1]>230 && XY[0]<90  && XY[1]<266 )
				  {
						//取消

					 VIEWTECH_71(32,8,230,90,266,8,230);
					 polarity_ch[0] = polarity_ch_initial1;
						polarity_ch[1] = polarity_ch_initial2;
						polarity_ch[2] = polarity_ch_initial3;
						polarity_ch[3] = polarity_ch_initial4;
					 Delay_Ms(250);  
           break;						
					}														
				}
			}
		else if(XY[0]>70 && XY[1]>110 && XY[0]<220  && XY[1]<152 )
		{
				    //关于
					VIEWTECH_70(9);
					VIEWTECH_71(10,70,110,220,152,70,110);
					option = about;
					Delay_Ms(250);
//					VIEWTECH_70(33);
//			    VIEWTECH_98 (135,165,0x22,0x91,3,0x000a,0x39c8,"机器型号",8);		 /*机器型号*/
//			    VIEWTECH_98 (135,210,0x22,0x91,3,0x000a,0x39c8,"软件版本",8);		 
//			    VIEWTECH_98 (135,255,0x22,0x91,3,0x000a,0x39c8,"硬件版本",8);		
//			    VIEWTECH_98 (135,300,0x22,0x91,3,0x000a,0x39c8,"序列编号",8);		 
//			    VIEWTECH_98 (235,165,0x22,0x90,3,0x000a,0x39c8,": BERTWave E410B",12);		 /*机器型号*/
//			    VIEWTECH_98 (235,210,0x22,0x90,3,0x000a,0x39c8,": 1.30",8);		 
//			    VIEWTECH_98 (235,255,0x22,0x90,3,0x000a,0x39c8,": 1.1",8);		
//			    VIEWTECH_98 (235,300,0x22,0x90,3,0x000a,0x39c8,": 410",5);	
//          VIEWTECH_A01(235+60,300,0x7022,0x000a,0x39c8,(long int)(id - 4100000000u));	   /* 测试计数 */		
          Show_id();			

					
		  while(1)
			{
					xls[1]=0;
			  	xls[2]=0;
				  XY[0]=0;
				  XY[1]=0;
		
		       while((XY[0]>1 && XY[0]<479 && XY[1]>1 && XY[1]<272)==0)		/* 再次取坐标 */ 
			     {
					XY[0]=xls[1]<<8;
					XY[0]+=xls[2];
					XY[1]=xls[3]<<8;
					XY[1]+=xls[4];
					Delay_Ms(10);
			     }
           if(XY[0]>377 && XY[1]>226 && XY[0]<459  && XY[1]<261 )
				  {
						//返回
 
					 VIEWTECH_71(34,377,226,459,261,377,226);
					 Delay_Ms(250);  
           break;						
					}					 
					 if(XY[0]>0 && XY[1]>0 && XY[0]<60  && XY[1]<60 && xls[0]==0x03)		 //点击关于页面的左上角可更改产品系列号，密码 104
		  		 {
						  //点击关于页面的左上角可更改产品系列号，产品序列号425 xxxx xxx，xxx为0~9的整数
				     VIEWTECH_7C01 (210,66,16,0x00);   //调出小键盘,不带回显功能
						 Show_id();
						while(1)
						{
						 if(biaoji == Virtual_keyboard)
						 {
							if(xls[2]==3 && xls[3]==49 && xls[4]==48 && xls[5]==52 ) //判断密码是否是3位，且是104
							 { 
								biaoji = No_touch; 
								 Show_id();
  							//VIEWTECH_71(34,260,300,460,350,260,300);      //擦除现在显示的序列号 和可能出现的PASSWORD ERROR
								Delay_Ms(100); 
								VIEWTECH_7C01 (210,66,16,0x00);  	
									Show_id();								 
//								VIEWTECH_7C04 (450,70,10,0x40,260,300,0x22,0x90,3,0x000a,0x39c8);   //调出小键盘，带回显
						    while(1)
                {
                 if(biaoji == Virtual_keyboard)
	               { 
									if(xls[2]==10 && xls[3]==52 && xls[4]==49 && xls[5]==53 && xls[6]>=48 && xls[6]<=57 && xls[7]>=48 && xls[7]<=57 && xls[8]>=48 && xls[8]<=49 
										&& xls[9]>=48 && xls[9]<=57 && xls[10]>=48 && xls[10]<=57 && xls[11]>=48 && xls[11]<=57 && xls[12]>=48 && xls[12]<=57) //判断是否是415 xxxx xxx
								   { 
										 biaoji = No_touch;
 	                   id = (xls[3]-48)*1000000000+(xls[4]-48)*100000000+(xls[5]-48)*10000000+(xls[6]-48)*1000000+(xls[7]-48)*100000+(xls[8]-48)*10000+(xls[9]-48)*1000+(xls[10]-48)*100+(xls[11]-48)*10+(xls[12]-48);
										 Show_id();
										 g_TamperData = id;
										 eeprom_write_block(&g_TamperData,E_Tamper,4);
//										 g_TamperData = id & 0xffff;
//										 eeprom_write_block(&g_TamperData,E_Tamper+2,2);
                   }
								   else
								   {
										 Show_id();
								   	VIEWTECH_98 (151,195,0x21,0x90,1,0xf800,0xa6de,ERR_id,20);		
                   }
									 break;
								 }
	              }							 
              }
							else
							{
								Show_id();
							 VIEWTECH_98 (151,195,0x21,0x90,1,0xf800,0xa6de,ERR_Password,20);		//密码输入错误，error
              }  							
							break;
				     }
					  } 
				   }
				 }					 
		   }
		  	else if(XY[0]>262 && XY[1]>110 && XY[0]<412  && XY[1]<152 )
				{
				    //帮助
					VIEWTECH_70(9);
					VIEWTECH_71(10,262,110,412,152,262,110);
					option = help;
					Delay_Ms(250);
					VIEWTECH_70(35);
					
				while(1)
				{
					xls[1]=0;
			  	xls[2]=0;
				  XY[0]=0;
				  XY[1]=0;
		
		       while((XY[0]>1 && XY[0]<479 && XY[1]>1 && XY[1]<272)==0)		/* 再次取坐标 */ 
			     {
					XY[0]=xls[1]<<8;
					XY[0]+=xls[2];
					XY[1]=xls[3]<<8;
					XY[1]+=xls[4];
					Delay_Ms(10);
			     }   	
					 if(XY[0]>377 && XY[1]>226 && XY[0]<459  && XY[1]<261 )
				  {
						//返回
 
					 VIEWTECH_71(34,377,226,459,261,377,226);
					 Delay_Ms(250);  
           break;						
					}					 
				}
			}
			else if(XY[0]>70 && XY[1]>173 && XY[0]<220  && XY[1]<214 )
			{
				    //输出极性翻转Rx
					VIEWTECH_70(9);
					VIEWTECH_71(10,70,173,220,214,70,173);
					option = clockdiv;
					Delay_Ms(250);
					VIEWTECH_70(31);
					polarity_ch_initial1 = polarity_rx[0];
					polarity_ch_initial2 = polarity_rx[1];
					polarity_ch_initial3 = polarity_rx[2];
					polarity_ch_initial4 = polarity_rx[3];
					
					switch(polarity_rx[0])
				{
				case normal_pn:
				 VIEWTECH_71(32,134,58,254,88,134,58);  /* 上一轮选中的极性变色*/
				 break;
		
				 case invert_pn:
				 VIEWTECH_71(32,282,58,403,88,282,58);
				 break;		
				}
				switch(polarity_rx[1])
				{
				 case normal_pn:
				 VIEWTECH_71(32,134,98,254,128,134,98);  /* 上一轮选中的极性变色*/
				 break;
		
				 case invert_pn:
				 VIEWTECH_71(32,282,98,403,128,282,98);
				 break;		
				}
				switch(polarity_rx[2])
				{
				 case normal_pn:
				 VIEWTECH_71(32,134,138,254,168,134,138);  /* 上一轮选中的极性变色*/
				 break;
		
				 case invert_pn:
				 VIEWTECH_71(32,282,138,403,168,282,138);
				 break;		
				}
				switch(polarity_rx[3])
				{
				case normal_pn:
				 VIEWTECH_71(32,134,178,254,208,134,178);  /* 上一轮选中的极性变色*/
				 break;
		
				 case invert_pn:
				 VIEWTECH_71(32,282,178,403,208,282,178);
				 break;			
				}
					 
				while(1)	
				{
					xls[1]=0;
			  	xls[2]=0;
				  XY[0]=0;
				  XY[1]=0;
		
		       while((XY[0]>1 && XY[0]<479 && XY[1]>1 && XY[1]<272)==0)		/* 再次取坐标 */ 
			     {
					XY[0]=xls[1]<<8;
					XY[0]+=xls[2];
					XY[1]=xls[3]<<8;
					XY[1]+=xls[4];
					Delay_Ms(10);
			     }   	
					 
					 if(XY[0]>134 && XY[1]>58 && XY[0]<254  && XY[1]<88 )
				  {
						//正常
           VIEWTECH_71(31,282,58,403,88,282,58);
					 VIEWTECH_71(32,134,58,254,88,134,58);
						curchan=0;
					 polarity_rx[curchan] = normal_pn;
					 Delay_Ms(250);						 	 
					}
					else if(XY[0]>282 && XY[1]>58 && XY[0]<403  && XY[1]<88 )
				  {
						//翻转
           VIEWTECH_71(31,134,58,254,88,134,58);
					 VIEWTECH_71(32,282,58,403,88,282,58);
						curchan=0;
					 polarity_rx[curchan] = invert_pn;
					 Delay_Ms(250);         					
					}
					else if(XY[0]>134 && XY[1]>98 && XY[0]<254  && XY[1]<128 )
				  {
						//正常
           VIEWTECH_71(31,282,98,403,128,282,98);
					 VIEWTECH_71(32,134,98,254,128,134,98);
					curchan=1;
					 polarity_rx[curchan] = normal_pn;
					 Delay_Ms(250);						 	 
					}
					else if(XY[0]>282 && XY[1]>98 && XY[0]<403  && XY[1]<128 )
				  {
						//翻转
           VIEWTECH_71(31,134,98,254,128,134,98);
					 VIEWTECH_71(32,282,98,403,128,282,98);
						curchan=1;
					 polarity_rx[curchan] = invert_pn;
					 Delay_Ms(250);         					
					}
				else if(XY[0]>134 && XY[1]>138 && XY[0]<254  && XY[1]<168 )
				  {
						//正常
           VIEWTECH_71(31,282,138,403,168,282,138);
					 VIEWTECH_71(32,134,138,254,168,134,138);
						curchan=2;
					 polarity_rx[curchan] = normal_pn;
					 Delay_Ms(250);						 	 
					}
					else if(XY[0]>282 && XY[1]>138 && XY[0]<403  && XY[1]<168 )
				  {
						//翻转
           VIEWTECH_71(31,134,138,254,168,134,138);
					 VIEWTECH_71(32,282,138,403,168,282,138);
						curchan=2;
					 polarity_rx[curchan] = invert_pn;
					 Delay_Ms(250);         					
					}
				else if(XY[0]>134 && XY[1]>178 && XY[0]<254  && XY[1]<208 )
				  {
						//正常
           VIEWTECH_71(31,282,178,403,208,282,178);
					 VIEWTECH_71(32,134,178,254,208,134,178);
						curchan=3;
					 polarity_rx[curchan] = normal_pn;
					 Delay_Ms(250);						 	 
					}
					else if(XY[0]>282 && XY[1]>178 && XY[0]<403  && XY[1]<208 )
				  {
						//翻转
           VIEWTECH_71(31,134,178,254,208,134,178);
					 VIEWTECH_71(32,282,178,403,208,282,178);
						curchan=3;
					 polarity_rx[curchan] = invert_pn;
					 Delay_Ms(250);         					
					}
					
					else if(XY[0]>395 && XY[1]>230 && XY[0]<476  && XY[1]<266 )
				  {
						//确定
 
					 VIEWTECH_71(32,395,230,476,266,395,230);
					 Delay_Ms(250);  
           break;						
					}
					else if(XY[0]>8 && XY[1]>230 && XY[0]<90  && XY[1]<266 )
				  {
						//取消

					 VIEWTECH_71(32,8,230,90,266,8,230);
					 polarity_rx[0] = polarity_ch_initial1;
						polarity_rx[1] = polarity_ch_initial2;
						polarity_rx[2] = polarity_ch_initial3;
						polarity_rx[3] = polarity_ch_initial4;
					 Delay_Ms(250);  
           break;						
					}														
				}
			}
			else if(XY[0]>262 && XY[1]>173 && XY[0]<412  && XY[1]<214 )
			{
				    //EQ调节
					VIEWTECH_70(9);
					//VIEWTECH_71(10,435,307,682,375,435,307);
					VIEWTECH_71(10,262,173,412,214,262,173);
					option = EQ;
					Delay_Ms(250);
					VIEWTECH_70(45);
					EQ_Ch_Tmp[0].Eqpst = EQ_Ch[0].Eqpst;
					EQ_Ch_Tmp[1].Eqpst = EQ_Ch[1].Eqpst;
					EQ_Ch_Tmp[2].Eqpst = EQ_Ch[2].Eqpst;
					EQ_Ch_Tmp[3].Eqpst = EQ_Ch[3].Eqpst;
					EQ_Ch_Tmp[0].Eqpre = EQ_Ch[0].Eqpre;
					EQ_Ch_Tmp[1].Eqpre = EQ_Ch[1].Eqpre;
					EQ_Ch_Tmp[2].Eqpre = EQ_Ch[2].Eqpre;
					EQ_Ch_Tmp[3].Eqpre = EQ_Ch[3].Eqpre;
					//显示EQ参数
					Menu_EQDisValue(EQ_Ch_Tmp);
					 
				while(1)	
				{
					xls[0]=0;
					xls[1]=0;
			  	xls[2]=0;
				  XY[0]=0;
				  XY[1]=0;
		
		       while((XY[0]>1 && XY[0]<479 && XY[1]>1 && XY[1]<272)==0)		/* 再次取坐标 */ 
			     {
					XY[0]=xls[1]<<8;
					XY[0]+=xls[2];
					XY[1]=xls[3]<<8;
					XY[1]+=xls[4];
					Delay_Ms(10);
			     }   	

					Menu_EQSet(EQ_Ch_Tmp, xls[0]);

					if(XY[0]>395 && XY[1]>230 && XY[0]<476  && XY[1]<266 )
				  {
						//确定
 
					 VIEWTECH_71(46,395,230,476,266,395,230);
					EQ_Ch[0].Eqpst = EQ_Ch_Tmp[0].Eqpst;
					EQ_Ch[1].Eqpst = EQ_Ch_Tmp[1].Eqpst;
					EQ_Ch[2].Eqpst = EQ_Ch_Tmp[2].Eqpst;
					EQ_Ch[3].Eqpst = EQ_Ch_Tmp[3].Eqpst;
					EQ_Ch[0].Eqpre = EQ_Ch_Tmp[0].Eqpre;
					EQ_Ch[1].Eqpre = EQ_Ch_Tmp[1].Eqpre;
					EQ_Ch[2].Eqpre = EQ_Ch_Tmp[2].Eqpre;
					EQ_Ch[3].Eqpre = EQ_Ch_Tmp[3].Eqpre;
					 Delay_Ms(250);  
           break;						
					}
					else if(XY[0]>8 && XY[1]>230 && XY[0]<90  && XY[1]<266 )
				  {
						//取消

					 VIEWTECH_71(46,8,230,90,266,8,230);
					 Delay_Ms(250);  
           break;						
					}														
				}
			}
			else 
				{
				  Delay_Ms(150);		
				}
				
			update_status(); 
			
}


/*******************************************************************************
* Function Name  : Menu_Rate
* Description    : 速率选择界面操作
* Input          : None
* Output         : None
* Return         : None
* Attention		 : None
*******************************************************************************/
void Menu_Rate(void)
{
          VIEWTECH_70(5);
	
		        switch(rate)
				{ 
				 case yiGe:                 //1.25G
				 VIEWTECH_71(6,44,40,132,78,44,40);
				 break;
		     
				 case erG_FC:                //2.125G
				 VIEWTECH_71(6,143,40,230,78,143,40);
				 break;
		
				 case oc48:                //2.488G
				 VIEWTECH_71(6,241,40,329,78,241,40);
				 break;
		 
				 case xaui:               //3.125G
				 VIEWTECH_71(6,340,40,430,78,340,40);
				 break;
		
				 case siG_FC:            //4.25G
				 VIEWTECH_71(6,44,81,132,116,44,81);
				 break;
		     
				 case oc96:                     //4.976G
				 VIEWTECH_71(6,143,81,230,116,143,81);
				 break;
		
				 case wuGinfi:               //5G
				 VIEWTECH_71(6,241,81,329,116,241,81);
				 break;
		
				 case rxaui:                    //6.25G
				 VIEWTECH_71(6,340,81,430,116,340,81);
				 break;
		
				 case baG_FC:                    //8.5G
				 VIEWTECH_71(6,44,120,132,154,44,120);
				 break;
		
		     case shiGwan:             //9.95328G
				 VIEWTECH_71(6,143,120,230,154,143,120);
				 break;
		
				 case shiGinfi:              //10G
				 VIEWTECH_71(6,241,120,329,154,241,120);
				 break;
		
				 case shiGlan:               //10.3125G
				 VIEWTECH_71(6,340,120,430,154,340,120);
				 break;

				 case shiG_FC:                 //10.52G
				  VIEWTECH_71(6,44,158,132,193,44,158);
				 break;

				 case otu2:         //10.709G
				 VIEWTECH_71(6,143,158,230,193,143,158);
				 break;
				 
				 case otu2e:         //11.09G
				 VIEWTECH_71(6,241,158,329,193,241,158);
				 break;
				 
				 case otu2f:         //11.318G
				 VIEWTECH_71(6,340,158,430,193,340,158);
				 break;
				 
				 case shierdianwuG: //12.5G
					 VIEWTECH_71(6,44,196,132,232,44,196);
				 break;
				
				case shiliuGfc:         //14.025G
				 VIEWTECH_71(6,143,196,230,232,143,196);
				 break;
				 
				 case shiwuG:         //15G
				 VIEWTECH_71(6,241,196,329,232,241,196);
				 break;	
				 
				  default:         //Other rate
				 VIEWTECH_71(6,340,196,430,232,340,196);
				 break;
//				 break;				 
				}	
	        
		 
			 	 xls[1]=0;
				 xls[2]=0;
				 XY[0]=0;
				 XY[1]=0;
		
		     while((((XY[0]>1 && XY[0]<479 && XY[1]>1 && XY[1]<230)==0)&&((XY[0]>1 && XY[0]<479 && XY[1]>236 && XY[1]<272)==0))==1)		/* 再次取坐标 */ 
			     {
					XY[0]=xls[1]<<8;
					XY[0]+=xls[2];
					XY[1]=xls[3]<<8;
					XY[1]+=xls[4];
					Delay_Ms(10);
			     }   	
		
		 	if(XY[0]>44 && XY[1]>40 && XY[0]<132  && XY[1]<78				)
				{
				    //1.25G
					VIEWTECH_70(5);
					VIEWTECH_71(6,44,40,132,78,44,40);
					rate = yiGe;
					Delay_Ms(250);
				}
				else	if(XY[0]>143 && XY[1]>40 && XY[0]<230  && XY[1]<78 )
				{
				    //2.125G
					VIEWTECH_70(5);
					VIEWTECH_71(6,143,40,230,78,143,40);
					rate = erG_FC;
					Delay_Ms(250);
				}
				else	if(XY[0]>241 && XY[1]>40 && XY[0]<329  && XY[1]<78 )
				{
				    //2.488G
					VIEWTECH_70(5);
					VIEWTECH_71(6,241,40,329,78,241,40);
					rate = oc48;
					Delay_Ms(250);
				}
				else	if(XY[0]>340 && XY[1]>40 && XY[0]<430  && XY[1]<78 )
				{
				    //3.125G
					VIEWTECH_70(5);
					VIEWTECH_71(6,340,40,430,78,340,40);
					rate = xaui;
					Delay_Ms(250);
				}
				else	if(XY[0]>44 && XY[1]>81 && XY[0]<132  && XY[1]<116 )
				{
				    //4.25G
					VIEWTECH_70(5);
					VIEWTECH_71(6,44,81,132,116,44,81);
					rate = siG_FC;
					Delay_Ms(250);
				}
				else	if(XY[0]>143 && XY[1]>81 && XY[0]<230  && XY[1]<116 )
				{
				   //4.976G
					VIEWTECH_70(5);
					VIEWTECH_71(6,143,81,230,116,143,81);
					rate = oc96;
					Delay_Ms(250);
				}
				else	if(XY[0]>241 && XY[1]>81 && XY[0]<329  && XY[1]<116 )
				{
				    //5G
					VIEWTECH_70(5);
					VIEWTECH_71(6,241,81,329,116,241,81);
					rate = wuGinfi;
					Delay_Ms(250);
				}
				else	if(XY[0]>340 && XY[1]>81 && XY[0]<430  && XY[1]<116 )
				{
				    //6.25G
					VIEWTECH_70(5);
					VIEWTECH_71(6,340,81,430,116,340,81);
					rate = rxaui;
					Delay_Ms(250);
				}
				else	if(XY[0]>44 && XY[1]>120 && XY[0]<132  && XY[1]<154 )
				{
				    //8.5G
					VIEWTECH_70(5);
					VIEWTECH_71(6,44,120,132,154,44,120);
					rate = baG_FC;
					Delay_Ms(250);
				}
				else	if(XY[0]>143 && XY[1]>120 && XY[0]<230  && XY[1]<154 )
				{
				    //9.95328G
					VIEWTECH_70(5);
					 VIEWTECH_71(6,143,120,230,154,143,120);
					rate = shiGwan;
					Delay_Ms(250);
				}
				else	if(XY[0]>241 && XY[1]>120 && XY[0]<329  && XY[1]<154 )
				{
				    //10G
					VIEWTECH_70(5);
					VIEWTECH_71(6,241,120,329,154,241,120);
					rate = shiGinfi;
					Delay_Ms(250);
				}
			else if(XY[0]>340 && XY[1]>120 && XY[0]<430  && XY[1]<154 )
				{
				    //10.3125G
					VIEWTECH_70(5);
					VIEWTECH_71(6,340,120,430,154,340,120);
					rate = shiGlan;
					Delay_Ms(250);		
				}
				else if(XY[0]>44 && XY[1]>158 && XY[0]<132  && XY[1]<193 )
				{
				    //10.52G
					VIEWTECH_70(5);
					VIEWTECH_71(6,44,158,132,193,44,158);
					rate = shiG_FC;
					Delay_Ms(250);		
				}
			else if(XY[0]>143 && XY[1]>158 && XY[0]<230  && XY[1]<193 )
				{
				    //10.709G
					VIEWTECH_70(5);
					VIEWTECH_71(6,143,158,230,193,143,158);
					rate = otu2;
					Delay_Ms(250);		
				}
			else if(XY[0]>241 && XY[1]>158 && XY[0]<329  && XY[1]<193 )
				{
				    //11.09G
					VIEWTECH_70(5);
					VIEWTECH_71(6,241,158,329,193,241,158);
					rate = otu2e;
					Delay_Ms(250);		
				}
			else if(XY[0]>340 && XY[1]>158 && XY[0]<430  && XY[1]<193 )
				{
				    //11.318G
					VIEWTECH_70(5);
					VIEWTECH_71(6,340,158,430,193,340,158);
					rate = otu2f;
					Delay_Ms(250);		
				}
				else if(XY[0]>44 && XY[1]>196 && XY[0]<132  && XY[1]<232 )
				{
				    //12.5G
					VIEWTECH_70(5);
					VIEWTECH_71(6,44,196,132,232,44,196);
					rate = shierdianwuG;
					Delay_Ms(250);				
				}
			else if(XY[0]>143 && XY[1]>196 && XY[0]<230  && XY[1]<232 )
				{
				    //14.025G
					VIEWTECH_70(5);
					VIEWTECH_71(6,143,196,230,232,143,196);
					rate = shiliuGfc;
					Delay_Ms(250);		
				}
			else if(XY[0]>241 && XY[1]>196 && XY[0]<329  && XY[1]<232 )
				{
				    //15G
					VIEWTECH_70(5);
					VIEWTECH_71(6,241,196,329,232,241,196);
					rate = shiwuG;
					Delay_Ms(250);		
				}
//				}
				else if(XY[0]>340 && XY[1]>196 && XY[0]<430  && XY[1]<232 )
				{
//				    //urate
					VIEWTECH_70(5);
					VIEWTECH_71(6,340,196,430,232,340,196);
					//rate = user_rate;
//					Menu_UDEFRate();
					Delay_Ms(250);
					//翻页
					Menu_OtherRate();				
							
				}
				else 
				{
					Delay_Ms(150);		
				}
				TX_Rate = 0;
		    update_status(); 
			
}

void Menu_OtherRate(void)
{
          VIEWTECH_70(39);
	
		        switch(rate)
				{ 
				 case cpon:                 //cpon
				 VIEWTECH_71(40,44,40,132,78,44,40);
				 break;
		     
				 case epon:                //epon
				 VIEWTECH_71(40,143,40,230,78,143,40);
				 break;
		
				 case gpon:                //gpon
				 VIEWTECH_71(40,241,40,329,78,241,40);
				 break;
		 
				 case xgpon:               //xgpon
				 VIEWTECH_71(40,340,40,430,78,340,40);
				 break;
		
				 case xgspon:            //xgspon
				 VIEWTECH_71(40,44,81,132,116,44,81);
				 break;  

				case cpri155:                //epon
				 VIEWTECH_71(40,143,81,230,116,143,81);
				 break;
		
				 case cpri622:                //gpon
				 VIEWTECH_71(40,241,81,329,116,241,81);
				 break;		

				 case cpri3:                //3G
				 VIEWTECH_71(40,340,81,430,116,340,81);
				 break;		

				 case cpri6:                //6G
				 VIEWTECH_71(40,44,120,132,154,44,120);
				 break;	

				 case cpri9:                //9G
				 VIEWTECH_71(40,143,120,230,154,143,120);
				 break;	

				case cpri300:                //300M
				 VIEWTECH_71(40,241,120,329,154,241,120);
				 break;					 

				  default:         //
				 //VIEWTECH_71(6,340,196,430,232,340,196);
				 break;
//				 break;				 
				}	
	        
		 
			 	 xls[1]=0;
				 xls[2]=0;
				 XY[0]=0;
				 XY[1]=0;
		
		     while((((XY[0]>1 && XY[0]<479 && XY[1]>1 && XY[1]<155)==0)&&((XY[0]>1 && XY[0]<479 && XY[1]>236 && XY[1]<272)==0))==1)		/* 再次取坐标 */ 
			     {
					XY[0]=xls[1]<<8;
					XY[0]+=xls[2];
					XY[1]=xls[3]<<8;
					XY[1]+=xls[4];
					Delay_Ms(100);
			     }   	
		
		 	if(XY[0]>44 && XY[1]>40 && XY[0]<132  && XY[1]<78	)
				{
				    //cpon
					VIEWTECH_70(39);
					VIEWTECH_71(40,44,40,132,78,44,40);
					rate = cpon;
					Delay_Ms(250);
				}
				else	if(XY[0]>143 && XY[1]>40 && XY[0]<230  && XY[1]<78 )
				{
				    //epon
					VIEWTECH_70(39);
					VIEWTECH_71(40,143,40,230,78,143,40);
					rate = epon;
					Delay_Ms(250);
				}
				else	if(XY[0]>241 && XY[1]>40 && XY[0]<329  && XY[1]<78 )
				{
				    //gpon
					VIEWTECH_70(39);
					VIEWTECH_71(40,241,40,329,78,241,40);
					rate = gpon;
					Delay_Ms(250);
				}
				else	if(XY[0]>340 && XY[1]>40 && XY[0]<430  && XY[1]<78 )
				{
				    //xgpon
					VIEWTECH_70(39);
					VIEWTECH_71(40,340,40,430,78,340,40);
					rate = xgpon;
					Delay_Ms(250);
				}
				else	if(XY[0]>44 && XY[1]>81 && XY[0]<132  && XY[1]<116 )
				{
				    //xgspon
					VIEWTECH_70(39);
					VIEWTECH_71(40,44,81,132,116,44,81);
					rate = xgspon;
					Delay_Ms(250);
				}
				else	if(XY[0]>143 && XY[1]>81 && XY[0]<230  && XY[1]<116 )
				{
				    //155.52
					VIEWTECH_70(39);
					VIEWTECH_71(40,143,81,230,116,143,81);
					rate = cpri155;
					Delay_Ms(250);
				}
				else	if(XY[0]>241 && XY[1]>81 && XY[0]<329  && XY[1]<116 )
				{
				    //622.08
					VIEWTECH_70(39);
					VIEWTECH_71(40,241,81,329,116,241,81);
					rate = cpri622;
					Delay_Ms(250);
				}
				else	if(XY[0]>340 && XY[1]>81 && XY[0]<430  && XY[1]<116 )
				{
				    //3g
					VIEWTECH_70(39);
					VIEWTECH_71(40,340,81,430,116,340,81);
					rate = cpri3;
					Delay_Ms(250);
				}
				else	if(XY[0]>44 && XY[1]>120 && XY[0]<132  && XY[1]<154 )
				{
				    //6g
					VIEWTECH_70(39);
					VIEWTECH_71(40,44,120,132,154,44,120);
					rate = cpri6;
					Delay_Ms(250);
				}
				else	if(XY[0]>143 && XY[1]>120 && XY[0]<230  && XY[1]<154 )
				{
				    //9g
					VIEWTECH_70(39);
					VIEWTECH_71(40,143,120,230,154,143,120);
					rate = cpri9;
					Delay_Ms(250);
				}
				else	if(XY[0]>241 && XY[1]>120 && XY[0]<329  && XY[1]<154 )
				{
				    //300m
					VIEWTECH_70(39);
					VIEWTECH_71(40,241,120,329,154,241,120);
					rate = cpri300;
					Delay_Ms(250);
				}

				else 
				{
					Delay_Ms(150);		
				}
//				TX_Rate = 0;
//		    update_status(); 			
}


void Menu_UDEFRate(void)
{
	  u16 def_h = 10;
    u16 def_m = 312;
    u16 def_s = 500;
	
        VIEWTECH_70(38);
	def_h = (u16)(def_rate/1000);
	def_m = (u16)def_rate%1000;
	def_s = (u16)((u32)(def_rate*1000)%1000%1000);
			  
	      VIEWTECH_A01 (235,142,0x20e3,0x07E0,0xffff,def_h);	 	
			  VIEWTECH_A01 (235,233,0x30e3,0x07E0,0xffff,def_m);	 	
			  VIEWTECH_A01 (235,323,0x30e3,0x07E0,0xffff,def_s);	 	 
		             		 
			 	 while(1)					
				{
           xls[1]=0;									 /* 坐标清0 */
				   xls[2]=0;
				   XY[0]=0;
				   XY[1]=0;
		
		      while((XY[0]>1 && XY[0]<799 && XY[1]>1 && XY[1]<479)==0)		/* 再次取坐标 */ 
			     {
					XY[0]=xls[1]<<8;
					XY[0]+=xls[2];
					XY[1]=xls[3]<<8;
					XY[1]+=xls[4];
					Delay_Ms(10);
			     }   	
					 
					  if(XY[0]>195 && XY[1]>132 && XY[0]<304  && XY[1]<188 && xls[0]==0x03)		 //G
		  		 {
						 VIEWTECH_7C01 (450,130,2,0x00);
						 VIEWTECH_71(38,195,132,304,188,195,132);
//				     VIEWTECH_7C04 (450,70,2,0x40,235,142,0x23,0x90,3,0x001f,0x39c8);
						 while(1)
             {
   
               if(biaoji == Virtual_keyboard)
	             {  
								 if(xls[2]==2)  //
							   {
								    if(((xls[3]==48 && xls[4]>=48 && xls[4]<=57) || (xls[3]==49 && xls[4]>=48 && xls[4]<=54))) //判断时间设置-小时 是否合理
								    { 
							  	   def_h = (xls[3]-48)*10+(xls[4]-48);
											VIEWTECH_A01 (235,142,0x20e3,0x001f,0xffff,def_h);	
											VIEWTECH_71(38,450,130,800,400,450,130);											
                    }
										else 
										{
								   	 VIEWTECH_98 (210,142,0x23,0x90,3,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/	
											VIEWTECH_71(38,450,130,800,400,450,130);											
                    }
										
								 	}
									else if(xls[2]==1)
									{
 	                   if(xls[3]>=48 && xls[3]<=57)   //判断时间设置-小时 是否合理
								    { 
							  	   def_h = (xls[3]-48);
											VIEWTECH_A01 (235,142,0x20e3,0x001f,0xffff,def_h);	
											VIEWTECH_71(38,450,130,800,400,450,130);											
                    }
										else 
										{
								   	 VIEWTECH_98 (210,142,0x23,0x90,3,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/
											VIEWTECH_71(38,450,130,800,400,450,130);											
                    }
                  }	
							  	else
									{
										VIEWTECH_98 (210,142,0x23,0x90,3,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/
										VIEWTECH_71(38,450,130,800,400,450,130);
                  }
							  biaoji = No_touch;
								break;
	             }
             }
				   }
			    	else if(XY[0]>195 && XY[1]>223 && XY[0]<304  && XY[1]<279 && xls[0]==0x03 )		      //M
				   {
						 VIEWTECH_7C01 (450,130,2,0x00);
						 VIEWTECH_71(38,195,223,304,279,195,223);
//				     VIEWTECH_7C04 (450,70,3,0x40,235,233,0x23,0x90,3,0x001f,0x39c8);
						 while(1)
             {
   
               if(biaoji == Virtual_keyboard)
	             {
									if(xls[2]==3)
							   {
								    if(xls[3]>=48 && xls[3]<=57  && xls[4]>=48 && xls[4]<=57 && xls[5]>=48 && xls[5]<=57)  //判断时间设置 分钟 是否合理
								    { 
							  	   def_m = (xls[3]-48)*100+(xls[4]-48)*10+(xls[5]-48);
											VIEWTECH_A01 (235,233,0x30e3,0x001f,0xffff,def_m);	
											VIEWTECH_71(38,450,130,800,400,450,130);											
                    }
										else 
										{
								   	 VIEWTECH_98 (210,233,0x23,0x90,3,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/
										 VIEWTECH_71(38,450,130,800,400,450,130);											
                    }
										
								 	}								 
								 else if(xls[2]==2)
							   {
								    if(xls[3]>=48 && xls[3]<=57  && xls[4]>=48 && xls[4]<=57)  //判断时间设置 分钟 是否合理
								    { 
							  	   def_m = (xls[3]-48)*10+(xls[4]-48);
											VIEWTECH_A01 (235,233,0x30e3,0x001f,0xffff,def_m);	
											VIEWTECH_71(38,450,130,800,400,450,130);											
                    }
										else 
										{
								   	 VIEWTECH_98 (210,233,0x23,0x90,3,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/
											VIEWTECH_71(38,450,130,800,400,450,130);											
                    }
										
								 	}
									else if(xls[2]==1)
									{
 	                   if(xls[3]>=48 && xls[3]<=57)   //判断时间设置-分钟 是否合理
								    { 
							  	   def_m = (xls[3]-48);
											VIEWTECH_A01 (235,233,0x30e3,0x001f,0xffff,def_m);	
											VIEWTECH_71(38,450,130,800,400,450,130);											
                    }
										else 
										{
								   	 VIEWTECH_98 (210,233,0x23,0x90,3,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/	
											VIEWTECH_71(38,450,130,800,400,450,130);											
                    }
                  }	
							  	else
									{
										VIEWTECH_98 (210,233,0x23,0x90,3,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/
										VIEWTECH_71(38,450,130,800,400,450,130);
                  }
							  biaoji = No_touch;
								break;
	             }
             }
				   }
					 else if(XY[0]>195 && XY[1]>313 && XY[0]<304  && XY[1]<369 && xls[0]==0x03 )		  //K
				   {
						 VIEWTECH_7C01 (450,130,2,0x00);
						 VIEWTECH_71(38,195,313,304,369,195,313);
//				     VIEWTECH_7C04 (450,70,3,0x40,235,323,0x23,0x90,3,0x001f,0x39c8);
						 while(1)
             {
   
                if(biaoji == Virtual_keyboard)
	             { 
								 if(xls[2]==3)
							   {
								    if(xls[3]>=48 && xls[3]<=57  && xls[4]>=48 && xls[4]<=57 && xls[5]>=48 && xls[5]<=57)  //判断时间设置-秒 是否合理
								    { 
							  	   def_s = (xls[3]-48)*100+(xls[4]-48)*10 + (xls[5]-48);
											VIEWTECH_A01 (235,323,0x30e3,0x001f,0xffff,def_s);
											VIEWTECH_71(38,450,130,800,400,450,130);											
                    }
										else 
										{
								   	 VIEWTECH_98 (210,323,0x23,0x90,3,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/	
											VIEWTECH_71(38,450,130,800,400,450,130);
                    }
										
								 	}
								 else if(xls[2]==2)
							   {
								    if(xls[3]>=48 && xls[3]<=57  && xls[4]>=48 && xls[4]<=57)  //判断时间设置-秒 是否合理
								    { 
							  	   def_s = (xls[3]-48)*10+(xls[4]-48);
												VIEWTECH_A01 (235,323,0x30e3,0x001f,0xffff,def_s);
											VIEWTECH_71(38,450,130,800,400,450,130);
                    }
										else 
										{
								   	 VIEWTECH_98 (210,323,0x23,0x90,3,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/	
												VIEWTECH_71(38,450,130,800,400,450,130);
                    }
										
								 	}
									else if(xls[2]==1)
									{
 	                   if(xls[3]>=48 && xls[3]<=57)   //判断时间设置 秒 是否合理
								    { 
							  	   def_s = (xls[3]-48);
												VIEWTECH_A01 (235,323,0x30e3,0x001f,0xffff,def_s);
											VIEWTECH_71(38,450,130,800,400,450,130);
                    }
										else 
										{
								   	 VIEWTECH_98 (210,323,0x23,0x90,3,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/	
												VIEWTECH_71(38,450,130,800,400,450,130);
                    }
                  }	
							  	else
									{
										VIEWTECH_98 (210,323,0x23,0x90,3,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/
										VIEWTECH_71(38,450,130,800,400,450,130);
                  }
							  biaoji = No_touch;
								break;
	             }
             }
				   }
					 else if(XY[0]>659 && XY[1]>406 && XY[0]<791  && XY[1]<465 )		 //确定
				   { 
						 VIEWTECH_71(28,659,406,791,465,659,406);
						 def_rate = (float)(def_h*1000 + (double)def_m  + (float)def_s/1000);
						 
						 if((def_rate < 128.0)||(def_rate >= 17000.0)) reconfigRate();						 
						 
						 TX_Rate = 0;
						 Delay_Ms(10);	
						 
				     break;
	         }
					 else if(XY[0]>14 && XY[1]>406 && XY[0]<146  && XY[1]<465 )		  //取消
				   { 
						 VIEWTECH_71(28,14,406,146,465,14,406);
						 Delay_Ms(10);	
						 
				     break;
	         }
				 }
				 update_status(); 

}

void reconfigRate(void)
{
		VIEWTECH_71(36,199,107,601,369,199,107);
	
				 while(1)					
				{
           xls[1]=0;									 /* ???0 */
				   xls[2]=0;
				   XY[0]=0;
				   XY[1]=0;
		
		      while((XY[0]>1 && XY[0]<799 && XY[1]>1 && XY[1]<479)==0)		/* ????? */ 
			     {
					XY[0]=xls[1]<<8;
					XY[0]+=xls[2];
					XY[1]=xls[3]<<8;
					XY[1]+=xls[4];
					Delay_Ms(10);
			     } 
					 
					 if(XY[0]>335 && XY[1]>296 && XY[0]<466  && XY[1]<354 )
				  {
						
							Delay_Ms(100);
							Menu_UDEFRate();
							break;							
						
					}
				}	
	
}



/*******************************************************************************
* Function Name  : Menu_RTC
* Description    : RTC时钟设置界面操作
* Input          : None
* Output         : None
* Return         : None
* Attention		 : None
*******************************************************************************/
void Menu_RTC(void)
{
		u8 rtc_year  = 0x0f;
    u8 rtc_month = 0x07;
    u8 rtc_date  = 0x0B;
	  u8 rtc_h = 0x08;
    u8 rtc_m = 0x08;
    u8 rtc_s = 0x08;
	
        VIEWTECH_70(37);
  
		  	VIEWTECH_9B5A();
			  while(1)
        {
          if(biaoji == Time_read)
	        {
						rtc_year  = xls[2] ;
            rtc_month = xls[3] ;
            rtc_date  = xls[4] ;
						rtc_h  = xls[6] ;
            rtc_m  = xls[7] ;
            rtc_s  = xls[8] ;
						biaoji = No_touch;
						break;
          } 
				}
			  
	      VIEWTECH_A01 (235,142,0x20e3,0x07E0,0x39c8,rtc_h);	 	
			  VIEWTECH_A01 (235,233,0x20e3,0x07E0,0x39c8,rtc_m);	 	
			  VIEWTECH_A01 (235,323,0x20e3,0x07E0,0x39c8,rtc_s);	 	 
		             		 
			 	 while(1)					
				{
           xls[1]=0;									 /* 坐标清0 */
				   xls[2]=0;
				   XY[0]=0;
				   XY[1]=0;
		
		      while((XY[0]>1 && XY[0]<799 && XY[1]>1 && XY[1]<479)==0)		/* 再次取坐标 */ 
			     {
					XY[0]=xls[1]<<8;
					XY[0]+=xls[2];
					XY[1]=xls[3]<<8;
					XY[1]+=xls[4];
					Delay_Ms(10);
			     }   	
					 
					  if(XY[0]>195 && XY[1]>132 && XY[0]<304  && XY[1]<188 && xls[0]==0x03)		 //时
		  		 {
						 VIEWTECH_71(37,195,132,304,188,195,132);
//				     VIEWTECH_7C04 (450,70,2,0x40,235,142,0x23,0x90,3,0x001f,0x39c8);
						 while(1)
             {
   
               if(biaoji == Virtual_keyboard)
	             {  
								 if(xls[2]==2)
							   {
								    if(((xls[3]==48 && xls[4]>=48 && xls[4]<=57) || (xls[3]==49 && xls[4]>=48 && xls[4]<=57))||(xls[3]==50 && xls[4]>=48 && xls[4]<=52)) //判断时间设置-小时 是否合理
								    { 
							  	   rtc_h = (xls[3]-48)*10+(xls[4]-48);							    
                    }
										else 
										{
								   	 VIEWTECH_98 (205,142,0x23,0x90,3,0xf800,0x39c8,ERR_rtc,5);		 /*输入错误*/									   
                    }
										
								 	}
									else if(xls[2]==1)
									{
 	                   if(xls[3]>=48 && xls[3]<=57)   //判断时间设置-小时 是否合理
								    { 
							  	   rtc_h = (xls[3]-48);							       
                    }
										else 
										{
								   	 VIEWTECH_98 (205,142,0x23,0x90,3,0xf800,0x39c8,ERR_rtc,5);		 /*输入错误*/									   
                    }
                  }	
							  	else
									{
										VIEWTECH_98 (205,142,0x23,0x90,3,0xf800,0x39c8,ERR_rtc,5);		 /*输入错误*/
                  }
							  biaoji = No_touch;
								break;
	             }
             }
				   }
			    	else if(XY[0]>195 && XY[1]>223 && XY[0]<304  && XY[1]<279 && xls[0]==0x03 )		      //分
				   {
						 VIEWTECH_71(37,195,223,304,279,195,223);
//				     VIEWTECH_7C04 (450,70,2,0x40,235,233,0x23,0x90,3,0x001f,0x39c8);
						 while(1)
             {
   
               if(biaoji == Virtual_keyboard)
	             { 
								 if(xls[2]==2)
							   {
								    if(xls[3]>=48 && xls[3]<=53  && xls[4]>=48 && xls[4]<=57)  //判断时间设置 分钟 是否合理
								    { 
							  	   rtc_m = (xls[3]-48)*10+(xls[4]-48);							    
                    }
										else 
										{
								   	 VIEWTECH_98 (205,233,0x23,0x90,3,0xf800,0x39c8,ERR_rtc,5);		 /*输入错误*/									   
                    }
										
								 	}
									else if(xls[2]==1)
									{
 	                   if(xls[3]>=48 && xls[3]<=57)   //判断时间设置-分钟 是否合理
								    { 
							  	   rtc_m = (xls[3]-48);							       
                    }
										else 
										{
								   	 VIEWTECH_98 (205,233,0x23,0x90,3,0xf800,0x39c8,ERR_rtc,5);		 /*输入错误*/									   
                    }
                  }	
							  	else
									{
										VIEWTECH_98 (205,233,0x23,0x90,3,0xf800,0x39c8,ERR_rtc,5);		 /*输入错误*/
                  }
							  biaoji = No_touch;
								break;
	             }
             }
				   }
					 else if(XY[0]>195 && XY[1]>313 && XY[0]<304  && XY[1]<369 && xls[0]==0x03 )		  //秒
				   {
						 VIEWTECH_71(37,195,313,304,369,195,313);
//				     VIEWTECH_7C04 (450,70,2,0x40,235,323,0x23,0x90,3,0x001f,0x39c8);
						 while(1)
             {
   
                if(biaoji == Virtual_keyboard)
	             { 
								 if(xls[2]==2)
							   {
								    if(xls[3]>=48 && xls[3]<=53  && xls[4]>=48 && xls[4]<=57)  //判断时间设置-秒 是否合理
								    { 
							  	   rtc_s = (xls[3]-48)*10+(xls[4]-48);							    
                    }
										else 
										{
								   	 VIEWTECH_98 (205,323,0x23,0x90,3,0xf800,0x39c8,ERR_rtc,5);		 /*输入错误*/									   
                    }
										
								 	}
									else if(xls[2]==1)
									{
 	                   if(xls[3]>=48 && xls[3]<=57)   //判断时间设置 秒 是否合理
								    { 
							  	   rtc_s = (xls[3]-48);							       
                    }
										else 
										{
								   	 VIEWTECH_98 (205,323,0x23,0x90,3,0xf800,0x39c8,ERR_rtc,5);		 /*输入错误*/									   
                    }
                  }	
							  	else
									{
										VIEWTECH_98 (205,323,0x23,0x90,3,0xf800,0x39c8,ERR_rtc,5);		 /*输入错误*/
                  }
							  biaoji = No_touch;
								break;
	             }
             }
				   }
					 else if(XY[0]>659 && XY[1]>406 && XY[0]<791  && XY[1]<465 )		 
				   { 
						 VIEWTECH_71(28,659,406,791,465,659,406);
						 VIEWTECH_E7(rtc_year,rtc_month,rtc_date,rtc_h,rtc_m,rtc_s);
						 Delay_Ms(150);	
						 
				     break;
	         }
					 else if(XY[0]>14 && XY[1]>406 && XY[0]<146  && XY[1]<465 )		 
				   { 
						 VIEWTECH_71(28,14,406,146,465,14,406);
						 Delay_Ms(150);	
						 
				     break;
	         }
				 }
				 update_status(); 

}

void Menu_UDEFT(void)
{
	  u8 def_h = 0x00;
    u8 def_m = 0x00;
    u8 def_s = 0x00;
	  u8 cover1=0;
		u8 cover2=0;
		u8 cover3=0;
	
	
        VIEWTECH_70(37);
	def_h = timer_set[curchan]/3600;
	def_m = timer_set[curchan]%3600/60;
	def_s = timer_set[curchan]%3600%60;
			  
	      VIEWTECH_A01 (140,78,0x20e2,0x07E0,0xffff,def_h);	 	
			  VIEWTECH_A01 (140,129,0x20e2,0x07E0,0xffff,def_m);	 	
			  VIEWTECH_A01 (140,180,0x20e2,0x07E0,0xffff,def_s);	 	 
		             		 
			 	 while(1)					
				{
           xls[1]=0;									 /* 坐标清0 */
				   xls[2]=0;
				   XY[0]=0;
				   XY[1]=0;
		
		      while((XY[0]>1 && XY[0]<479 && XY[1]>1 && XY[1]<272)==0)		/* 再次取坐标 */ 
			     {
					XY[0]=xls[1]<<8;
					XY[0]+=xls[2];
					XY[1]=xls[3]<<8;
					XY[1]+=xls[4];
					Delay_Ms(10);
			     }   	
					 
					  if(XY[0]>117 && XY[1]>76 && XY[0]<184  && XY[1]<108 && xls[0]==0x03)		 //时
		  		 {
						 VIEWTECH_7C01 (210,66,16,0x00);
						 VIEWTECH_71(37,117,76,184,108,117,76);
//				     VIEWTECH_7C04 (450,70,2,0x40,235,142,0x23,0x90,3,0x001f,0x39c8);
						 while(1)
             {
   
               if(biaoji == Virtual_keyboard)
	             {  
								 if(xls[2]==2)
							   {
								    if(((xls[3]==48 && xls[4]>=48 && xls[4]<=57) || (xls[3]==49 && xls[4]>=48 && xls[4]<=57))||(xls[3]==50 && xls[4]>=48 && xls[4]<=52)) //判断时间设置-小时 是否合理
								    { 
							  	   def_h = (xls[3]-48)*10+(xls[4]-48);
											cover1 =1;
											VIEWTECH_70(37);
											if(cover1==1) VIEWTECH_A01 (140,78,0x20e2,0x001f,0xffff,def_h);
											else VIEWTECH_A01 (140,78,0x20e2,0x07E0,0xffff,def_h);
											if(cover2==1) VIEWTECH_A01 (140,129,0x20e2,0x001f,0xffff,def_m);
											else VIEWTECH_A01 (140,129,0x20e2,0x07E0,0xffff,def_m);
											if(cover3 == 1) VIEWTECH_A01 (140,180,0x20e2,0x001f,0xffff,def_s);
											else 	VIEWTECH_A01 (140,180,0x20e2,0x07E0,0xffff,def_s);										
																						
                    }
										else 
										{
											VIEWTECH_70(37);
											VIEWTECH_71(37,117,76,184,108,117,76);
								   	  VIEWTECH_98 (130,81,0x21,0x90,1,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/	
											if(cover2==1) VIEWTECH_A01 (140,129,0x20e2,0x001f,0xffff,def_m);
											else VIEWTECH_A01 (140,129,0x20e2,0x07E0,0xffff,def_m);
											if(cover3 == 1) VIEWTECH_A01 (140,180,0x20e2,0x001f,0xffff,def_s);
											else 	VIEWTECH_A01 (140,180,0x20e2,0x07E0,0xffff,def_s);										
                    }
										
								 	}
									else if(xls[2]==1)
									{
 	                   if(xls[3]>=48 && xls[3]<=57)   //判断时间设置-小时 是否合理
								    { 
							  	   def_h = (xls[3]-48);
											cover1 =1;
											VIEWTECH_70(37);
											if(cover1==1) VIEWTECH_A01 (140,78,0x20e2,0x001f,0xffff,def_h);
											else VIEWTECH_A01 (140,78,0x20e2,0x07E0,0xffff,def_h);
											if(cover2==1) VIEWTECH_A01 (140,129,0x20e2,0x001f,0xffff,def_m);
											else VIEWTECH_A01 (140,129,0x20e2,0x07E0,0xffff,def_m);
											if(cover3 == 1) VIEWTECH_A01 (140,180,0x20e2,0x001f,0xffff,def_s);
											else 	VIEWTECH_A01 (140,180,0x20e2,0x07E0,0xffff,def_s);												
                    }
										else 
										{
								   	 //VIEWTECH_98 (205,142,0x23,0x90,3,0xf800,0x39c8,ERR_rtc,5);		 /*输入错误*/
											VIEWTECH_70(37);
											VIEWTECH_71(37,117,76,184,108,117,76);
								   	  VIEWTECH_98 (130,81,0x21,0x90,1,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/	
											if(cover2==1) VIEWTECH_A01 (140,129,0x20e2,0x001f,0xffff,def_m);
											else VIEWTECH_A01 (140,129,0x20e2,0x07E0,0xffff,def_m);
											if(cover3 == 1) VIEWTECH_A01 (140,180,0x20e2,0x001f,0xffff,def_s);
											else 	VIEWTECH_A01 (140,180,0x20e2,0x07E0,0xffff,def_s);											
                    }
                  }	
							  	else
									{
										//VIEWTECH_98 (205,142,0x23,0x90,3,0xf800,0x39c8,ERR_rtc,5);		 /*输入错误*/
											VIEWTECH_70(37);
											VIEWTECH_71(37,117,76,184,108,117,76);
								   	  VIEWTECH_98 (130,81,0x21,0x90,1,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/	
											if(cover2==1) VIEWTECH_A01 (140,129,0x20e2,0x001f,0xffff,def_m);
											else VIEWTECH_A01 (140,129,0x20e2,0x07E0,0xffff,def_m);
											if(cover3 == 1) VIEWTECH_A01 (140,180,0x20e2,0x001f,0xffff,def_s);
											else 	VIEWTECH_A01 (140,180,0x20e2,0x07E0,0xffff,def_s);
                  }
							  biaoji = No_touch;
								break;
	             }
             }
				   }
			    	else if(XY[0]>117 && XY[1]>127 && XY[0]<184  && XY[1]<159 && xls[0]==0x03 )		      //分
				   {
						 VIEWTECH_7C01 (210,66,16,0x00);
						 VIEWTECH_71(37,117,127,184,159,117,127);
//				     VIEWTECH_7C04 (450,70,2,0x40,235,233,0x23,0x90,3,0x001f,0x39c8);
						 while(1)
             {
   
               if(biaoji == Virtual_keyboard)
	             { 
								 if(xls[2]==2)
							   {
								    if(xls[3]>=48 && xls[3]<=53  && xls[4]>=48 && xls[4]<=57)  //判断时间设置 分钟 是否合理
								    { 
							  	   def_m = (xls[3]-48)*10+(xls[4]-48);
											cover2 =1;
											VIEWTECH_70(37);
											if(cover1==1) VIEWTECH_A01 (140,78,0x20e2,0x001f,0xffff,def_h);
											else VIEWTECH_A01 (140,78,0x20e2,0x07E0,0xffff,def_h);
											if(cover2==1) VIEWTECH_A01 (140,129,0x20e2,0x001f,0xffff,def_m);
											else VIEWTECH_A01 (140,129,0x20e2,0x07E0,0xffff,def_m);
											if(cover3 == 1) VIEWTECH_A01 (140,180,0x20e2,0x001f,0xffff,def_s);
											else 	VIEWTECH_A01 (140,180,0x20e2,0x07E0,0xffff,def_s);											
                    }
										else 
										{
											VIEWTECH_70(37);
											if(cover1==1) VIEWTECH_A01 (140,78,0x20e2,0x001f,0xffff,def_h);
											else VIEWTECH_A01 (140,78,0x20e2,0x07E0,0xffff,def_h);
											if(cover3 == 1) VIEWTECH_A01 (140,180,0x20e2,0x001f,0xffff,def_s);
											else 	VIEWTECH_A01 (140,180,0x20e2,0x07E0,0xffff,def_s);
											VIEWTECH_71(37,117,127,184,159,117,127);
								   	  VIEWTECH_98 (130,132,0x21,0x90,1,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/									   
                    }
										
								 	}
									else if(xls[2]==1)
									{
 	                   if(xls[3]>=48 && xls[3]<=57)   //判断时间设置-分钟 是否合理
								    { 
							  	   def_m = (xls[3]-48);	
										cover2 =1;
											VIEWTECH_70(37);
											if(cover1==1) VIEWTECH_A01 (140,78,0x20e2,0x001f,0xffff,def_h);
											else VIEWTECH_A01 (140,78,0x20e2,0x07E0,0xffff,def_h);
											if(cover2==1) VIEWTECH_A01 (140,129,0x20e2,0x001f,0xffff,def_m);
											else VIEWTECH_A01 (140,129,0x20e2,0x07E0,0xffff,def_m);
											if(cover3 == 1) VIEWTECH_A01 (140,180,0x20e2,0x001f,0xffff,def_s);
											else 	VIEWTECH_A01 (140,180,0x20e2,0x07E0,0xffff,def_s);	
                    }
										else 
										{
								   	 //VIEWTECH_98 (205,233,0x23,0x90,3,0xf800,0x39c8,ERR_rtc,5);		 /*输入错误*/	
											VIEWTECH_70(37);
											if(cover1==1) VIEWTECH_A01 (140,78,0x20e2,0x001f,0xffff,def_h);
											else VIEWTECH_A01 (140,78,0x20e2,0x07E0,0xffff,def_h);
											if(cover3 == 1) VIEWTECH_A01 (140,180,0x20e2,0x001f,0xffff,def_s);
											else 	VIEWTECH_A01 (140,180,0x20e2,0x07E0,0xffff,def_s);
											VIEWTECH_71(37,117,127,184,159,117,127);
								   	  VIEWTECH_98 (130,132,0x21,0x90,1,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/												
                    }
                  }	
							  	else
									{
										//VIEWTECH_98 (205,233,0x23,0x90,3,0xf800,0x39c8,ERR_rtc,5);		 /*输入错误*/
										VIEWTECH_70(37);
											if(cover1==1) VIEWTECH_A01 (140,78,0x20e2,0x001f,0xffff,def_h);
											else VIEWTECH_A01 (140,78,0x20e2,0x07E0,0xffff,def_h);
											if(cover3 == 1) VIEWTECH_A01 (140,180,0x20e2,0x001f,0xffff,def_s);
											else 	VIEWTECH_A01 (140,180,0x20e2,0x07E0,0xffff,def_s);
											VIEWTECH_71(37,117,127,184,159,117,127);
								   	  VIEWTECH_98 (130,132,0x21,0x90,1,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/	
                  }
							  biaoji = No_touch;
								break;
	             }
             }
				   }
					 else if(XY[0]>117 && XY[1]>178 && XY[0]<184  && XY[1]<210 && xls[0]==0x03 )		  //秒
				   {
						 VIEWTECH_7C01 (210,66,16,0x00);
						 VIEWTECH_71(37,117,178,184,210,117,178);
//				     VIEWTECH_7C04 (450,70,2,0x40,235,323,0x23,0x90,3,0x001f,0x39c8);
						 while(1)
             {
   
                if(biaoji == Virtual_keyboard)
	             { 
								 if(xls[2]==2)
							   {
								    if(xls[3]>=48 && xls[3]<=53  && xls[4]>=48 && xls[4]<=57)  //判断时间设置-秒 是否合理
								    { 
							  	   def_s = (xls[3]-48)*10+(xls[4]-48);	
											cover3 =1;
											VIEWTECH_70(37);
											if(cover1==1) VIEWTECH_A01 (140,78,0x20e2,0x001f,0xffff,def_h);
											else VIEWTECH_A01 (140,78,0x20e2,0x07E0,0xffff,def_h);
											if(cover2==1) VIEWTECH_A01 (140,129,0x20e2,0x001f,0xffff,def_m);
											else VIEWTECH_A01 (140,129,0x20e2,0x07E0,0xffff,def_m);
											if(cover3 == 1) VIEWTECH_A01 (140,180,0x20e2,0x001f,0xffff,def_s);
											else 	VIEWTECH_A01 (140,180,0x20e2,0x07E0,0xffff,def_s);											
                    }
										else 
										{
											VIEWTECH_70(37);
											if(cover1==1) VIEWTECH_A01 (140,78,0x20e2,0x001f,0xffff,def_h);
											else VIEWTECH_A01 (140,78,0x20e2,0x07E0,0xffff,def_h);
											if(cover2==1) VIEWTECH_A01 (140,129,0x20e2,0x001f,0xffff,def_m);
											else VIEWTECH_A01 (140,129,0x20e2,0x07E0,0xffff,def_m);
											VIEWTECH_71(37,117,178,184,210,117,178);
								   	 VIEWTECH_98 (130,183,0x21,0x90,1,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/									   
                    }
										
								 	}
									else if(xls[2]==1)
									{
 	                   if(xls[3]>=48 && xls[3]<=57)   //判断时间设置 秒 是否合理
								    { 
							  	   def_s = (xls[3]-48);	
											cover3 =1;
											VIEWTECH_70(37);
											if(cover1==1) VIEWTECH_A01 (140,78,0x20e2,0x001f,0xffff,def_h);
											else VIEWTECH_A01 (140,78,0x20e2,0x07E0,0xffff,def_h);
											if(cover2==1) VIEWTECH_A01 (140,129,0x20e2,0x001f,0xffff,def_m);
											else VIEWTECH_A01 (140,129,0x20e2,0x07E0,0xffff,def_m);
											if(cover3 == 1) VIEWTECH_A01 (140,180,0x20e2,0x001f,0xffff,def_s);
											else 	VIEWTECH_A01 (140,180,0x20e2,0x07E0,0xffff,def_s);											
                    }
										else 
										{
								   	 VIEWTECH_70(37);
											if(cover1==1) VIEWTECH_A01 (140,78,0x20e2,0x001f,0xffff,def_h);
											else VIEWTECH_A01 (140,78,0x20e2,0x07E0,0xffff,def_h);
											if(cover2==1) VIEWTECH_A01 (140,129,0x20e2,0x001f,0xffff,def_m);
											else VIEWTECH_A01 (140,129,0x20e2,0x07E0,0xffff,def_m);
											VIEWTECH_71(37,117,178,184,210,117,178);
								   	 VIEWTECH_98 (130,183,0x21,0x90,1,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/									   
                    }
                  }	
							  	else
									{
										VIEWTECH_70(37);
											if(cover1==1) VIEWTECH_A01 (140,78,0x20e2,0x001f,0xffff,def_h);
											else VIEWTECH_A01 (140,78,0x20e2,0x07E0,0xffff,def_h);
											if(cover2==1) VIEWTECH_A01 (140,129,0x20e2,0x001f,0xffff,def_m);
											else VIEWTECH_A01 (140,129,0x20e2,0x07E0,0xffff,def_m);
											VIEWTECH_71(37,117,178,184,210,117,178);
								   	 VIEWTECH_98 (130,183,0x21,0x90,1,0xf800,0xffff,ERR_rtc,5);		 /*输入错误*/	
                  }
							  biaoji = No_touch;
								break;
	             }
             }
				   }
					 else if(XY[0]>395 && XY[1]>230 && XY[0]<476  && XY[1]<266 )		 //确定
				   { 
						 VIEWTECH_71(28,395,230,476,266,395,230);
						 timer_set[curchan] = def_h*3600 + def_m *60 + def_s;
						 Delay_Ms(10);	
						 
				     break;
	         }
					 else if(XY[0]>8 && XY[1]>230 && XY[0]<90  && XY[1]<266 )		  //取消
				   { 
						 VIEWTECH_71(28,8,230,90,266,8,230);
						 Delay_Ms(10);	
						 
				     break;
	         }
				 }
				 update_status(); 

}


/*******************************************************************************
* Function Name  : draw_gang
* Description    : 划线程序
* Input          : None
* Output         : None
* Return         : None
* Attention		 : None
*******************************************************************************/
void draw_gang(void)
{
  VIEWTECH_98 (138,41,0x21,0x90,1,0x0000,0x85fb,HGANG,12);
  VIEWTECH_98 (138,61,0x21,0x90,1,0x0000,0x85fb,HGANG,12);
  VIEWTECH_98 (138,87,0x21,0x90,1,0x0000,0x85fb,HGANG,12);
  VIEWTECH_98 (138,107,0x21,0x90,1,0x0000,0x85fb,HGANG,12);

  VIEWTECH_98 (138,133,0x21,0x90,1,0x0000,0x85fb,HGANG,12);
  VIEWTECH_98 (138,153,0x21,0x90,1,0x0000,0x85fb,HGANG,12);
  VIEWTECH_98 (138,179,0x21,0x90,1,0x0000,0x85fb,HGANG,12);
  VIEWTECH_98 (138,199,0x21,0x90,1,0x0000,0x85fb,HGANG,12);
//  VIEWTECH_98 (265,337,0x23,0x90,3,0x2e9f,0x85fb,HGANG1,3);
}

void update_status(void)    //更换右侧 码型 速率 定时 的参数
{
        VIEWTECH_70(2);	              //切换到主界面2
			  draw_gang();

			  // 显示网络状态在LCD顶部
			  show_network_status();
	
	    TX_First=0;
			
			if((TX_Status&0x01)==1) 
			{				
				VIEWTECH_71(2,5,46,27,71,5,46);
			}
			else if((TX_Status&0x01)==0) 
			{				
				VIEWTECH_71(25,5,46,27,71,5,46);
			}
			
			if((TX_Status&0x02)==2) 
			{				
				VIEWTECH_71(2,5,92,27,117,5,92);
			}
			else if((TX_Status&0x02)==0) 
			{				
				VIEWTECH_71(25,5,92,27,117,5,92);
			}
			
			if((TX_Status&0x04)==4)
			{				
				VIEWTECH_71(2,5,138,27,163,5,138);
			}
			else if((TX_Status&0x04)==0) 
			{				
				VIEWTECH_71(25,5,138,27,163,5,138);
			}
				
			if((TX_Status&0x08)==8) 
			{					
				VIEWTECH_71(2,5,184,27,209,5,184);
			}
			else if((TX_Status&0x08)==0) 
			{				
				VIEWTECH_71(25,5,184,27,209,5,184);
			}

			if((RUN_FLAG==noRuning)&&(RUN_FLAG1==noRuning1)&&(RUN_FLAG2==noRuning2)&&(RUN_FLAG3==noRuning3))
			{
				VIEWTECH_71(25,273,50,359,210,273,50);				
			}
			else if(RUN_FLAG==Runing)			VIEWTECH_71(12,273,50,359,73,273,50);	
			else if(RUN_FLAG1==Runing1)			VIEWTECH_71(12,273,96,359,119,273,96);	
			else if(RUN_FLAG2==Runing2)			VIEWTECH_71(12,273,142,359,165,273,142);
			else if(RUN_FLAG3==Runing3)			VIEWTECH_71(12,273,188,359,210,273,188);			
			else{}	
				
			
	
				if(mode[0] == TIMER_EN)
				  {
						VIEWTECH_71(12,402,40,468,60,402,40);    //---miao 
						 VIEWTECH_A01 (396,40,0x6021,0x000a,0x39c8,((u32)(timer_set[0])));   //miaoshu
//						 VIEWTECH_71(2,626,117,780,141,626,117);				//zanting	
					}
					else VIEWTECH_71(2,402,40,468,60,402,40);	  //wudingshi

				if(mode[1] == TIMER_EN)
				  {		
							VIEWTECH_71(12,402,86,468,106,402,86);    //---miao 
						 VIEWTECH_A01 (396,86,0x6021,0x000a,0x39c8,((u32)(timer_set[1])));   //miaoshu
//						 VIEWTECH_71(2,626,195,780,219,626,195);				//zanting	
					}
					else VIEWTECH_71(2,402,86,468,106,402,86);	
				
				if(mode[2] == TIMER_EN)
				  {						
						VIEWTECH_71(12,402,132,468,152,402,132);    //---miao 
						 VIEWTECH_A01 (396,132,0x6021,0x000a,0x39c8,((u32)(timer_set[2])));   //miaoshu
//						 VIEWTECH_71(2,626,273,780,297,626,273);				//zanting	
					}
					else  VIEWTECH_71(2,402,132,468,152,402,132);
						
					if(mode[3] == TIMER_EN)
				  {	
					VIEWTECH_71(12,402,180,468,200,402,180);    //---miao 	
						 VIEWTECH_A01 (396,178,0x6021,0x000a,0x39c8,((u32)(timer_set[3])));   //miaoshu
//						 VIEWTECH_71(2,626,356,780,380,626,356);				//zanting		
				  }
					else
					{
						VIEWTECH_71(2,402,180,468,200,402,180);						
					}

					switch(rate)
					{													
					  case shiGwan:
						case oc192:
						VIEWTECH_71(20,170,15,388,30,170,15);
						break;
					
					case epon:
						VIEWTECH_71(47,170,15,388,30,170,15);
						break;
						
					case gpon:
						VIEWTECH_71(48,170,15,388,30,170,15);
						break;
						
					case cpon:
						VIEWTECH_71(49,170,15,388,30,170,15);
						break;
					
					case xgpon:
						VIEWTECH_71(50,170,15,388,30,170,15);
						break;
						
					case xgspon:
						VIEWTECH_71(51,170,15,388,30,170,15);
						break;
					
					case cpri155:
						VIEWTECH_71(41,170,15,388,30,170,15);
						break;
						
					case cpri622:
						VIEWTECH_71(42,170,15,388,30,170,15);
						break;
					
					case cpri3:
						VIEWTECH_71(52,170,15,388,30,170,15);
						break;
						
					case cpri6:
						VIEWTECH_71(55,170,15,388,30,170,15);
						break;
					
					case cpri9:
						VIEWTECH_71(56,170,15,388,30,170,15);
						break;
					
					case cpri300:
						VIEWTECH_71(58,170,15,388,30,170,15);
						break;
//						case user_rate:
//						VIEWTECH_71(54,295,31,400,52,295,31);
//						VIEWTECH_A02 (283,26,0x522a,0x000a,0x39c8,def_rate);
//						
////						VIEWTECH_71(12,626,242,740,280,626,200);
////						  VIEWTECH_A01 (626,205,0x6022,0x000a,0x39c8,((u32)(timer_set)));
//						break;
						
						case shiwuG:
						VIEWTECH_71(21,170,15,388,30,170,15);
						break;

						case shiGlan:
						VIEWTECH_71(2,170,15,388,30,170,15);
						break;	
						
						case yiGe:
						VIEWTECH_71(11,170,15,388,30,170,15);
						break;
						
						case rxaui:
						VIEWTECH_71(14,170,15,388,30,170,15);
					  break;
					
						case xaui:
						VIEWTECH_71(15,170,15,388,30,170,15);
						break;
						
						case oc96:
						VIEWTECH_71(12,170,15,388,30,170,15);
					  break;
					
						case oc48:
						VIEWTECH_71(13,170,15,388,30,170,15);
						break;

//            case oc24:
//						VIEWTECH_71(17,526,116,745,156,526,116);
//						break;	

//						case oc12:
//						VIEWTECH_71(22,526,116,745,156,526,116);
//						break;						
						
						case shiliuGfc:
						VIEWTECH_71(16,170,15,388,30,170,15);
						break;	

						case shiG_FC:
						VIEWTECH_71(23,170,15,388,30,170,15);
						break;	
						
						case baG_FC:
						VIEWTECH_71(19,170,15,388,30,170,15);
						break;

						case wuGinfi:
						VIEWTECH_71(18,170,15,388,30,170,15);
						break;	

						case siG_FC:
						VIEWTECH_71(24,170,15,388,30,170,15);
						break;

						case erG_FC:
						VIEWTECH_71(25,170,15,388,30,170,15);
						break;

//						case yiG_FC:
//						VIEWTECH_71(26,526,116,745,156,526,116);
//						break;

//						case odu2:
//						VIEWTECH_71(53,526,116,745,156,526,116);
//						break;

//						case otu1:
//						VIEWTECH_71(54,526,116,745,156,526,116);
//						break;	

//						case otu1e:
//						VIEWTECH_71(55,526,116,745,156,526,116);
//						break;	

//						case otu1f:
//						VIEWTECH_71(56,526,116,745,156,526,116);
//						break;		

						case otu2:
						VIEWTECH_71(57,170,15,388,30,170,15);
						break;	

						case otu2e:
						VIEWTECH_71(26,170,15,388,30,170,15);
						break;

						case otu2f:
						VIEWTECH_71(22,170,15,388,30,170,15);
						break;

//						case otu3:
//						VIEWTECH_71(60,526,116,745,156,526,116);
//						break;

//						case otu3e2:
//						VIEWTECH_71(61,526,116,745,156,526,116);
//						break;	

						case shiGinfi:
						VIEWTECH_71(17,170,15,388,30,170,15);
						break;

//						case cpri8:
//						VIEWTECH_71(63,526,116,745,156,526,116);
//						break;	

//						case cpri7:
//						VIEWTECH_71(64,526,116,745,156,526,116);
//						break;	

//						case cpri6:
//						VIEWTECH_71(65,526,116,745,156,526,116);
//						break;	

//						case cpri5:
//						VIEWTECH_71(66,526,116,745,156,526,116);
//						break;	

//						case cpri4:
//						VIEWTECH_71(67,526,116,745,156,526,116);
//						break;	

//						case cpri3:
//						VIEWTECH_71(68,526,116,745,156,526,116);
//						break;	

//						case cpri2:
//						VIEWTECH_71(69,526,116,745,156,526,116);
//						break;	

//						case cpri1:
//						VIEWTECH_71(70,526,116,745,156,526,116);
//						break;	
						
						case shierdianwuG:
						VIEWTECH_71(53,170,15,388,30,170,15);
						break;
					  }				

	
}
 /*******************************************************************************
* Function Name  : User_defined_pattern
* Description    : 自定义编码的正确显示程序，A01只能显示十进制整数，所以先把二进制变成10进制，再显示出来
* Input          : None
* Output         : None
* Return         : None
* Attention		 : None
*******************************************************************************/
void User_defined_pattern(void)
{
	uint32_t udp0_lcd = 0;
  uint32_t udp8_lcd = 0;
  uint32_t udp16_lcd = 0;
  uint32_t udp24_lcd = 0;
	uint32_t udp32_lcd = 0;
	uint32_t udp40_lcd = 0;
  uint32_t udp48_lcd = 0;
  uint32_t udp56_lcd = 0;
	
  udp0_lcd = ((udp[curchan][0]&0x01)*1+((udp[curchan][0]>>1)&0x01)*10+((udp[curchan][0]>>2)&0x01)*100+((udp[curchan][0]>>3)&0x01)*1000
					   +((udp[curchan][0]>>4)&0x01)*10000+((udp[curchan][0]>>5)&0x01)*100000+((udp[curchan][0]>>6)&0x01)*1000000+((udp[curchan][0]>>7)&0x01)*10000000);
	VIEWTECH_A01 (76,66,0x80e1,0x07E0,0xffff,udp0_lcd);	 	
					
	udp8_lcd = ((udp[curchan][1]&0x01)*1+((udp[curchan][1]>>1)&0x01)*10+((udp[curchan][1]>>2)&0x01)*100+((udp[curchan][1]>>3)&0x01)*1000
		         +((udp[curchan][1]>>4)&0x01)*10000+((udp[curchan][1]>>5)&0x01)*100000+((udp[curchan][1]>>6)&0x01)*1000000+((udp[curchan][1]>>7)&0x01)*10000000);			
	VIEWTECH_A01 (140,66,0x80e1,0x07E0,0xffff,udp8_lcd);	 					
					
  udp16_lcd = ((udp[curchan][2]&0x01)*1+((udp[curchan][2]>>1)&0x01)*10+((udp[curchan][2]>>2)&0x01)*100+((udp[curchan][2]>>3)&0x01)*1000
					   +((udp[curchan][2]>>4)&0x01)*10000+((udp[curchan][2]>>5)&0x01)*100000+((udp[curchan][2]>>6)&0x01)*1000000+((udp[curchan][2]>>7)&0x01)*10000000);					
  VIEWTECH_A01 (76,108,0x80e1,0x07E0,0xffff,udp16_lcd);	 					
					
	udp24_lcd = ((udp[curchan][3]&0x01)*1+((udp[curchan][3]>>1)&0x01)*10+((udp[curchan][3]>>2)&0x01)*100+((udp[curchan][3]>>3)&0x01)*1000
					    +((udp[curchan][3]>>4)&0x01)*10000+((udp[curchan][3]>>5)&0x01)*100000+((udp[curchan][3]>>6)&0x01)*1000000+((udp[curchan][3]>>7)&0x01)*10000000);					
  VIEWTECH_A01 (140,108,0x80e1,0x07E0,0xffff,udp24_lcd);	 	

  udp32_lcd = ((udp[curchan][4]&0x01)*1+((udp[curchan][4]>>1)&0x01)*10+((udp[curchan][4]>>2)&0x01)*100+((udp[curchan][4]>>3)&0x01)*1000
					    +((udp[curchan][4]>>4)&0x01)*10000+((udp[curchan][4]>>5)&0x01)*100000+((udp[curchan][4]>>6)&0x01)*1000000+((udp[curchan][4]>>7)&0x01)*10000000);
  VIEWTECH_A01 (76,150,0x80e1,0x07E0,0xffff,udp32_lcd);	 	
					
  udp40_lcd = ((udp[curchan][5]&0x01)*1+((udp[curchan][5]>>1)&0x01)*10+((udp[curchan][5]>>2)&0x01)*100+((udp[curchan][5]>>3)&0x01)*1000
              +((udp[curchan][5]>>4)&0x01)*10000+((udp[curchan][5]>>5)&0x01)*100000+((udp[curchan][5]>>6)&0x01)*1000000+((udp[curchan][5]>>7)&0x01)*10000000);			
	VIEWTECH_A01 (140,150,0x80e1,0x07E0,0xffff,udp40_lcd);	 					
					
	udp48_lcd = ((udp[curchan][6]&0x01)*1+((udp[curchan][6]>>1)&0x01)*10+((udp[curchan][6]>>2)&0x01)*100+((udp[curchan][6]>>3)&0x01)*1000
					    +((udp[curchan][6]>>4)&0x01)*10000+((udp[curchan][6]>>5)&0x01)*100000+((udp[curchan][6]>>6)&0x01)*1000000+((udp[curchan][6]>>7)&0x01)*10000000);					
	VIEWTECH_A01 (76,192,0x80e1,0x07E0,0xffff,udp48_lcd);	 					
					
	udp56_lcd = ((udp[curchan][7]&0x01)*1+((udp[curchan][7]>>1)&0x01)*10+((udp[curchan][7]>>2)&0x01)*100+((udp[curchan][7]>>3)&0x01)*1000
					   +((udp[curchan][7]>>4)&0x01)*10000+((udp[curchan][7]>>5)&0x01)*100000+((udp[curchan][7]>>6)&0x01)*1000000+((udp[curchan][7]>>7)&0x01)*10000000);					
	VIEWTECH_A01 (140,192,0x80e1,0x07E0,0xffff,udp56_lcd);	 					
}


void Show_id(void)
{
					VIEWTECH_70(33);
			VIEWTECH_98 (81,94,0x21,0x91,1,0x000a,0xa6de,"机器型号:",10);		 /*机器型号*/
			VIEWTECH_98 (81,119,0x21,0x91,1,0x000a,0xa6de,"软件版本:",10);		 
			VIEWTECH_98 (81,145,0x21,0x91,1,0x000a,0xa6de,"硬件版本:",10);		
			VIEWTECH_98 (81,170,0x21,0x91,1,0x000a,0xa6de,"序列编号:",10);		 
			    VIEWTECH_98 (151,94,0x21,0x90,1,0x000a,0xa6de, " BERTWave E415B",16);		 /*机器型号*/
			    VIEWTECH_98 (151,119,0x21,0x90,1,0x000a,0xa6de," 1.52",8);		 
			    VIEWTECH_98 (151,145,0x21,0x90,1,0x000a,0xa6de," 1.1",8);		
			    VIEWTECH_98 (151,170,0x21,0x90,1,0x000a,0xa6de," 415",5);	
          VIEWTECH_A01(183,170,0x7021,0x000a,0xa6de,(long int)(id - 4150000000u));	   /* 测试计数 */
}

/**
 * @brief 显示网络状态在LCD顶部
 */
void show_network_status(void)
{
    NetConfig_t config;
    NetConfigError_t result;
    char ip_str[16];
    char status_str[20];
    wiz_NetInfo netinfo;

    // 从Flash加载网络配置
    result = load_network_config(&config);

    if (result == NET_CONFIG_OK) {
        // 格式化IP地址字符串
        sprintf(ip_str, "%d.%d.%d.%d", config.ip[0], config.ip[1], config.ip[2], config.ip[3]);

        // 获取W5500当前状态
        wizchip_getnetinfo(&netinfo);

        // 检查网络连接状态 - 更严格的测试
        uint8_t link_status = test_network_connectivity();
        if (link_status == 1 && memcmp(netinfo.ip, config.ip, 4) == 0) {
            sprintf(status_str, "已连接");
        } else if (memcmp(netinfo.ip, config.ip, 4) == 0) {
            sprintf(status_str, "配置中");
        } else {
            sprintf(status_str, "未连接");
        }

        // 在LCD顶部显示网络状态 (坐标: 400-600, 0-25)
        // 显示"网络:"标签
        VIEWTECH_98(400, 5, 0x21, 0x90, 1, 0x07E0, 0x0000, "网络:", 6);

        // 显示IP地址
        VIEWTECH_98(340, 5, 0x21, 0x90, 1, 0xFFFF, 0x0000, ip_str, strlen(ip_str));

        // 显示连接状态
        if (link_status == 1 && memcmp(netinfo.ip, config.ip, 4) == 0) {
            // 绿色显示"已连接"
            VIEWTECH_98(440, 5, 0x21, 0x90, 1, 0x07E0, 0x0000, status_str, strlen(status_str));
        } else if (memcmp(netinfo.ip, config.ip, 4) == 0) {
            // 黄色显示"配置中"
            VIEWTECH_98(440, 5, 0x21, 0x90, 1, 0xFFE0, 0x0000, status_str, strlen(status_str));
        } else {
            // 红色显示"未连接"
            VIEWTECH_98(440, 5, 0x21, 0x90, 1, 0xF800, 0x0000, status_str, strlen(status_str));
        }

    } else {
        // 配置加载失败，显示详细错误状态
        char error_msg[32];
        switch(result) {
            case NET_CONFIG_ERR_STORAGE_CRC:
                sprintf(error_msg, "网络:CRC错误");
                // 自动尝试修复CRC错误
                fix_crc_error();
                break;
            case NET_CONFIG_ERR_STORAGE_READ:
                sprintf(error_msg, "网络:读取失败");
                break;
            case NET_CONFIG_ERR_STORAGE_WRITE:
                sprintf(error_msg, "网络:写入失败");
                break;
            case NET_CONFIG_ERR_INVALID_PARAM:
                sprintf(error_msg, "网络:参数无效");
                break;
            case NET_CONFIG_ERR_APPLY_FAILED:
                sprintf(error_msg, "网络:应用失败");
                break;
            default:
                sprintf(error_msg, "网络:错误%d", result);
                break;
        }
        VIEWTECH_98(380, 5, 0x21, 0x90, 1, 0xF800, 0x0000, error_msg, strlen(error_msg));
    }
}

/**
 * @brief 显示详细网络配置信息
 */
void show_detailed_network_info(void)
{
    NetConfig_t config;
    NetConfigError_t result;
    char info_str[32];

    // 从Flash加载网络配置
    result = load_network_config(&config);

    if (result == NET_CONFIG_OK) {
        // 显示IP地址
        sprintf(info_str, "IP: %d.%d.%d.%d", config.ip[0], config.ip[1], config.ip[2], config.ip[3]);
        VIEWTECH_98(50, 30, 0x21, 0x90, 1, 0x07E0, 0x0000, info_str, strlen(info_str));

        // 显示子网掩码
        sprintf(info_str, "掩码: %d.%d.%d.%d", config.subnet[0], config.subnet[1], config.subnet[2], config.subnet[3]);
        VIEWTECH_98(50, 50, 0x21, 0x90, 1, 0x07E0, 0x0000, info_str, strlen(info_str));

        // 显示网关
        sprintf(info_str, "网关: %d.%d.%d.%d", config.gateway[0], config.gateway[1], config.gateway[2], config.gateway[3]);
        VIEWTECH_98(50, 70, 0x21, 0x90, 1, 0x07E0, 0x0000, info_str, strlen(info_str));

        // 显示DNS
        sprintf(info_str, "DNS: %d.%d.%d.%d", config.dns[0], config.dns[1], config.dns[2], config.dns[3]);
        VIEWTECH_98(50, 90, 0x21, 0x90, 1, 0x07E0, 0x0000, info_str, strlen(info_str));

        // 显示MAC地址
        sprintf(info_str, "MAC: %02X:%02X:%02X:%02X:%02X:%02X",
                config.mac[0], config.mac[1], config.mac[2],
                config.mac[3], config.mac[4], config.mac[5]);
        VIEWTECH_98(50, 110, 0x21, 0x90, 1, 0x07E0, 0x0000, info_str, strlen(info_str));

        // 显示DHCP模式
        if (config.dhcp_mode) {
            VIEWTECH_98(50, 130, 0x21, 0x90, 1, 0x07E0, 0x0000, "模式: DHCP", 10);
        } else {
            VIEWTECH_98(50, 130, 0x21, 0x90, 1, 0x07E0, 0x0000, "模式: 静态IP", 12);
        }
    } else {
        VIEWTECH_98(50, 30, 0x21, 0x90, 1, 0xF800, 0x0000, "网络配置加载失败", 16);
    }
}

/**
 * @brief 修复CRC错误
 */
void fix_crc_error(void)
{
    NetConfigError_t result;

    // 显示CRC修复信息
    VIEWTECH_98(50, 180, 0x21, 0x90, 1, 0xFFFF, 0x0000, "检测到CRC错误", 12);
    VIEWTECH_98(50, 200, 0x21, 0x90, 1, 0xFFFF, 0x0000, "正在清除损坏数据...", 18);

    // 强制清除Flash配置区域
    result = force_clear_flash_config();

    if (result == NET_CONFIG_OK) {
        VIEWTECH_98(50, 220, 0x21, 0x90, 1, 0x07E0, 0x0000, "Flash清除成功", 12);

        // 延时后重新创建默认配置
        Delay_Ms(1000);
        fix_network_config_error();
    } else {
        VIEWTECH_98(50, 220, 0x21, 0x90, 1, 0xF800, 0x0000, "Flash清除失败", 12);
    }
}

/**
 * @brief 修复网络配置错误
 */
void fix_network_config_error(void)
{
    NetConfig_t default_config;
    NetConfigError_t result;

    // 显示修复信息
    VIEWTECH_98(50, 200, 0x21, 0x90, 1, 0xFFFF, 0x0000, "正在修复网络配置...", 18);

    // 创建默认配置
    uint8_t default_ip[] = {192, 168, 1, 100};
    uint8_t default_mask[] = {255, 255, 255, 0};
    uint8_t default_gw[] = {192, 168, 1, 1};
    uint8_t default_dns[] = {8, 8, 8, 8};
    uint8_t default_mac[] = {0x00, 0x08, 0xDC, 0x01, 0x02, 0x03};

    memcpy(default_config.ip, default_ip, 4);
    memcpy(default_config.subnet, default_mask, 4);
    memcpy(default_config.gateway, default_gw, 4);
    memcpy(default_config.dns, default_dns, 4);
    memcpy(default_config.mac, default_mac, 6);
    default_config.dhcp_mode = 0; // 静态IP
    memset(default_config.reserved, 0, sizeof(default_config.reserved));
    default_config.checksum = 0;

    // 保存默认配置
    result = save_network_config(&default_config);

    if (result == NET_CONFIG_OK) {
        VIEWTECH_98(50, 220, 0x21, 0x90, 1, 0x07E0, 0x0000, "默认配置已创建", 14);

        // 应用配置到W5500
        //result = apply_network_config();
        if (result == NET_CONFIG_OK) {
            VIEWTECH_98(50, 240, 0x21, 0x90, 1, 0x07E0, 0x0000, "配置应用成功", 12);
        } else {
            VIEWTECH_98(50, 240, 0x21, 0x90, 1, 0xF800, 0x0000, "配置应用失败", 12);
        }
    } else {
        VIEWTECH_98(50, 220, 0x21, 0x90, 1, 0xF800, 0x0000, "配置保存失败", 12);
    }

    Delay_Ms(3000);
}

/**
 * @brief 测试网络连通性
 * @return uint8_t 1=连通, 0=不通
 */
uint8_t test_network_connectivity(void)
{
    wiz_NetInfo netinfo;
    uint8_t socket_num = 7; // 使用socket 7进行测试
    uint16_t test_port = 8080;
    uint8_t gateway_ip[4];

    // 获取当前网络配置
    wizchip_getnetinfo(&netinfo);

    // 检查基本配置
    if (netinfo.ip[0] == 0 && netinfo.ip[1] == 0 &&
        netinfo.ip[2] == 0 && netinfo.ip[3] == 0) {
        return 0; // IP地址为0.0.0.0
    }

    // 复制网关地址
    memcpy(gateway_ip, netinfo.gw, 4);

    // 检查网关地址是否有效
    if (gateway_ip[0] == 0 && gateway_ip[1] == 0 &&
        gateway_ip[2] == 0 && gateway_ip[3] == 0) {
        return 0; // 网关地址无效
    }

    // 尝试创建UDP socket进行连通性测试
    uint8_t socket_status = socket(socket_num, Sn_MR_UDP, test_port, 0);

    if (socket_status != socket_num) {
        return 0; // Socket创建失败
    }

    // 检查socket状态
    uint8_t sock_status = getSn_SR(socket_num);
    if (sock_status != SOCK_UDP) {
        close(socket_num);
        return 0; // Socket状态异常
    }

    // 关闭测试socket
    //close(socket_num);

    // 检查PHY链路状态（如果W5500支持）
    uint8_t phy_status = wizphy_getphylink();
    if (phy_status == PHY_LINK_OFF) {
        return 0; // 物理链路断开
    }

    return 1; // 网络连通
}

/**
 * @brief 详细的网络诊断测试
 * @return uint8_t 诊断结果码
 */
uint8_t detailed_network_diagnosis(void)
{
    wiz_NetInfo netinfo;
    uint8_t result = 0;

    // 获取网络配置
    wizchip_getnetinfo(&netinfo);

    // 测试1: IP地址配置
    if (!(netinfo.ip[0] == 0 && netinfo.ip[1] == 0 &&
          netinfo.ip[2] == 0 && netinfo.ip[3] == 0)) {
        result |= 0x01; // IP配置正常
    }

    // 测试2: 子网掩码配置
    if (!(netinfo.sn[0] == 0 && netinfo.sn[1] == 0 &&
          netinfo.sn[2] == 0 && netinfo.sn[3] == 0)) {
        result |= 0x02; // 子网掩码配置正常
    }

    // 测试3: 网关配置
    if (!(netinfo.gw[0] == 0 && netinfo.gw[1] == 0 &&
          netinfo.gw[2] == 0 && netinfo.gw[3] == 0)) {
        result |= 0x04; // 网关配置正常
    }

    // 测试4: PHY链路状态
    if (wizphy_getphylink() == PHY_LINK_ON) {
        result |= 0x08; // 物理链路正常
    }

    // 测试5: Socket功能
    uint8_t test_socket = socket(7, Sn_MR_UDP, 8080, 0);
    if (test_socket == 7) {
        if (getSn_SR(7) == SOCK_UDP) {
            result |= 0x10; // Socket功能正常
        }
        close(7);
    }

    return result;
}

/**
 * @brief 显示网络诊断信息
 */
void show_network_diagnosis(void)
{
    uint8_t diagnosis = detailed_network_diagnosis();
    wiz_NetInfo netinfo;
    char info_str[64];

    // 获取当前网络信息
    wizchip_getnetinfo(&netinfo);

    // 清除显示区域
    VIEWTECH_98(50, 30, 0x21, 0x90, 1, 0x0000, 0x0000, "                                ", 32);

    // 显示标题
    VIEWTECH_98(50, 30, 0x21, 0x90, 1, 0xFFFF, 0x0000, "=== 网络诊断信息 ===", 20);

    // 显示当前IP配置
    sprintf(info_str, "当前IP: %d.%d.%d.%d", netinfo.ip[0], netinfo.ip[1], netinfo.ip[2], netinfo.ip[3]);
    VIEWTECH_98(50, 50, 0x21, 0x90, 1, 0xFFFF, 0x0000, info_str, strlen(info_str));

    sprintf(info_str, "子网掩码: %d.%d.%d.%d", netinfo.sn[0], netinfo.sn[1], netinfo.sn[2], netinfo.sn[3]);
    VIEWTECH_98(50, 70, 0x21, 0x90, 1, 0xFFFF, 0x0000, info_str, strlen(info_str));

    sprintf(info_str, "网关: %d.%d.%d.%d", netinfo.gw[0], netinfo.gw[1], netinfo.gw[2], netinfo.gw[3]);
    VIEWTECH_98(50, 90, 0x21, 0x90, 1, 0xFFFF, 0x0000, info_str, strlen(info_str));

    // 显示诊断结果
    VIEWTECH_98(50, 120, 0x21, 0x90, 1, 0xFFFF, 0x0000, "诊断结果:", 10);

    // IP配置检查
    if (diagnosis & 0x01) {
        VIEWTECH_98(50, 140, 0x21, 0x90, 1, 0x07E0, 0x0000, "? IP地址配置正常", 16);
    } else {
        VIEWTECH_98(50, 140, 0x21, 0x90, 1, 0xF800, 0x0000, "? IP地址配置异常", 16);
    }

    // 子网掩码检查
    if (diagnosis & 0x02) {
        VIEWTECH_98(50, 160, 0x21, 0x90, 1, 0x07E0, 0x0000, "? 子网掩码正常", 14);
    } else {
        VIEWTECH_98(50, 160, 0x21, 0x90, 1, 0xF800, 0x0000, "? 子网掩码异常", 14);
    }

    // 网关检查
    if (diagnosis & 0x04) {
        VIEWTECH_98(50, 180, 0x21, 0x90, 1, 0x07E0, 0x0000, "? 网关配置正常", 14);
    } else {
        VIEWTECH_98(50, 180, 0x21, 0x90, 1, 0xF800, 0x0000, "? 网关配置异常", 14);
    }

    // 物理链路检查
    if (diagnosis & 0x08) {
        VIEWTECH_98(50, 200, 0x21, 0x90, 1, 0x07E0, 0x0000, "? 物理链路正常", 14);
    } else {
        VIEWTECH_98(50, 200, 0x21, 0x90, 1, 0xF800, 0x0000, "? 物理链路断开", 14);
    }

    // Socket功能检查
    if (diagnosis & 0x10) {
        VIEWTECH_98(50, 220, 0x21, 0x90, 1, 0x07E0, 0x0000, "? Socket功能正常", 16);
    } else {
        VIEWTECH_98(50, 220, 0x21, 0x90, 1, 0xF800, 0x0000, "? Socket功能异常", 16);
    }

    // 显示总体状态
    uint8_t total_score = 0;
    for (int i = 0; i < 5; i++) {
        if (diagnosis & (1 << i)) total_score++;
    }

    sprintf(info_str, "网络健康度: %d/5", total_score);
    if (total_score >= 4) {
        VIEWTECH_98(50, 250, 0x21, 0x90, 1, 0x07E0, 0x0000, info_str, strlen(info_str));
    } else if (total_score >= 2) {
        VIEWTECH_98(50, 250, 0x21, 0x90, 1, 0xFFE0, 0x0000, info_str, strlen(info_str));
    } else {
        VIEWTECH_98(50, 250, 0x21, 0x90, 1, 0xF800, 0x0000, info_str, strlen(info_str));
    }
}

/**
 * @brief 强制重新配置W5500网络
 */
void force_reconfigure_w5500(void)
{
    NetConfig_t config;
    NetConfigError_t result;
    wiz_NetInfo netinfo;

    VIEWTECH_98(50, 30, 0x21, 0x90, 1, 0xFFFF, 0x0000, "正在重新配置W5500...", 20);

    // 从Flash加载配置
    result = load_network_config(&config);
    if (result != NET_CONFIG_OK) {
        VIEWTECH_98(50, 50, 0x21, 0x90, 1, 0xF800, 0x0000, "配置加载失败", 12);
        return;
    }

    // 重置W5500芯片
    VIEWTECH_98(50, 50, 0x21, 0x90, 1, 0xFFFF, 0x0000, "重置W5500芯片...", 16);

    // 这里应该添加W5500硬件重置代码
    // Reset_W5500(); // 如果有硬件重置函数
    Delay_Ms(100);

    // 重新初始化W5500
    VIEWTECH_98(50, 70, 0x21, 0x90, 1, 0xFFFF, 0x0000, "重新初始化...", 14);

    // 准备网络配置
    memcpy(netinfo.mac, config.mac, 6);
    memcpy(netinfo.ip, config.ip, 4);
    memcpy(netinfo.sn, config.subnet, 4);
    memcpy(netinfo.gw, config.gateway, 4);
    memcpy(netinfo.dns, config.dns, 4);
    netinfo.dhcp = config.dhcp_mode ? NETINFO_DHCP : NETINFO_STATIC;

    // 应用配置到W5500
    VIEWTECH_98(50, 90, 0x21, 0x90, 1, 0xFFFF, 0x0000, "应用网络配置...", 16);
    wizchip_setnetinfo(&netinfo);

    // 等待配置生效
    Delay_Ms(1000);

    // 验证配置
    wiz_NetInfo read_netinfo;
    wizchip_getnetinfo(&read_netinfo);

    if (memcmp(read_netinfo.ip, config.ip, 4) == 0) {
        VIEWTECH_98(50, 110, 0x21, 0x90, 1, 0x07E0, 0x0000, "配置应用成功", 12);

        // 测试连通性
        uint8_t connectivity = test_network_connectivity();
        if (connectivity) {
            VIEWTECH_98(50, 130, 0x21, 0x90, 1, 0x07E0, 0x0000, "网络连通性正常", 14);
        } else {
            VIEWTECH_98(50, 130, 0x21, 0x90, 1, 0xFFE0, 0x0000, "网络连通性待确认", 16);
        }
    } else {
        VIEWTECH_98(50, 110, 0x21, 0x90, 1, 0xF800, 0x0000, "配置应用失败", 12);
    }

    Delay_Ms(3000);
}

/**
 * @brief 初始化网络配置（如果不存在）
 */
void init_network_config_if_needed(void)
{
    NetConfig_t config;
    NetConfigError_t result;

    // 尝试加载现有配置
    result = load_network_config(&config);

    if (result != NET_CONFIG_OK) {
        // 配置不存在或损坏，修复配置
        fix_network_config_error();
    }
}

/**
 * @brief 测试网络状态显示功能
 */
void test_network_display(void)
{
    // 显示测试信息
    VIEWTECH_98(50, 200, 0x21, 0x90, 1, 0x07E0, 0x0000, "网络状态显示测试", 16);

    // 初始化网络配置（如果需要）
    init_network_config_if_needed();

    // 调用网络状态显示函数
    show_network_status();

    // 延时2秒显示详细信息
    Delay_Ms(2000);
    show_detailed_network_info();

    // 延时3秒显示网络诊断
    Delay_Ms(3000);
    show_network_diagnosis();

    // 如果网络有问题，尝试重新配置
    uint8_t connectivity = test_network_connectivity();
    if (!connectivity) {
        Delay_Ms(2000);
        force_reconfigure_w5500();
    }
}


/*********************************************************************************************************
      END FILE
*********************************************************************************************************/
