//************************************************************************************************
//**
//**文  件  名：Uart.c
//**
//**说      明：串口驱动。
//**
//**作      者：WBH
//**
//**时      间：2015年04月21日
//**
//************************************************************************************************
//驱动程序头文件
#include "Drivers.h"
#include <stdarg.h>
//#include <stdio.h>
//#include <string.h>

USART_InitTypeDef USART_InitStructure;    //串口初始化结构体声明

//------------------------------------------------------------------------------------------------
//定义串口结构体

#if UART1EN
UARTTypeDef UART1;
#endif

#if UART2EN
UARTTypeDef UART2;
#endif

#if UART3EN
UARTTypeDef UART3;
#endif

/*****************************串口1的条件编译***************************************************/
#if UART1EN 
//************************************************************************************************
//**名      称：UART1_Init
//**说      明：串口1初始化
//**形      参：无
//**返  回  值：无
//************************************************************************************************
void UART1_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    UART1.ReceiveBytes=0;
    UART1.SendBytes=0;
    UART1.RecTimeOutFlag=0;
    UART1.ComInterruptFlag=0;
    UART1.TimeCounter=0;
    UART1.pRecBuff=(u8 *)UART1.RecBuff;     //将发送数据指针指向发送缓冲区头
    UART1.pSendBuff=(u8 *)UART1.SendBuff;   //将接收数据指针指向接收缓存区头
    
    
	GPIO_PinAFConfig(USART1_GPIO, USART1_TX_SOURCE, USART1_GPIO_AF);
	GPIO_PinAFConfig(USART1_GPIO, USART1_RX_SOURCE, USART1_GPIO_AF);
	/* Configure USART Tx and Rx as alternate function push-pull */
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;

    GPIO_InitStructure.GPIO_Pin = USART1_TxPin;
    GPIO_Init(USART1_GPIO, &GPIO_InitStructure);
	
	GPIO_InitStructure.GPIO_Pin = USART1_RxPin;
    GPIO_Init(USART1_GPIO, &GPIO_InitStructure);

    /* USARTx configured as follow:
        - BaudRate = 9600 baud  
        - Word Length = 8 Bits
        - One Stop Bit
        - No parity
        - Hardware flow control disabled (RTS and CTS signals)
        - Receive and transmit enabled
    */
    USART_InitStructure.USART_BaudRate = BAUDRATE1;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;

    USART_Init(USART1, &USART_InitStructure);
    /*使能串口1的发送和接收中断*/
    USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);
    //USART_ITConfig(USART1, USART_IT_TC, ENABLE);
    /* 使能串口1 */
    USART_Cmd(USART1, ENABLE);

    USART_ClearFlag(USART1, USART_FLAG_TC);
}

//************************************************************************************************
//**名      称：UART1_TestRecTimeOut
//**说      明：检测串口1接收超时
//**形      参：无
//**返  回  值：无
//************************************************************************************************
void UART1_TestRecTimeOut(void)
{
    if( UART1.TimeCounter > 0 )
    {
        if(  --UART1.TimeCounter  == 0 )
        {
            UART1.RecTimeOutFlag = 1 ;
        }
    }
}

//************************************************************************************************
//**名      称：UART1_SendFrame
//**说      明：串口1发送一帧
//**形      参：需要发送字节数
//**返  回  值：无
//************************************************************************************************
void UART1_SendFrame(u16 bytes)
{
    UART1.ReceiveBytes = 0 ;
    UART1.SendBytes=bytes;
    UART1.pSendBuff = (u8 *)(UART1.SendBuff);     //将发送数据指针指向缓冲区头部
    USART_ClearFlag(USART1, USART_FLAG_TC);//
    USART_SendData(USART1, *(UART1.pSendBuff));    
}

//************************************************************************************************
//**名      称：USART_SendWIFIData
//**说      明：调用串口1发送WIFI数据
//**形      参：-USARTx 串口通道，这里只用到了串口1，即USART1
//**            -Data   要发送到串口的内容的指针
//**            -...    其他参数
//**返  回  值：无
//************************************************************************************************
void USART_SendWIFIData(USART_TypeDef* USARTx, char *Data,...)
{
	const char *s;
	int d;   
	char buf[16];

	va_list ap;
	va_start(ap, Data);

	Clear_RecBuff();		// 每次发送前清空接收缓冲区
	while ( *Data != 0)     // 判断是否到达字符串结束符
	{				                          
		if ( *Data == 0x5c )  //'\'
		{									  
			switch ( *++Data )
			{
				case 'r':							          //回车符
					USART_SendData(USARTx, 0x0d);
					Data ++;
					break;

				case 'n':							          //换行符
					USART_SendData(USARTx, 0x0a);	
					Data ++;
					break;
				
				default:
					Data ++;
				    break;
			}			 
		}
		else if ( *Data == '%')
		{									  
			switch ( *++Data )
			{				
				case 's':										 //字符串
					s = va_arg(ap, const char *);
					for ( ; *s; s++) 
					{
						USART_SendData(USARTx,*s);
						while( USART_GetFlagStatus(USARTx, USART_FLAG_TXE) == RESET );
					}
					Data++;
				break;

				case 'd':										//十进制
					d = va_arg(ap, int);
					sprintf(buf, "%d", d);
					for (s = buf; *s; s++) 
					{
						USART_SendData(USARTx,*s);
						while( USART_GetFlagStatus(USARTx, USART_FLAG_TXE) == RESET );
					}
					Data++;
					break;
				default:
					Data++;
					break;
			}		 
		} /* end of else if */
		else USART_SendData(USARTx, *Data++);
		while( USART_GetFlagStatus(USARTx, USART_FLAG_TXE) == RESET );
	}
}

//************************************************************************************************
//**名      称：Get_RecBuff
//**说      明：获取串口1接收到的数据
//**形      参：len: 获取数据的长度
//**返  回  值：(char *)&UART.RecBuff: 接收到的数据内容
//************************************************************************************************
char *Get_RecBuff(uint8_t *len)
{
    *len = UART1.ReceiveBytes;
    return (char *)&UART1.RecBuff;
}

//************************************************************************************************
//**名      称：Clear_RecBuff
//**说      明：清空串口1接收数据缓存
//**形      参：无
//**返  回  值：无
//************************************************************************************************
void Clear_RecBuff(void)
{
	UART1.ReceiveBytes = 0;
	memset(UART1.RecBuff,0x00,sizeof(UART1.RecBuff));
}

#endif //串口1的条件编译结束


/*****************************串口2的条件编译***************************************************/
#if UART2EN
 //************************************************************************************************
//**名      称：UART2_Init
//**说      明：串口2初始化
//**形      参：无
//**返  回  值：无
//************************************************************************************************
void UART2_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    UART2.ReceiveBytes=0;
    UART2.SendBytes=0;
    UART2.RecTimeOutFlag=0;
    UART2.ComInterruptFlag=0;
    UART2.TimeCounter=0;
    UART2.pRecBuff=(u8 *)UART2.RecBuff;     //将发送数据指针指向发送缓冲区头
    UART2.pSendBuff=(u8 *)UART2.SendBuff;   //将接收数据指针指向接收缓存区头
    

    /* Connect USART pins to AF7 */
	GPIO_PinAFConfig(USART2_GPIO, USART2_TX_SOURCE, USART2_GPIO_AF);
	GPIO_PinAFConfig(USART2_GPIO, USART2_RX_SOURCE, USART2_GPIO_AF);
	/* Configure USART Tx and Rx as alternate function push-pull */
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;

    GPIO_InitStructure.GPIO_Pin = USART2_TxPin;
    GPIO_Init(USART2_GPIO, &GPIO_InitStructure);
	
	GPIO_InitStructure.GPIO_Pin = USART2_RxPin;
    GPIO_Init(USART2_GPIO, &GPIO_InitStructure);

    USART_InitStructure.USART_BaudRate = BAUDRATE2;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;

    USART_Init(USART2, &USART_InitStructure);
    /*使能串口1的发送和接收中断*/
    USART_ITConfig(USART2, USART_IT_RXNE, ENABLE);
    //USART_ITConfig(USART2, USART_IT_TC, ENABLE);
    /* 使能串口1 */
    USART_Cmd(USART2, ENABLE);

    USART_ClearFlag(USART2, USART_FLAG_TC);
}

//************************************************************************************************
//**名      称：UART1_TestRecTimeOut
//**说      明：检测串口1接收超时
//**形      参：无
//**返  回  值：无
//************************************************************************************************
void UART2_TestRecTimeOut(void)
{
    if( UART2.TimeCounter > 0 )
    {
        if(  --UART2.TimeCounter  == 0 )
        {
            UART2.RecTimeOutFlag = 1 ;
        }
    }
}

//************************************************************************************************
//**名      称：UART2_SendFrame
//**说      明：串口2发送一帧
//**形      参：需要发送字节数
//**返  回  值：无
//************************************************************************************************
void UART2_SendFrame(u16 bytes)
{
    UART2.ReceiveBytes = 0 ;
    UART2.SendBytes=bytes;
    UART2.pSendBuff = (u8 *)(UART2.SendBuff);     //将发送数据指针指向缓冲区头部
    USART_ClearFlag(USART2, USART_FLAG_TC);//
    USART_SendData(USART2, *(UART2.pSendBuff));    
}

#endif //串口2的条件编译结束


/*****************************串口3的条件编译***************************************************/
#if UART3EN
 //************************************************************************************************
//**名      称：UART2_Init
//**说      明：串口2初始化
//**形      参：无
//**返  回  值：无
//************************************************************************************************
void UART3_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    UART3.ReceiveBytes=0;
    UART3.SendBytes=0;
    UART3.RecTimeOutFlag=0;
    UART3.ComInterruptFlag=0;
    UART3.TimeCounter=0;
    UART3.pRecBuff=(u8 *)UART3.RecBuff;     //将发送数据指针指向发送缓冲区头
    UART3.pSendBuff=(u8 *)UART3.SendBuff;   //将接收数据指针指向接收缓存区头
    

    /* Connect USART pins to AF7 */
	GPIO_PinAFConfig(USART3_GPIO, USART3_TX_SOURCE, USART3_GPIO_AF);
	GPIO_PinAFConfig(USART3_GPIO, USART3_RX_SOURCE, USART3_GPIO_AF);
	/* Configure USART Tx and Rx as alternate function push-pull */
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;

    GPIO_InitStructure.GPIO_Pin = USART3_TxPin;
    GPIO_Init(USART3_GPIO, &GPIO_InitStructure);
	
	GPIO_InitStructure.GPIO_Pin = USART3_RxPin;
    GPIO_Init(USART3_GPIO, &GPIO_InitStructure);

    USART_InitStructure.USART_BaudRate = BAUDRATE3;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;

    USART_Init(USART3, &USART_InitStructure);
    /*使能串口1的发送和接收中断*/
    USART_ITConfig(USART3, USART_IT_RXNE, ENABLE);
    //USART_ITConfig(USART2, USART_IT_TC, ENABLE);
    /* 使能串口1 */
    USART_Cmd(USART3, ENABLE);

    USART_ClearFlag(USART3, USART_FLAG_TC);
}

//************************************************************************************************
//**名      称：UART1_TestRecTimeOut
//**说      明：检测串口1接收超时
//**形      参：无
//**返  回  值：无
//************************************************************************************************
void UART3_TestRecTimeOut(void)
{
    if( UART3.TimeCounter > 0 )
    {
        if(  --UART3.TimeCounter  == 0 )
        {
            UART3.RecTimeOutFlag = 1 ;
        }
    }
}

//************************************************************************************************
//**名      称：UART2_SendFrame
//**说      明：串口2发送一帧
//**形      参：需要发送字节数
//**返  回  值：无
//************************************************************************************************
void UART3_SendFrame(u16 bytes)
{
    UART3.ReceiveBytes = 0 ;
    UART3.SendBytes=bytes;
    UART3.pSendBuff = (u8 *)(UART3.SendBuff);     //将发送数据指针指向缓冲区头部
    USART_ClearFlag(USART3, USART_FLAG_TC);//
    USART_SendData(USART3, *(UART3.pSendBuff));    
}

#endif //串口2的条件编译结束


