/************************************************************************************************
 *
 *工程名称：100G误码仪
 *
 *说	明：基于LPCDR方案
 *
 *版	本：V1.0
 *
 *作	者：WG
 *
 *创建时间：2017年8月8日
 *
 *修改时间：2017年8月18日
 *
 s************************************************************************************************/
#include "stm32f4xx.h"
#include "Application.h"
#include "Drivers.h"
#include "System.h"
#include "CS4343_operation.h"
#include "cs4224_api.h"
#include "udp.h"
#include "socket.h"
#include "serial_net_config.h"

#ifdef __GNUC__
  /* With GCC/RAISONANCE, small printf (option LD Linker->Libraries->Small printf
     set to 'Yes') calls __io_putchar() */
  #define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
  #define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif /* __GNUC__ */



int main(void)
{
//	u8 reg_data1,reg_data2,reg_data3,reg_data4;
//	u16 reg_data01,reg_data02,reg_data03,reg_data04;

	//看门狗初始化
//	WTD_Init();      
	//各模块时钟初始化
	
	RCC_Init();
	//滴答时钟初始化
	SysTick_Init();
	//精确延时初始化
	Delay_Init();
	//中断初始化
	NVIC_Configuration();
	//各外设引脚初始化
//	GPIO_Configuration();
	//延时保证AVCC_ADJ初始化完成
	Delay_Ms(10);
	//SPI_MasterInit();
	UART1_Init();						   /* 配置上位机串口 */
	//串口2初始化与屏通讯
	UART2_Init();  
	UART3_Init();
//	//TIM3初始化
	TIM3_Init();
	id_read(); 
//	/*初始化SDRAM模块*/
//	SDRAM_Init();
	POWER_Init();
	SiCLK_Out_Init();	
	
	Si5340_Address_Init();	
	Si5340_Out_Init();
	
	IIC_Init();
	
	IICDIV_Init();
//	IICCDR_Init();
	MDIO_Init();
 //SPI_MasterInit();
//	Delay_Ms(5000);
	LCD_Init();
	
	 Freq_Set(312.5);
	 Si5340_Config(siconfig[1][0],siconfig[1][1],siconfig[1][2],siconfig[1][3],siconfig[1][4],siconfig[1][5],siconfig[1][6],siconfig[1][7],siconfig[1][8],siconfig[1][9],siconfig[1][10],siconfig[1][11],siconfig[1][12],siconfig[1][13],siconfig[1][14],siconfig[1][15]);
		//	 Delay_Ms(2000);
	 //上电时序  0.9v先上电  1.8v后上电  cs4343才正常工作
	 VCC0d9_PWR1_Ctrl_ON;
	 VCC0d9_PWR2_Ctrl_ON;
	 Delay_Ms(500);
	 VCC1d8_PWR1_Ctrl_ON;
	 VCC1d8_PWR2_Ctrl_ON;
	
	Delay_Us(5000);
	CS4343RESET_Ctrl1_OFF;
	CS4343RESET_Ctrl2_OFF;
	Delay_Us(1000);
	CS4343RESET_Ctrl1_ON;
	CS4343RESET_Ctrl2_ON;

	MDC_Period(250);  //reset后500个mdc周期才能正常接收指令
	MDC_Period(250);
	MDC_Period(250);	
	Delay_Ms(100);
	
	upd_connect();//
	
	
//	 Delay_Us(5000);	
//		//test cs4343 mdio
//	if(CS_OK!=cs4343_test_mdio())
//		VIEWTECH_A01 (600,380,0x6022,0xf800,0x39c8,000);
//	else
//	{		
//		VIEWTECH_A01 (600,380,0x6022,0xf800,0x39c8,111);
//	}

	
//	  Delay_Us(5000);	
//		GPIO_ResetBits(GPIOH,GPIO_Pin_13);//reset CDR2
//		GPIO_ResetBits(GPIOD,GPIO_Pin_1);
//		GPIO_ResetBits(GPIOG,GPIO_Pin_10);
//		GPIO_ResetBits(GPIOE,GPIO_Pin_6);
//		Delay_Us(5000);
//		GPIO_SetBits(GPIOH,GPIO_Pin_13);
//		GPIO_SetBits(GPIOD,GPIO_Pin_1);
//		GPIO_SetBits(GPIOG,GPIO_Pin_10);
//		GPIO_SetBits(GPIOE,GPIO_Pin_6);
//		Delay_Us(5000);
//		
//		LMX2592_Init_Config();	
		
	
   while(1)
  {
		
		if(biaoji == Yes_touch && USB_biaoji == No_Order)
	 {
 	   XY_touch(XY);		   //触屏程序
	   biaoji = No_touch;
	 }
		else if(USB_biaoji == Up_Config)  //将目前的下位机配置情况上传给上位机
	 {
		 //LCD_nEN;   //关闭显示屏
		 VIEWTECH_E0 (0x07,0x18);  //关闭触摸
		 
 	   USB_01();
		 
		  VIEWTECH_71(25,273,50,359,73,273,50);  //ch0  run  stop
				VIEWTECH_71(11,402,62,468,77,402,62);	                /* 显示暂停状态  */
				START_RUN = First_circle;
	      START_SYNC1 = SYNC1_First;
				RUN_FLAG = noRuning;
		 
		 VIEWTECH_71(25,273,96,359,119,273,96);  //ch1  run  stop				 
				  VIEWTECH_71(11,402,108,468,123,402,108);	                /* 显示暂停状态  */
				START_RUN1 = First1_circle;
	      START_SYNC2 = SYNC2_First;
				RUN_FLAG1 = noRuning1;
		 
		 VIEWTECH_71(25,273,142,359,165,273,142);  //ch2  run  stop				 
				   VIEWTECH_71(11,402,154,468,169,402,154);	                /* 显示暂停状态  */
				START_RUN2 = First2_circle;
	      START_SYNC3 = SYNC3_First;
				RUN_FLAG2 = noRuning2;
		 
		 VIEWTECH_71(25,273,188,359,210,273,188);  //ch1  run  stop				 
				  VIEWTECH_71(11,402,200,468,215,402,200);
				START_RUN3 = First3_circle;
	      START_SYNC4 = SYNC4_First;
				RUN_FLAG3 = noRuning3;
		 
	   USB_biaoji = No_Order;
	 }
	 else if(USB_biaoji == Down_Setup )   //需要确定在配置时 Run_FLAG==0
	 {
 	   USB_10();
		 USB_Reso_10();
	   USB_biaoji = No_Order;
	 }
//	  else if(USB_biaoji == Down_Setup )   //需要确定在配置时 Run_FLAG==0
//	 {
// 	   USB_11();
//		 USB_Reso_11();
//	   USB_biaoji = No_Order;
//	 }
//	 else if(USB_biaoji == Timer_Set )   //需要确定在配置时 Run_FLAG==0
//	 {
// 	   USB_12();
//		 USB_Reso_12();
//	   USB_biaoji = No_Order;
//	 }
//	 else if(USB_biaoji == Div_Set )   //设置速率分频
//	 {
// 	   USB_60();
//		 USB_Reso_60();
//	   USB_biaoji = No_Order;
//	 }
//	 else if(USB_biaoji == userRate_Set )   //设置速率分频
//	 {
// 	   USB_62();
//		 USB_Reso_62();
//	   USB_biaoji = No_Order;
//	 }
//	 else if(USB_biaoji == Up_Div )   //上传速率分频
//	 {
// 	   USB_61();
//	   USB_biaoji = No_Order;
//	 }
	 else if(USB_biaoji == Down_Startrx)  //上位机下传开始、停止检测指令
	 {
 	   USB_00();
		 USB_Reso_00();
	   USB_biaoji = No_Order;
	 }
	 else if(USB_biaoji == Down_Starttx)  //上位机下传开始、停止检测指令
	 {
 	   USB_02();
		 USB_Reso_02();
	   USB_biaoji = No_Order;
	 }
	 else if(USB_biaoji == Up_Ber ) //下位机上传误码数指令
	 {
 	   USB_20();
	   USB_biaoji = No_Order;
	 } 
	 else if(USB_biaoji == Up_iBer ) //下位机上传误码数指令
	 {
 	   USB_40();
	   USB_biaoji = No_Order;
	 } 
	 else if(USB_biaoji == Up_Eye ) //下位机上传eye
	 {
 	   USB_Reso_30();
		 //Eye_Scan();
		 USB_30();
	   USB_biaoji = No_Order;
	 } 
	 else if(USB_biaoji == Touch_On ) //开启触屏
	 {		 
		 USB_03();
		 VIEWTECH_E0 (0x07,0x08);
	   USB_biaoji = No_Order;
	 }

	// 处理串口网络配置命令
	serial_config_task();

	// 检查串口接收超时
	UART1_TestRecTimeOut();

	RUN_SHOW();             //运行误码检测程序
	 	//RUN_Eye();
	}
}


/*******************************************************************/
/**
  * @brief  Retargets the C library printf function to the USART.
  * @param  None
  * @retval None
  */
PUTCHAR_PROTOTYPE
{
  /* Place your implementation of fputc here */
  /* e.g. write a character to the USART */
  USART_SendData(USART1, (uint8_t) ch);

  /* Loop until the end of transmission */
  while (USART_GetFlagStatus(USART1, USART_FLAG_TC) == RESET)
  {}

  return ch;
}


#ifdef  USE_FULL_ASSERT

/**
  * @brief  Reports the name of the source file and the source line number
  *   where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t* file, uint32_t line)
{ 
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */

  /* Infinite loop */
  while (1)
  {
  }
}
#endif

