# LCD网络状态显示功能使用说明

## 概述

本功能在LCD屏幕顶部实时显示网络状态信息，包括IP地址、连接状态等，方便用户直观了解设备的网络配置和连接情况。

## 功能特性

### 1. 实时网络状态显示
- **位置**: LCD屏幕顶部 (坐标: 400-600, 0-25)
- **内容**: 网络标签 + IP地址 + 连接状态
- **更新频率**: 随主界面刷新自动更新

### 2. 状态指示颜色
- **绿色**: 网络已连接，配置正常
- **红色**: 网络未连接或配置错误
- **白色**: IP地址显示

### 3. 详细网络信息显示
- IP地址、子网掩码、网关、DNS服务器
- MAC地址、DHCP模式
- 配置加载状态

## 显示内容说明

### 顶部状态栏显示格式
```
网络: ************* 已连接
```

### 详细信息显示格式
```
IP: *************
掩码: *************
网关: ***********
DNS: *******
MAC: 00:08:DC:01:02:03
模式: 静态IP
```

## 集成位置

### 1. 主界面状态更新
- 函数: `update_status()`
- 位置: `User/Lcd_Interface.c`
- 调用: 每次界面更新时自动显示

### 2. 运行界面显示
- 函数: `RUN_SHOW()`
- 位置: `User/BER_Test.c`
- 调用: 主运行循环中持续显示

## 核心函数说明

### 1. show_network_status()
```c
/**
 * @brief 显示网络状态在LCD顶部
 * @details 显示简要的网络状态信息，包括IP地址和连接状态
 */
void show_network_status(void);
```

**功能**:
- 从Flash加载网络配置
- 获取W5500当前状态
- 在LCD顶部显示网络信息
- 根据连接状态显示不同颜色

**显示位置**:
- 网络标签: (400, 5)
- IP地址: (440, 5)
- 连接状态: (540, 5)

### 2. show_detailed_network_info()
```c
/**
 * @brief 显示详细网络配置信息
 * @details 显示完整的网络配置参数
 */
void show_detailed_network_info(void);
```

**功能**:
- 显示完整的网络配置信息
- 包括IP、掩码、网关、DNS、MAC等
- 显示DHCP模式状态

**显示位置**:
- 从(50, 30)开始，每行间隔20像素

### 3. test_network_display()
```c
/**
 * @brief 测试网络状态显示功能
 * @details 用于测试和验证网络显示功能
 */
void test_network_display(void);
```

## 配置参数

### LCD显示参数
```c
// VIEWTECH_98函数参数说明
// VIEWTECH_98(x, y, font_size, font_type, mode, fg_color, bg_color, text, length)

// 颜色定义
#define COLOR_GREEN   0x07E0    // 绿色 (已连接)
#define COLOR_RED     0xF800    // 红色 (未连接/错误)
#define COLOR_WHITE   0xFFFF    // 白色 (IP地址)
#define COLOR_BLACK   0x0000    // 黑色 (背景)
```

### 显示位置配置
```c
// 顶部状态栏位置
#define NET_LABEL_X     400     // "网络:"标签X坐标
#define NET_IP_X        440     // IP地址X坐标
#define NET_STATUS_X    540     // 状态X坐标
#define NET_TOP_Y       5       // 顶部Y坐标

// 详细信息显示位置
#define NET_DETAIL_X    50      // 详细信息X坐标
#define NET_DETAIL_Y    30      // 详细信息起始Y坐标
#define NET_LINE_HEIGHT 20      // 行间距
```

## 错误处理

### 1. 配置加载失败
- 显示: "网络:配置错误" (红色)
- 原因: Flash配置损坏或未初始化
- 解决: 重新配置网络参数

### 2. W5500通信失败
- 显示: "网络:未连接" (红色)
- 原因: W5500芯片通信异常
- 解决: 检查硬件连接

### 3. 网络参数不匹配
- 显示: IP地址正常，状态显示"未连接"
- 原因: 配置未正确应用到W5500
- 解决: 重新应用网络配置

## 性能优化

### 1. 显示更新频率
- 避免过于频繁的更新
- 仅在网络状态变化时刷新
- 减少Flash读取次数

### 2. 内存使用优化
- 使用局部变量存储临时数据
- 避免全局变量占用
- 字符串缓冲区大小适中

## 调试和测试

### 1. 测试步骤
```c
// 在main函数中调用测试函数
test_network_display();
```

### 2. 调试信息
- 通过串口输出调试信息
- 检查网络配置加载状态
- 验证W5500状态读取

### 3. 常见问题
1. **显示乱码**: 检查字符编码和字体设置
2. **位置偏移**: 调整坐标参数
3. **颜色异常**: 检查颜色值定义
4. **更新不及时**: 检查调用频率

## 扩展功能

### 1. 可添加的显示内容
- 网络流量统计
- 连接时长显示
- 信号强度指示
- 错误计数显示

### 2. 交互功能
- 点击显示详细信息
- 长按进入网络配置
- 状态栏菜单功能

## 注意事项

1. **坐标系统**: 确保显示坐标在LCD有效范围内
2. **字体大小**: 根据LCD分辨率选择合适字体
3. **颜色搭配**: 确保文字与背景对比度足够
4. **更新频率**: 避免过度刷新影响性能
5. **错误处理**: 确保异常情况下不影响主功能

## 版本信息

- **版本**: V1.0
- **日期**: 2024年
- **兼容性**: STM32F429 + LCD + W5500
- **依赖**: VIEWTECH显示库、网络配置模块
