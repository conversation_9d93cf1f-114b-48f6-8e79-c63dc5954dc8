#include "serial_net_config.h"
#include <stdio.h>
#include <string.h>
#include <assert.h>

/* 扩展的网络参数验证测试 */
static int test_count = 0;
static int test_passed = 0;

#define TEST_ASSERT(condition, message) \
    do { \
        test_count++; \
        if (condition) { \
            test_passed++; \
            printf("PASS: %s\n", message); \
        } else { \
            printf("FAIL: %s\n", message); \
        } \
    } while(0)

/* 网络配置场景测试 */
void test_network_scenarios(void)
{
    printf("\n=== 网络配置场景测试 ===\n");
    
    NetConfig_t config;
    
    // 场景1: 典型企业网络配置
    config.ip[0] = 192; config.ip[1] = 168; config.ip[2] = 10; config.ip[3] = 50;
    config.subnet[0] = 255; config.subnet[1] = 255; config.subnet[2] = 255; config.subnet[3] = 0;
    config.gateway[0] = 192; config.gateway[1] = 168; config.gateway[2] = 10; config.gateway[3] = 1;
    config.dns[0] = 192; config.dns[1] = 168; config.dns[2] = 10; config.dns[3] = 1;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_OK, "企业网络配置 ************/24");
    
    // 场景2: 大型网络配置 (/16)
    config.ip[0] = 10; config.ip[1] = 1; config.ip[2] = 1; config.ip[3] = 100;
    config.subnet[0] = 255; config.subnet[1] = 255; config.subnet[2] = 0; config.subnet[3] = 0;
    config.gateway[0] = 10; config.gateway[1] = 1; config.gateway[2] = 0; config.gateway[3] = 1;
    config.dns[0] = 8; config.dns[1] = 8; config.dns[2] = 8; config.dns[3] = 8;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_OK, "大型网络配置 ********/16");
    
    // 场景3: 小型网络配置 (/28)
    config.ip[0] = 192; config.ip[1] = 168; config.ip[2] = 1; config.ip[3] = 10;
    config.subnet[0] = 255; config.subnet[1] = 255; config.subnet[2] = 255; config.subnet[3] = 240;
    config.gateway[0] = 192; config.gateway[1] = 168; config.gateway[2] = 1; config.gateway[3] = 1;
    config.dns[0] = 1; config.dns[1] = 1; config.dns[2] = 1; config.dns[3] = 1;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_OK, "小型网络配置 ***********/28");
    
    // 场景4: 点对点网络 (/30)
    config.ip[0] = 192; config.ip[1] = 168; config.ip[2] = 1; config.ip[3] = 2;
    config.subnet[0] = 255; config.subnet[1] = 255; config.subnet[2] = 255; config.subnet[3] = 252;
    config.gateway[0] = 192; config.gateway[1] = 168; config.gateway[2] = 1; config.gateway[3] = 1;
    config.dns[0] = 8; config.dns[1] = 8; config.dns[2] = 4; config.dns[3] = 4;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_OK, "点对点网络配置 /30");
}

/* 边界条件测试 */
void test_boundary_conditions(void)
{
    printf("\n=== 边界条件测试 ===\n");
    
    NetConfig_t config;
    
    // 测试最小有效子网 (/30)
    config.ip[0] = 192; config.ip[1] = 168; config.ip[2] = 1; config.ip[3] = 2;
    config.subnet[0] = 255; config.subnet[1] = 255; config.subnet[2] = 255; config.subnet[3] = 252;
    config.gateway[0] = 192; config.gateway[1] = 168; config.gateway[2] = 1; config.gateway[3] = 1;
    config.dns[0] = 8; config.dns[1] = 8; config.dns[2] = 8; config.dns[3] = 8;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_OK, "最小子网 /30");
    
    // 测试IP在子网边界 - 第一个可用IP
    config.ip[0] = 192; config.ip[1] = 168; config.ip[2] = 1; config.ip[3] = 1;
    config.subnet[0] = 255; config.subnet[1] = 255; config.subnet[2] = 255; config.subnet[3] = 252;
    config.gateway[0] = 192; config.gateway[1] = 168; config.gateway[2] = 1; config.gateway[3] = 2;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_OK, "第一个可用IP");
    
    // 测试IP在子网边界 - 最后一个可用IP
    config.ip[0] = 192; config.ip[1] = 168; config.ip[2] = 1; config.ip[3] = 2;
    config.subnet[0] = 255; config.subnet[1] = 255; config.subnet[2] = 255; config.subnet[3] = 252;
    config.gateway[0] = 192; config.gateway[1] = 168; config.gateway[2] = 1; config.gateway[3] = 1;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_OK, "最后一个可用IP");
    
    // 测试网关在子网边界
    config.ip[0] = 192; config.ip[1] = 168; config.ip[2] = 1; config.ip[3] = 1;
    config.subnet[0] = 255; config.subnet[1] = 255; config.subnet[2] = 255; config.subnet[3] = 0;
    config.gateway[0] = 192; config.gateway[1] = 168; config.gateway[2] = 1; config.gateway[3] = 254;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_OK, "网关在子网末尾");
}

/* 错误配置测试 */
void test_invalid_configurations(void)
{
    printf("\n=== 错误配置测试 ===\n");
    
    NetConfig_t config;
    
    // 基础有效配置
    config.ip[0] = 192; config.ip[1] = 168; config.ip[2] = 1; config.ip[3] = 100;
    config.subnet[0] = 255; config.subnet[1] = 255; config.subnet[2] = 255; config.subnet[3] = 0;
    config.gateway[0] = 192; config.gateway[1] = 168; config.gateway[2] = 1; config.gateway[3] = 1;
    config.dns[0] = 8; config.dns[1] = 8; config.dns[2] = 8; config.dns[3] = 8;
    
    // 测试网关为网络地址
    config.gateway[0] = 192; config.gateway[1] = 168; config.gateway[2] = 1; config.gateway[3] = 0;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_ERR_INVALID_GW, "网关为网络地址");
    
    // 测试网关为广播地址
    config.gateway[0] = 192; config.gateway[1] = 168; config.gateway[2] = 1; config.gateway[3] = 255;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_ERR_INVALID_GW, "网关为广播地址");
    
    // 恢复正常网关
    config.gateway[0] = 192; config.gateway[1] = 168; config.gateway[2] = 1; config.gateway[3] = 1;
    
    // 测试DNS为特殊地址
    config.dns[0] = 0; config.dns[1] = 0; config.dns[2] = 0; config.dns[3] = 1;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_ERR_INVALID_DNS, "DNS为特殊地址");
    
    // 测试无效子网掩码
    config.dns[0] = 8; config.dns[1] = 8; config.dns[2] = 8; config.dns[3] = 8;
    config.subnet[0] = 255; config.subnet[1] = 0; config.subnet[2] = 255; config.subnet[3] = 0;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_ERR_INVALID_MASK, "不连续子网掩码");
}

/* 网络冲突检测测试 */
void test_network_conflict_detection(void)
{
    printf("\n=== 网络冲突检测测试 ===\n");
    
    NetConfig_t config1, config2;
    
    // 配置1
    config1.ip[0] = 192; config1.ip[1] = 168; config1.ip[2] = 1; config1.ip[3] = 100;
    config1.mac[0] = 0x00; config1.mac[1] = 0x08; config1.mac[2] = 0xDC;
    config1.mac[3] = 0x01; config1.mac[4] = 0x02; config1.mac[5] = 0x03;
    
    // 配置2 - 不同IP和MAC
    config2.ip[0] = 192; config2.ip[1] = 168; config2.ip[2] = 1; config2.ip[3] = 101;
    config2.mac[0] = 0x00; config2.mac[1] = 0x08; config2.mac[2] = 0xDC;
    config2.mac[3] = 0x01; config2.mac[4] = 0x02; config2.mac[5] = 0x04;
    TEST_ASSERT(check_network_conflict(&config1, &config2) == 0, "无冲突配置");
    
    // 相同IP地址
    config2.ip[0] = 192; config2.ip[1] = 168; config2.ip[2] = 1; config2.ip[3] = 100;
    TEST_ASSERT(check_network_conflict(&config1, &config2) == 1, "IP地址冲突");
    
    // 恢复不同IP，但相同MAC
    config2.ip[0] = 192; config2.ip[1] = 168; config2.ip[2] = 1; config2.ip[3] = 101;
    config2.mac[0] = 0x00; config2.mac[1] = 0x08; config2.mac[2] = 0xDC;
    config2.mac[3] = 0x01; config2.mac[4] = 0x02; config2.mac[5] = 0x03;
    TEST_ASSERT(check_network_conflict(&config1, &config2) == 1, "MAC地址冲突");
    
    // 空指针测试
    TEST_ASSERT(check_network_conflict(NULL, &config2) == 0, "空指针1");
    TEST_ASSERT(check_network_conflict(&config1, NULL) == 0, "空指针2");
}

/* 私有地址范围测试 */
void test_private_address_ranges(void)
{
    printf("\n=== 私有地址范围测试 ===\n");
    
    NetConfig_t config;
    config.subnet[0] = 255; config.subnet[1] = 255; config.subnet[2] = 255; config.subnet[3] = 0;
    config.dns[0] = 8; config.dns[1] = 8; config.dns[2] = 8; config.dns[3] = 8;
    
    // 10.0.0.0/8 私有地址范围
    config.ip[0] = 10; config.ip[1] = 0; config.ip[2] = 0; config.ip[3] = 100;
    config.gateway[0] = 10; config.gateway[1] = 0; config.gateway[2] = 0; config.gateway[3] = 1;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_OK, "10.x.x.x 私有地址");
    
    // **********/12 私有地址范围
    config.ip[0] = 172; config.ip[1] = 16; config.ip[2] = 0; config.ip[3] = 100;
    config.gateway[0] = 172; config.gateway[1] = 16; config.gateway[2] = 0; config.gateway[3] = 1;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_OK, "172.16.x.x 私有地址");
    
    // ***********/16 私有地址范围
    config.ip[0] = 192; config.ip[1] = 168; config.ip[2] = 100; config.ip[3] = 50;
    config.gateway[0] = 192; config.gateway[1] = 168; config.gateway[2] = 100; config.gateway[3] = 1;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_OK, "192.168.x.x 私有地址");
}

/* 运行所有扩展测试 */
int main(void)
{
    printf("开始网络参数完整性验证扩展测试...\n");
    
    test_network_scenarios();
    test_boundary_conditions();
    test_invalid_configurations();
    test_network_conflict_detection();
    test_private_address_ranges();
    
    printf("\n=== 测试结果 ===\n");
    printf("总测试数: %d\n", test_count);
    printf("通过测试: %d\n", test_passed);
    printf("失败测试: %d\n", test_count - test_passed);
    printf("通过率: %.1f%%\n", (float)test_passed / test_count * 100);
    
    return (test_count == test_passed) ? 0 : 1;
}