#include "udp.h"
#include "Spi.h"
#include "w5500.h"
#include "socket.h"	// Just include one header for WIZCHIP
#include "string.h"
#include "delay.h"
#include "stdio.h"
#include "gpio.h"
#include "Lcd_Interface.h"
#include "BER_Test.h"
extern u8 kls[50];
#define SOCK_TCPS        0
#define DATA_BUF_SIZE   2048
uint8_t gDATABUF[2048];

wiz_NetInfo gWIZNETINFO = { .mac = {0x00, 0xDA, 0x70,0xA4, 0x48, 0xA8},
                            .ip = {192, 168, 10, 120},    //本机IP
                            .sn = {255,255,255,0},
                            .gw = {192, 168, 10, 121},
                            .dns = {8,8,8,8},
                            .dhcp = NETINFO_STATIC };
uint8_t tmp;
int16_t ret = 0;
uint8_t memsize[2][8] = {{2,2,2,2,2,2,2,2},{2,2,2,2,2,2,2,2}};
uint8_t DstIP[4]={192,168,10,130};//目标IP
uint16_t	DstPort=9999;       //端口号
uint8_t udp_over = 0;

void udp_poll(void)
{
	
	switch(getSn_SR(0))																						// 获取socket0的状态
		{
			case SOCK_UDP:																							// Socket处于初始化完成(打开)状态
					Delay_Ms(100);
					if(getSn_IR(0) & Sn_IR_RECV)
					{
						setSn_IR(0, Sn_IR_RECV);															// Sn_IR的RECV位置1
					}
					// 数据回环测试程序：数据从远程上位机发给W5500，W5500接收到数据后再回给远程上位机
					if((ret = getSn_RX_RSR(0))>0)
					{ 
						
						memset(gDATABUF,0,ret+1);
						recvfrom(0,gDATABUF, ret, DstIP,&DstPort);			// W5500接收来自远程上位机的数据，并通过SPI发送给MCU
						udp_over = 1;  //UDP数据接收完成
//						printf("%s\r\n",gDATABUF);	
//						printf("%d\r\n",ret);						
//						sendto(0,gDATABUF,ret, DstIP, DstPort);		  		// 接收到数据后再回给远程上位机，完成数据回环
						
					}
			break;
			case SOCK_CLOSED:																						// Socket处于关闭状态
					socket(0,Sn_MR_UDP,9999,0x00);												// 打开Socket0，并配置为UDP模式，打开一个本地端口
			break;
		}
}
void udp_return(uint8_t number)
{
	u8 txdata[256];
	u8 i=0,Sum_Check=0;
	txdata[i++]=0x68;
	txdata[i++]=(u8)(id>>24);
	txdata[i++]=(u8)(id>>16);
	txdata[i++]=(u8)(id>>8);
	txdata[i++]=(u8)(id);
	txdata[i++]=number;  //控制字
	txdata[i++]=0x00;  //数据长度0000
	txdata[i++]=0x00;  //数据长度0000
	txdata[i++]= 0xEE;
	txdata[i++]= 0x16;
	sendto(0,txdata,i, DstIP, DstPort);	// 接收到数据后再回给远程上位机，完成数据回环
}
void UDP_01()
{
	u8 txdata[256];
	u8 i=0;
	txdata[i++]=0x68;
	txdata[i++]=0x00;  //此时上位机那边的ID还是00 00 00 00
	txdata[i++]=0x00;
	txdata[i++]=0x00;
	txdata[i++]=0x00;
	txdata[i++]=0x01;  //控制字
	txdata[i++]=0x00;  //数据长度60+18
	txdata[i++]=0x52;//0x4E;//0x3C;
	txdata[i++]=(u8)(id>>24);
	txdata[i++]=(u8)(id>>16);
	txdata[i++]=(u8)(id>>8);
	txdata[i++]=(u8)(id);
	txdata[i++]= pattern[0];
	txdata[i++]= udp[0][0];
	txdata[i++]= udp[0][1];
	txdata[i++]= udp[0][2];
	txdata[i++]= udp[0][3];
	txdata[i++]= udp[0][4];
	txdata[i++]= udp[0][5];
	txdata[i++]= udp[0][6];
	txdata[i++]= udp[0][7];
	txdata[i++]= pattern[1];
	txdata[i++]= udp[1][0];
	txdata[i++]= udp[1][1];
	txdata[i++]= udp[1][2];
	txdata[i++]= udp[1][3];
	txdata[i++]= udp[1][4];
	txdata[i++]= udp[1][5];
	txdata[i++]= udp[1][6];
	txdata[i++]= udp[1][7];
	txdata[i++]= pattern[2];
	txdata[i++]= udp[2][0];
	txdata[i++]= udp[2][1];
	txdata[i++]= udp[2][2];
	txdata[i++]= udp[2][3];
	txdata[i++]= udp[2][4];
	txdata[i++]= udp[2][5];
	txdata[i++]= udp[2][6];
	txdata[i++]= udp[2][7];
	txdata[i++]= pattern[3];
	txdata[i++]= udp[3][0];
	txdata[i++]= udp[3][1];
	txdata[i++]= udp[3][2];
	txdata[i++]= udp[3][3];
	txdata[i++]= udp[3][4];
	txdata[i++]= udp[3][5];
	txdata[i++]= udp[3][6];
	txdata[i++]= udp[3][7];
	txdata[i++]= rate;
	txdata[i++]= mode[0];
	txdata[i++]=(u8)(timer_set[0]>>24);
	txdata[i++]=(u8)(timer_set[0]>>16);
	txdata[i++]=(u8)(timer_set[0]>>8);
	txdata[i++]=(u8)(timer_set[0]);
	txdata[i++]= mode[1];
	txdata[i++]=(u8)(timer_set[1]>>24);
	txdata[i++]=(u8)(timer_set[1]>>16);
	txdata[i++]=(u8)(timer_set[1]>>8);
	txdata[i++]=(u8)(timer_set[1]);
	txdata[i++]= mode[2];
	txdata[i++]=(u8)(timer_set[2]>>24);
	txdata[i++]=(u8)(timer_set[2]>>16);
	txdata[i++]=(u8)(timer_set[2]>>8);
	txdata[i++]=(u8)(timer_set[2]);
	txdata[i++]= mode[3];
	txdata[i++]=(u8)(timer_set[3]>>24);
	txdata[i++]=(u8)(timer_set[3]>>16);
	txdata[i++]=(u8)(timer_set[3]>>8);
	txdata[i++]=(u8)(timer_set[3]);
	txdata[i++]= swing_ch[0];
	txdata[i++]= swing_ch[1];
	txdata[i++]= swing_ch[2];
	txdata[i++]= swing_ch[3];
	txdata[i++]= polarity_ch[0];
	txdata[i++]= polarity_ch[1];
	txdata[i++]= polarity_ch[2];
	txdata[i++]= polarity_ch[3];
	//txdata[i++]= ClockDivFlag;
	txdata[i++]= TX_Status;
	txdata[i++]= EQ_Ch[0].Eqpst;
	txdata[i++]= EQ_Ch[0].Eqpre;
	txdata[i++]= EQ_Ch[1].Eqpst;
	txdata[i++]= EQ_Ch[1].Eqpre;
	txdata[i++]= EQ_Ch[2].Eqpst;
	txdata[i++]= EQ_Ch[2].Eqpre;
	txdata[i++]= EQ_Ch[3].Eqpst;
	txdata[i++]= EQ_Ch[3].Eqpre;
	txdata[i++]= polarity_rx[0];
	txdata[i++]= polarity_rx[1];
	txdata[i++]= polarity_rx[2];
	txdata[i++]= polarity_rx[3];
	txdata[i++]= 0xEE;
	txdata[i++]= 0x16;
	sendto(0,txdata,i, DstIP, DstPort);	// 接收到数据后再回给远程上位机
}
void udp_20(void)
{
	u8 txdata[256];
	u8 i=0,Sum_Check=0;
	txdata[i++]=0x68;
	txdata[i++]=(u8)(id>>24);
	txdata[i++]=(u8)(id>>16);
	txdata[i++]=(u8)(id>>8);
	txdata[i++]=(u8)(id);
	txdata[i++]=0x20;  //控制字
	txdata[i++]=0x00;  //数据长度0014    20+5字节
	txdata[i++]=40;
	if(TIME_COUNT00 == 0) txdata[i++]= 0;
	else 	txdata[i++]= sta_ch0;
	txdata[i++]= (u8)(BEC_Value_ch[0]>>24);
	txdata[i++]= (u8)(BEC_Value_ch[0]>>16);
	txdata[i++]= (u8)(BEC_Value_ch[0]>>8);
	txdata[i++]= (u8)(BEC_Value_ch[0]);
	txdata[i++]= (u8)(TIME_COUNT00>>24);
	txdata[i++]= (u8)(TIME_COUNT00>>16);
	txdata[i++]= (u8)(TIME_COUNT00>>8);
	txdata[i++]= (u8)(TIME_COUNT00);
	txdata[i++]= (u8)(RUN_FLAG);
	if(TIME_COUNT11 == 0) txdata[i++]= 0;
	else 	txdata[i++]= sta_ch1;
	txdata[i++]= (u8)(BEC_Value_ch[1]>>24);
	txdata[i++]= (u8)(BEC_Value_ch[1]>>16);
	txdata[i++]= (u8)(BEC_Value_ch[1]>>8);
	txdata[i++]= (u8)(BEC_Value_ch[1]);
	txdata[i++]= (u8)(TIME_COUNT11>>24);
	txdata[i++]= (u8)(TIME_COUNT11>>16);
	txdata[i++]= (u8)(TIME_COUNT11>>8);
	txdata[i++]= (u8)(TIME_COUNT11);
	txdata[i++]= (u8)(RUN_FLAG1);
	if(TIME_COUNT22 == 0) txdata[i++]= 0;
	else txdata[i++]= sta_ch2;
	txdata[i++]= (u8)(BEC_Value_ch[2]>>24);
	txdata[i++]= (u8)(BEC_Value_ch[2]>>16);
	txdata[i++]= (u8)(BEC_Value_ch[2]>>8);
	txdata[i++]= (u8)(BEC_Value_ch[2]);
	txdata[i++]= (u8)(TIME_COUNT22>>24);
	txdata[i++]= (u8)(TIME_COUNT22>>16);
	txdata[i++]= (u8)(TIME_COUNT22>>8);
	txdata[i++]= (u8)(TIME_COUNT22);
	txdata[i++]= (u8)(RUN_FLAG2);
	if(TIME_COUNT33 == 0) txdata[i++]= 0;
	else 	txdata[i++]= sta_ch3;
	txdata[i++]= (u8)(BEC_Value_ch[3]>>24);
	txdata[i++]= (u8)(BEC_Value_ch[3]>>16);
	txdata[i++]= (u8)(BEC_Value_ch[3]>>8);
	txdata[i++]= (u8)(BEC_Value_ch[3]);
	txdata[i++]= (u8)(TIME_COUNT33>>24);
	txdata[i++]= (u8)(TIME_COUNT33>>16);
	txdata[i++]= (u8)(TIME_COUNT33>>8);
	txdata[i++]= (u8)(TIME_COUNT33);
	txdata[i++]= (u8)(RUN_FLAG3);
	txdata[i++]= 0xEE;
	txdata[i++]= 0x16;
	sendto(0,txdata,i, DstIP, DstPort);	// 接收到数据后再回给远程上位机
}
void udp_analysis(void)
{
//	uint8_t check;
//	check = Checksum (gDATABUF, ret-10 );//网络调试助手发送数据会多8
	uint16_t rev_num = 0;
	rev_num = ((gDATABUF[6]<<8)|gDATABUF[7]) + 18; 
	if(rev_num == ret)
	{
		if(gDATABUF[0] == 0x68&& gDATABUF[rev_num-9]==0x16 && gDATABUF[rev_num-10] == 0xEE)
		{
			switch(gDATABUF[5])
			{
				case 0x02:
					USB_biaoji = Down_Starttx;
					kls[7] = gDATABUF[8];
					kls[8] = gDATABUF[9];
				break;
				case 0x10:
					USB_biaoji = Down_Setup;
					kls[27] = gDATABUF[28];
					kls[7] = gDATABUF[8];
					kls[8] = gDATABUF[9];
					kls[9] = gDATABUF[10];
					kls[10] = gDATABUF[11];
					kls[11] = gDATABUF[12];
					kls[12] = gDATABUF[13];
					kls[13] = gDATABUF[14];
					kls[14] = gDATABUF[15];
					kls[15] = gDATABUF[16];
					kls[16] = gDATABUF[17];
					kls[17] = gDATABUF[18];
					kls[18] = gDATABUF[19];
					kls[19] = gDATABUF[20];
					kls[20] = gDATABUF[21];
					kls[21] = gDATABUF[22];
					kls[22] = gDATABUF[23];
					kls[23] = gDATABUF[24];
					kls[24] = gDATABUF[25];
					kls[25] = gDATABUF[26];
					kls[26] = gDATABUF[27];
				break;
				case 0x11:
					USB_biaoji = 11;
					kls[7] = gDATABUF[8];
					break;
				case 0x20:
					USB_biaoji = Up_Ber;
					break;
				case 0x01:
					USB_biaoji = Up_Config;
				    communication_flag = 0;
					break;
				case 0x00:
					USB_biaoji = Down_Startrx;
					kls[7] = gDATABUF[8];
					kls[8] = gDATABUF[9];
				break;
			}
		}
	}
	else
	{
		
	}
	ret = 0;
	memset(gDATABUF,0,sizeof(gDATABUF));
	udp_over = 0;//等待下次数据
}
void upd_connect(void)
{
	//W5500_spi_init();
	SPI_MasterInit();
	w5500_init();
    Reset_W5500();
  	//uint16_t	DstPort=9999;
	reg_wizchip_cris_cbfunc(SPI_CrisEnter, SPI_CrisExit);	//注册临界区函数
	/* Chip selection call back */
#if   _WIZCHIP_IO_MODE_ == _WIZCHIP_IO_MODE_SPI_VDM_
	reg_wizchip_cs_cbfunc(SPI_CS_Select, SPI_CS_Deselect);//注册SPI片选信号函数
#elif _WIZCHIP_IO_MODE_ == _WIZCHIP_IO_MODE_SPI_FDM_
	reg_wizchip_cs_cbfunc(SPI_CS_Select, SPI_CS_Deselect);  // CS must be tried with LOW.
#else
   #if (_WIZCHIP_IO_MODE_ & _WIZCHIP_IO_MODE_SIP_) != _WIZCHIP_IO_MODE_SIP_
      #error "Unknown _WIZCHIP_IO_MODE_"
   #else
      reg_wizchip_cs_cbfunc(wizchip_select, wizchip_deselect);
   #endif
#endif
	/* SPI Read & Write callback function */
	reg_wizchip_spi_cbfunc(SPI_ReadByte, SPI_WriteByte);	//注册读写函数
	/* WIZCHIP SOCKET Buffer initialize */
	if(ctlwizchip(CW_INIT_WIZCHIP,(void*)memsize) == -1){
		 printf("WIZCHIP Initialized fail.\r\n");
		 while(1);
	}

	/* PHY link status check */
//	do{
//		 if(ctlwizchip(CW_GET_PHYLINK, (void*)&tmp) == -1){
//				printf("Unknown PHY Link stauts.\r\n");
//		 }
//	}while(tmp == PHY_LINK_OFF);

	/* Network initialization */
	network_init();
}
void network_init(void)
{
    uint8_t tmpstr[6];
	int ret = ctlnetwork(CN_SET_NETINFO, (void*)&gWIZNETINFO);
	printf("ret=%d\n",ret);
	ret = ctlnetwork(CN_GET_NETINFO, (void*)&gWIZNETINFO);
	printf("ret=%d\n",ret);

	// Display Network Information
	ctlwizchip(CW_GET_ID,(void*)tmpstr);
	printf("\r\n=== %s NET CONF ===\r\n",(char*)tmpstr);
	printf("MAC: %02X:%02X:%02X:%02X:%02X:%02X\r\n",gWIZNETINFO.mac[0],gWIZNETINFO.mac[1],gWIZNETINFO.mac[2],
		  gWIZNETINFO.mac[3],gWIZNETINFO.mac[4],gWIZNETINFO.mac[5]);
	printf("SIP: %d.%d.%d.%d\r\n", gWIZNETINFO.ip[0],gWIZNETINFO.ip[1],gWIZNETINFO.ip[2],gWIZNETINFO.ip[3]);
	printf("GAR: %d.%d.%d.%d\r\n", gWIZNETINFO.gw[0],gWIZNETINFO.gw[1],gWIZNETINFO.gw[2],gWIZNETINFO.gw[3]);
	printf("SUB: %d.%d.%d.%d\r\n", gWIZNETINFO.sn[0],gWIZNETINFO.sn[1],gWIZNETINFO.sn[2],gWIZNETINFO.sn[3]);
	printf("DNS: %d.%d.%d.%d\r\n", gWIZNETINFO.dns[0],gWIZNETINFO.dns[1],gWIZNETINFO.dns[2],gWIZNETINFO.dns[3]);
	printf("======================\r\n");
}