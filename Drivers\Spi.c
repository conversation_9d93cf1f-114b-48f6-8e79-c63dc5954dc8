//************************************************************************************************
//**
//**文  件  名：Spi.c
//**
//**说      明：spi通讯
//**
//**作      者：
//**
//**时      间：2017年10月17日
//**
//************************************************************************************************
//驱动程序头文件
#include "Spi.h"
//------------------------------------------------------------------------------------------------
//SPI端口。
#define SPI1_BIT_CS				GPIO_Pin_4
#define SPI1_BIT_SCK			GPIO_Pin_5
#define SPI1_BIT_MISO    	GPIO_Pin_6
#define SPI1_BIT_MOSI     GPIO_Pin_7

#define SPI1_SCK_SOURCE		GPIO_PinSource5
#define SPI1_MISO_SOURCE	GPIO_PinSource6
#define SPI1_MOSI_SOURCE	GPIO_PinSource7

#define SPI1_GPIO     	GPIOA
#define SPI1_GPIO_AF    GPIO_AF_SPI1
#define Dummy_Byte 0x0

#define SPI1_CS_Set_H	GPIO_SetBits(SPI1_GPIO, SPI1_BIT_CS )
#define SPI1_CS_Set_L	GPIO_ResetBits(SPI1_GPIO, SPI1_BIT_CS )

//#define SPI1_DR_BASE	(SPI1_BASE+0x0C)
//u16 lmxconfig[2][65]={{0x221C,0x0808,0x03,0x00,0x3F,0x00,0x00,0x80,0x80,0x0A,0x78},
//                      {0x59,0x59,0x03,0x50,0x6A,0xF0,0xCE,0xD1,0x00,0x0E,0x78}};                      


void SPI_MasterInit(void)
{
    SPI_InitTypeDef  SPI_InitStructure;
    GPIO_InitTypeDef GPIO_InitStructure;
	
	/* 配置 SCK, MISO 、 MOSI 为复用功能 */
	GPIO_PinAFConfig(SPI1_GPIO, SPI1_SCK_SOURCE, SPI1_GPIO_AF);
	GPIO_PinAFConfig(SPI1_GPIO, SPI1_MISO_SOURCE, SPI1_GPIO_AF);
	GPIO_PinAFConfig(SPI1_GPIO, SPI1_MOSI_SOURCE, SPI1_GPIO_AF);    

   /* Configure SPI1 pins: NSS, SCK, MISO and MOSI */
  GPIO_InitStructure.GPIO_Pin = SPI1_BIT_MOSI | SPI1_BIT_MISO | SPI1_BIT_SCK;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;	
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_PuPd  = GPIO_PuPd_NOPULL;
  GPIO_Init(SPI1_GPIO, &GPIO_InitStructure);
	
	GPIO_InitStructure.GPIO_Pin = SPI1_BIT_CS;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
	GPIO_Init(SPI1_GPIO, &GPIO_InitStructure);
	SPI1_CS_Set_H;
	
	SPI_I2S_DeInit(SPI1);
    /* SPI1 configuration */ 
    SPI_InitStructure.SPI_Direction = SPI_Direction_2Lines_FullDuplex; //SPI1设置为两线全双工
    SPI_InitStructure.SPI_Mode = SPI_Mode_Master;                       //设置SPI1为主模式
    SPI_InitStructure.SPI_DataSize = SPI_DataSize_8b;                  //SPI发送接收16位帧结构
	SPI_InitStructure.SPI_CPOL = SPI_CPOL_Low;		//串行同步时钟的空闲状态为高电平
	SPI_InitStructure.SPI_CPHA = SPI_CPHA_1Edge;	//串行同步时钟的第二个跳变沿（上升或下降）数据被采样
    SPI_InitStructure.SPI_NSS = SPI_NSS_Soft;                           //NSS信号由软件（使用SSI位）管理
    SPI_InitStructure.SPI_BaudRatePrescaler = SPI_BaudRatePrescaler_256; //定义波特率预分频的值:波特率预分频值为8
    SPI_InitStructure.SPI_FirstBit = SPI_FirstBit_MSB;                   //数据传输从MSB位开始
    SPI_InitStructure.SPI_CRCPolynomial = 7;                           //CRC值计算的多项式
    SPI_Init(SPI1, &SPI_InitStructure);
    /* Enable SPI1  */
    SPI_Cmd(SPI1, ENABLE);                                               //使能SPI1外设
} 

void SPI_WriteByte(uint8 byte)
{
  /* Loop while DR register in not emplty */
  while (SPI_I2S_GetFlagStatus(SPI1, SPI_I2S_FLAG_TXE) == RESET);

  /* Send byte through the SPI1 peripheral */
  SPI_I2S_SendData(SPI1, byte);
}

uint8 SPI_ReadByte(void)	
{
    return SPI_ReadAndWriteByte(Dummy_Byte);	
    //return (SPI_WriteByte(Dummy_Byte));
}

uint8 SPI_ReadAndWriteByte(uint8 TxData)
{                 
    u16 retry=0; 
//    Delay_Us(100);	
    while(SPI_I2S_GetFlagStatus(SPI1, SPI_I2S_FLAG_TXE) == RESET)//等待发送区空    
    {
//        retry++;
//        if(retry>0x1000)return 0;
    }              
    SPI_I2S_SendData(SPI1, TxData); 
    retry=0;
    while(SPI_I2S_GetFlagStatus(SPI1, SPI_I2S_FLAG_RXNE) == RESET) //等待接收完一个byte  
    {
//        retry++;
//        if(retry>0x1000)return 0;
    }                                  
    return SPI_I2S_ReceiveData(SPI1);          //返回收到的数据         
}

void LMX2592_Write(u8 add, u16 data)
{
	SPI1_CS_Set_L;
//__nop();__nop();
	
//	SPI_ReadAndWriteByte(0xFF);
	SPI_ReadAndWriteByte(add);
//	__nop();
	SPI_ReadAndWriteByte((u8)(data>>8));

	SPI_ReadAndWriteByte((u8)data);
//__nop();__nop();
	SPI1_CS_Set_H;	
}

u16 LMX2592_Read(u8 add)
{
	u8 temp1,temp2;
	u16 temp=0xFFFF;
	SPI1_CS_Set_L;
//__nop();__nop();
//	SPI_ReadAndWriteByte(0xFF);
	SPI_ReadAndWriteByte(add|0x80);
//	__nop();
	temp1=SPI_ReadAndWriteByte(Dummy_Byte);

	temp2=SPI_ReadAndWriteByte(Dummy_Byte);
//__nop();__nop();		
	temp=(u16)(temp1<<8)+temp2;
	SPI1_CS_Set_H;	
	
  return  temp;	
}

void LMX2592_WriteRWM(u8 add, u16 data,u16 mask)
{
	u16 readVal,writeVal;
    readVal = LMX2592_Read(add);
    writeVal = (readVal & ~mask) +(data & mask);
	LMX2592_Write(add,writeVal);	
}


void LMX2592_Init_Config(void)
{
//1. Apply power to the device and ensure the V CC pins are at the proper levels
//2. Ensure that a valid reference is applied to the OSCin pin
//3. Soft reset the device (write R0[1] = 1)
//4. Program the remaining registers
//5. Frequency calibrate (write R0[3] = 1)

//	VIEWTECH_A01 (100,20,0x6022,0xf800,0x39c8,LMX2592_Read(0));
//	LMX2592_Write(0, 0x2218);
//	LMX2592_Write(0x3B, 0x0020);
//	VIEWTECH_A01 (200,20,0x6022,0xf800,0x39c8,LMX2592_Read(0));
	
	LMX2592_Write(0, 0x231A);
	Delay_Us(1000);
	LMX2592_Write(0, 0x2318);	

	LMX2592_Write(64, 0x0077);
	//VIEWTECH_A01 (100,40,0x6022,0xf800,0x39c8,LMX2592_Read(64));
	LMX2592_Write(62, 0x0000);
	LMX2592_Write(61, 0x0000);
	//VIEWTECH_A01 (200,40,0x6022,0xf800,0x39c8,LMX2592_Read(61));
	LMX2592_Write(59, 0x0020);
	LMX2592_Write(48, 0x03FD);
	//VIEWTECH_A01 (300,40,0x6022,0xf800,0x39c8,LMX2592_Read(48));
	LMX2592_Write(47, 0x08C7);
	//VIEWTECH_A01 (400,40,0x6022,0xf800,0x39c8,LMX2592_Read(47));
	LMX2592_Write(46, 0x0721);
	
	LMX2592_Write(45, 0x0001);
	LMX2592_Write(44, 0x0000);
	
	LMX2592_Write(43, 0x0000);		
	LMX2592_Write(42, 0x0000);
	
	LMX2592_Write(41, 0x0002);
	LMX2592_Write(40, 0x0000);
	
	LMX2592_Write(39, 0x8104);
	LMX2592_Write(38, 0x0020);
	
	LMX2592_Write(37, 0x4000);
	LMX2592_Write(36, 0x0021);
	
	LMX2592_Write(35, 0x009B);
	LMX2592_Write(34, 0xC3EA);
	
  LMX2592_Write(33, 0x2A0A);
	LMX2592_Write(32, 0x210A);
	LMX2592_Write(31, 0x0001);
	LMX2592_Write(30, 0x0034);
	LMX2592_Write(29, 0x0084);
	LMX2592_Write(28, 0x2924);
	LMX2592_Write(25, 0x0000);
	LMX2592_Write(24, 0x0509);
	LMX2592_Write(23, 0x8842);
	LMX2592_Write(22, 0x2300);
	LMX2592_Write(20, 0x012C);
	LMX2592_Write(19, 0x0965);
	LMX2592_Write(14, 0x018C);	
	LMX2592_Write(13, 0x4000);
	LMX2592_Write(12, 0x7002);
	
	LMX2592_Write(11, 0x0018);
	LMX2592_Write(10, 0x10D8);
	LMX2592_Write(9, 0x0302);
	LMX2592_Write(8, 0x1084);
	LMX2592_Write(7, 0x28B2);
	LMX2592_Write(4, 0x1943);
	LMX2592_Write(2, 0x0500);
	LMX2592_Write(1, 0x0809);

	LMX2592_Write(0, 0x2318);	
}

void LMX2592_Change_Config(u16 r48,u16 r47,u16 r45,u16 r41,u16 r38,u16 r37,u16 r36,u16 r35,u16 r31,u16 r30,u16 r0)
{
//1. Set the new N divider value (write R38[12:1])
//2. Set the new PLL numerator (R45 and R44) and denominator (R41 and R40)
//3. Frequency calibrate (write R0[3] = 1)
	
	LMX2592_Write(48, r48);
	LMX2592_Write(47, r47);
	
	LMX2592_Write(45, r45);	
	LMX2592_Write(44, 0x0000);

	LMX2592_Write(41, r41);
	LMX2592_Write(40, 0x0000);
	
	LMX2592_Write(38, r38);
	LMX2592_Write(37, r37);
	LMX2592_Write(36, r36);
	LMX2592_Write(35, r35);
	LMX2592_Write(31, r31);
	LMX2592_Write(30, r30);
	
	LMX2592_Write(0, r0);
}



/**
  * @brief  ?????
  * @retval None
  */
void SPI_CrisEnter(void)
{
	__set_PRIMASK(1);
}
/**
  * @brief  ?????
  * @retval None
  */
void SPI_CrisExit(void)
{
	__set_PRIMASK(0);
}

/**
  * @brief  ?????????
  * @retval None
  */
void SPI_CS_Select(void)
{
	GPIO_ResetBits(GPIOA,GPIO_Pin_4);
}

/**
  * @brief  ?????????
  * @retval None
  */
void SPI_CS_Deselect(void)
{
	GPIO_SetBits(GPIOA,GPIO_Pin_4);
}



