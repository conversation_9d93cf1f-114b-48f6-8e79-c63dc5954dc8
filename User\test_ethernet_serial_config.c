#include <stdio.h>
#include <string.h>
#include <assert.h>
#include "serial_net_config.h"
#include "Drivers.h"

/**
 * @brief 测试串口命令解析功能
 */
void test_serial_command_parsing(void)
{
    printf("=== 测试串口命令解析功能 ===\n");
    
    SerialCommand_t cmd;
    CommandType_t result;
    
    // 测试查看网络配置命令
    result = parse_serial_command("NET_CONFIG", &cmd);
    assert(result == CMD_NET_CONFIG);
    printf("✓ NET_CONFIG 命令解析成功\n");
    
    // 测试设置IP地址命令
    result = parse_serial_command("SET_IP ***********00", &cmd);
    assert(result == CMD_SET_IP);
    assert(cmd.param_count == 1);
    printf("✓ SET_IP 命令解析成功\n");
    
    // 测试设置子网掩码命令
    result = parse_serial_command("SET_MASK *************", &cmd);
    assert(result == CMD_SET_MASK);
    assert(cmd.param_count == 1);
    printf("✓ SET_MASK 命令解析成功\n");
    
    // 测试设置网关命令
    result = parse_serial_command("SET_GW ***********", &cmd);
    assert(result == CMD_SET_GW);
    assert(cmd.param_count == 1);
    printf("✓ SET_GW 命令解析成功\n");
    
    // 测试应用网络配置命令
    result = parse_serial_command("APPLY_NET", &cmd);
    assert(result == CMD_APPLY_NET);
    printf("✓ APPLY_NET 命令解析成功\n");
    
    // 测试无效命令
    result = parse_serial_command("INVALID_CMD", &cmd);
    assert(result == CMD_UNKNOWN);
    printf("✓ 无效命令正确识别\n");
    
    printf("串口命令解析测试通过！\n\n");
}

/**
 * @brief 测试IP地址验证功能
 */
void test_ip_validation(void)
{
    printf("=== 测试IP地址验证功能 ===\n");
    
    // 测试有效IP地址
    assert(validate_ip_format("***********") == NET_CONFIG_OK);
    assert(validate_ip_format("********") == NET_CONFIG_OK);
    assert(validate_ip_format("**********") == NET_CONFIG_OK);
    printf("✓ 有效IP地址验证通过\n");
    
    // 测试无效IP地址
    assert(validate_ip_format("256.1.1.1") != NET_CONFIG_OK);
    assert(validate_ip_format("192.168.1") != NET_CONFIG_OK);
    assert(validate_ip_format("***********.1") != NET_CONFIG_OK);
    assert(validate_ip_format("abc.def.ghi.jkl") != NET_CONFIG_OK);
    printf("✓ 无效IP地址正确拒绝\n");
    
    // 测试特殊IP地址
    assert(is_special_ip_address((uint8_t[]){0, 0, 0, 0}) == 1);      // 0.0.0.0
    assert(is_special_ip_address((uint8_t[]){255, 255, 255, 255}) == 1); // ***************
    assert(is_special_ip_address((uint8_t[]){127, 0, 0, 1}) == 1);    // 127.0.0.1
    printf("✓ 特殊IP地址正确识别\n");
    
    printf("IP地址验证测试通过！\n\n");
}

/**
 * @brief 测试子网掩码验证功能
 */
void test_subnet_validation(void)
{
    printf("=== 测试子网掩码验证功能 ===\n");
    
    // 测试有效子网掩码
    uint8_t valid_masks[][4] = {
        {255, 255, 255, 0},   // /24
        {255, 255, 0, 0},     // /16
        {255, 0, 0, 0},       // /8
        {255, 255, 255, 128}, // /25
    };
    
    for (int i = 0; i < 4; i++) {
        assert(validate_subnet_mask(valid_masks[i]) == NET_CONFIG_OK);
    }
    printf("✓ 有效子网掩码验证通过\n");
    
    // 测试无效子网掩码
    uint8_t invalid_masks[][4] = {
        {255, 255, 0, 255},   // 不连续
        {128, 255, 255, 0},   // 不连续
        {255, 254, 255, 0},   // 不连续
    };
    
    for (int i = 0; i < 3; i++) {
        assert(validate_subnet_mask(invalid_masks[i]) != NET_CONFIG_OK);
    }
    printf("✓ 无效子网掩码正确拒绝\n");
    
    printf("子网掩码验证测试通过！\n\n");
}

/**
 * @brief 测试网络配置存储功能
 */
void test_network_config_storage(void)
{
    printf("=== 测试网络配置存储功能 ===\n");
    
    NetConfig_t test_config;
    NetConfig_t loaded_config;
    NetConfigError_t result;
    
    // 准备测试配置
    uint8_t test_ip[] = {192, 168, 1, 100};
    uint8_t test_mask[] = {255, 255, 255, 0};
    uint8_t test_gw[] = {192, 168, 1, 1};
    uint8_t test_dns[] = {8, 8, 8, 8};
    uint8_t test_mac[] = {0x00, 0x08, 0xDC, 0x01, 0x02, 0x03};
    
    memcpy(test_config.ip, test_ip, 4);
    memcpy(test_config.subnet, test_mask, 4);
    memcpy(test_config.gateway, test_gw, 4);
    memcpy(test_config.dns, test_dns, 4);
    memcpy(test_config.mac, test_mac, 6);
    test_config.dhcp_mode = 0; // 静态IP
    memset(test_config.reserved, 0, sizeof(test_config.reserved));
    test_config.checksum = 0;
    
    // 测试保存配置
    result = save_network_config(&test_config);
    if (result == NET_CONFIG_OK) {
        printf("✓ 网络配置保存成功\n");
    } else {
        printf("✗ 网络配置保存失败，错误码: %d\n", result);
        return;
    }
    
    // 测试加载配置
    result = load_network_config(&loaded_config);
    if (result == NET_CONFIG_OK) {
        printf("✓ 网络配置加载成功\n");
    } else {
        printf("✗ 网络配置加载失败，错误码: %d\n", result);
        return;
    }
    
    // 验证配置内容
    if (memcmp(&test_config.ip, &loaded_config.ip, 4) == 0 &&
        memcmp(&test_config.subnet, &loaded_config.subnet, 4) == 0 &&
        memcmp(&test_config.gateway, &loaded_config.gateway, 4) == 0 &&
        memcmp(&test_config.dns, &loaded_config.dns, 4) == 0 &&
        memcmp(&test_config.mac, &loaded_config.mac, 6) == 0 &&
        test_config.dhcp_mode == loaded_config.dhcp_mode) {
        printf("✓ 配置内容验证通过\n");
    } else {
        printf("✗ 配置内容验证失败\n");
        return;
    }
    
    printf("网络配置存储测试通过！\n\n");
}

/**
 * @brief 测试网络配置应用功能
 */
void test_network_config_application(void)
{
    printf("=== 测试网络配置应用功能 ===\n");
    
    NetConfigError_t result = apply_network_config();
    
    if (result == NET_CONFIG_OK) {
        printf("✓ 网络配置应用成功\n");
    } else {
        printf("✗ 网络配置应用失败，错误码: %d\n", result);
    }
    
    printf("网络配置应用测试完成！\n\n");
}

/**
 * @brief 测试串口配置任务
 */
void test_serial_config_task(void)
{
    printf("=== 测试串口配置任务 ===\n");
    
    // 模拟串口接收到配置命令
    strcpy((char*)UART1.RecBuff, "NET_CONFIG\r\n");
    UART1.ReceiveBytes = strlen("NET_CONFIG\r\n");
    UART1.RecTimeOutFlag = 1;
    
    // 调用串口配置任务
    serial_config_task();
    
    printf("✓ 串口配置任务执行完成\n");
    printf("串口配置任务测试完成！\n\n");
}

/**
 * @brief 主测试函数
 */
void run_ethernet_serial_config_tests(void)
{
    printf("=== 以太网串口配置功能综合测试 ===\n\n");
    
    // 运行各项测试
    test_serial_command_parsing();
    test_ip_validation();
    test_subnet_validation();
    test_network_config_storage();
    test_network_config_application();
    test_serial_config_task();
    
    printf("=== 所有测试完成！===\n");
    printf("以太网串口配置功能已就绪，可以通过串口发送以下命令进行配置：\n");
    printf("- NET_CONFIG: 查看当前网络配置\n");
    printf("- SET_IP <ip_address>: 设置IP地址\n");
    printf("- SET_MASK <subnet_mask>: 设置子网掩码\n");
    printf("- SET_GW <gateway>: 设置网关\n");
    printf("- SET_DNS <dns_server>: 设置DNS服务器\n");
    printf("- APPLY_NET: 应用网络配置\n");
    printf("- RESET_NET: 重置为默认配置\n\n");
}
