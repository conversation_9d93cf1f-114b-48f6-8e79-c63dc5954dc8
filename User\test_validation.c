#include "serial_net_config.h"
#include <stdio.h>
#include <string.h>
#include <assert.h>

/* 单元测试框架 */
static int test_count = 0;
static int test_passed = 0;

#define TEST_ASSERT(condition, message) \
    do { \
        test_count++; \
        if (condition) { \
            test_passed++; \
            printf("PASS: %s\n", message); \
        } else { \
            printf("FAIL: %s\n", message); \
        } \
    } while(0)

/* IP地址格式验证测试 */
void test_validate_ip_format(void)
{
    printf("\n=== IP地址格式验证测试 ===\n");
    
    // 有效IP地址测试
    TEST_ASSERT(validate_ip_format("***********") == NET_CONFIG_OK, 
                "有效IP地址: ***********");
    TEST_ASSERT(validate_ip_format("0.0.0.0") == NET_CONFIG_OK, 
                "边界值: 0.0.0.0");
    TEST_ASSERT(validate_ip_format("***************") == NET_CONFIG_OK, 
                "边界值: ***************");
    TEST_ASSERT(validate_ip_format("1*******") == NET_CONFIG_OK, 
                "有效IP地址: 1*******");
    TEST_ASSERT(validate_ip_format("**********") == NET_CONFIG_OK, 
                "有效IP地址: **********");
    
    // 无效IP地址测试
    TEST_ASSERT(validate_ip_format(NULL) == NET_CONFIG_ERR_INVALID_IP, 
                "空指针");
    TEST_ASSERT(validate_ip_format("") == NET_CONFIG_ERR_INVALID_IP, 
                "空字符串");
    TEST_ASSERT(validate_ip_format("192.168.1") == NET_CONFIG_ERR_INVALID_IP, 
                "缺少八位组");
    TEST_ASSERT(validate_ip_format("***********.1") == NET_CONFIG_ERR_INVALID_IP, 
                "多余八位组");
    TEST_ASSERT(validate_ip_format("256.168.1.1") == NET_CONFIG_ERR_INVALID_IP, 
                "超出范围: 256");
    TEST_ASSERT(validate_ip_format("192.168.1.-1") == NET_CONFIG_ERR_INVALID_IP, 
                "负数");
    TEST_ASSERT(validate_ip_format("************") == NET_CONFIG_ERR_INVALID_IP, 
                "前导零");
    TEST_ASSERT(validate_ip_format("192.168.1.a") == NET_CONFIG_ERR_INVALID_IP, 
                "非数字字符");
    TEST_ASSERT(validate_ip_format("192..1.1") == NET_CONFIG_ERR_INVALID_IP, 
                "连续点号");
    TEST_ASSERT(validate_ip_format("192.168.1.") == NET_CONFIG_ERR_INVALID_IP, 
                "末尾点号");
    TEST_ASSERT(validate_ip_format(".***********") == NET_CONFIG_ERR_INVALID_IP, 
                "开头点号");
}

/* IP地址解析测试 */
void test_parse_ip_string(void)
{
    printf("\n=== IP地址解析测试 ===\n");
    
    uint8_t ip_bytes[4];
    
    // 正常解析测试
    TEST_ASSERT(parse_ip_string("***********00", ip_bytes) == NET_CONFIG_OK &&
                ip_bytes[0] == 192 && ip_bytes[1] == 168 && 
                ip_bytes[2] == 1 && ip_bytes[3] == 100, 
                "解析IP: ***********00");
    
    TEST_ASSERT(parse_ip_string("0.0.0.0", ip_bytes) == NET_CONFIG_OK &&
                ip_bytes[0] == 0 && ip_bytes[1] == 0 && 
                ip_bytes[2] == 0 && ip_bytes[3] == 0, 
                "解析IP: 0.0.0.0");
    
    TEST_ASSERT(parse_ip_string("***************", ip_bytes) == NET_CONFIG_OK &&
                ip_bytes[0] == 255 && ip_bytes[1] == 255 && 
                ip_bytes[2] == 255 && ip_bytes[3] == 255, 
                "解析IP: ***************");
    
    // 错误输入测试
    TEST_ASSERT(parse_ip_string("invalid.ip", ip_bytes) == NET_CONFIG_ERR_INVALID_IP, 
                "无效IP字符串");
    TEST_ASSERT(parse_ip_string("***********00", NULL) == NET_CONFIG_ERR_INVALID_PARAM, 
                "空输出缓冲区");
}

/* 特殊IP地址检测测试 */
void test_is_special_ip_address(void)
{
    printf("\n=== 特殊IP地址检测测试 ===\n");
    
    uint8_t ip[4];
    
    // 特殊地址测试
    ip[0] = 0; ip[1] = 0; ip[2] = 0; ip[3] = 1;  // *******
    TEST_ASSERT(is_special_ip_address(ip) == 1, "本网络地址: *******");
    
    ip[0] = 127; ip[1] = 0; ip[2] = 0; ip[3] = 1;  // 127.0.0.1
    TEST_ASSERT(is_special_ip_address(ip) == 1, "回环地址: 127.0.0.1");
    
    ip[0] = 224; ip[1] = 0; ip[2] = 0; ip[3] = 1;  // *********
    TEST_ASSERT(is_special_ip_address(ip) == 1, "多播地址: *********");
    
    ip[0] = 240; ip[1] = 0; ip[2] = 0; ip[3] = 1;  // 24*******
    TEST_ASSERT(is_special_ip_address(ip) == 1, "保留地址: 24*******");
    
    ip[0] = 255; ip[1] = 255; ip[2] = 255; ip[3] = 255;  // ***************
    TEST_ASSERT(is_special_ip_address(ip) == 1, "广播地址: ***************");
    
    // 普通地址测试
    ip[0] = 192; ip[1] = 168; ip[2] = 1; ip[3] = 100;  // ***********00
    TEST_ASSERT(is_special_ip_address(ip) == 0, "普通地址: ***********00");
    
    ip[0] = 10; ip[1] = 0; ip[2] = 0; ip[3] = 1;  // 1*******
    TEST_ASSERT(is_special_ip_address(ip) == 0, "私有地址: 1*******");
    
    TEST_ASSERT(is_special_ip_address(NULL) == 1, "空指针");
}

/* 子网掩码验证测试 */
void test_validate_subnet_mask(void)
{
    printf("\n=== 子网掩码验证测试 ===\n");
    
    uint8_t mask[4];
    
    // 有效子网掩码测试
    mask[0] = 255; mask[1] = 255; mask[2] = 255; mask[3] = 0;  // *************
    TEST_ASSERT(validate_subnet_mask(mask) == NET_CONFIG_OK, "有效掩码: *************");
    
    mask[0] = 255; mask[1] = 255; mask[2] = 0; mask[3] = 0;  // ***********
    TEST_ASSERT(validate_subnet_mask(mask) == NET_CONFIG_OK, "有效掩码: ***********");
    
    mask[0] = 255; mask[1] = 0; mask[2] = 0; mask[3] = 0;  // *********
    TEST_ASSERT(validate_subnet_mask(mask) == NET_CONFIG_OK, "有效掩码: *********");
    
    mask[0] = 255; mask[1] = 255; mask[2] = 255; mask[3] = 128;  // ***************
    TEST_ASSERT(validate_subnet_mask(mask) == NET_CONFIG_OK, "有效掩码: ***************");
    
    // 无效子网掩码测试
    mask[0] = 0; mask[1] = 0; mask[2] = 0; mask[3] = 0;  // 0.0.0.0
    TEST_ASSERT(validate_subnet_mask(mask) == NET_CONFIG_ERR_INVALID_MASK, "无效掩码: 0.0.0.0");
    
    mask[0] = 255; mask[1] = 255; mask[2] = 255; mask[3] = 255;  // ***************
    TEST_ASSERT(validate_subnet_mask(mask) == NET_CONFIG_ERR_INVALID_MASK, "无效掩码: ***************");
    
    mask[0] = 255; mask[1] = 255; mask[2] = 1; mask[3] = 0;  // *********** (不连续)
    TEST_ASSERT(validate_subnet_mask(mask) == NET_CONFIG_ERR_INVALID_MASK, "不连续掩码: ***********");
    
    TEST_ASSERT(validate_subnet_mask(NULL) == NET_CONFIG_ERR_INVALID_MASK, "空指针");
}

/* 网络参数完整性验证测试 */
void test_validate_network_params(void)
{
    printf("\n=== 网络参数完整性验证测试 ===\n");
    
    NetConfig_t config;
    
    // 有效配置测试
    config.ip[0] = 192; config.ip[1] = 168; config.ip[2] = 1; config.ip[3] = 100;
    config.subnet[0] = 255; config.subnet[1] = 255; config.subnet[2] = 255; config.subnet[3] = 0;
    config.gateway[0] = 192; config.gateway[1] = 168; config.gateway[2] = 1; config.gateway[3] = 1;
    config.dns[0] = 8; config.dns[1] = 8; config.dns[2] = 8; config.dns[3] = 8;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_OK, "有效网络配置");
    
    // IP地址为特殊地址
    config.ip[0] = 127; config.ip[1] = 0; config.ip[2] = 0; config.ip[3] = 1;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_ERR_INVALID_IP, "IP为回环地址");
    
    // 恢复正常IP
    config.ip[0] = 192; config.ip[1] = 168; config.ip[2] = 1; config.ip[3] = 100;
    
    // 网关不在同一子网
    config.gateway[0] = 10; config.gateway[1] = 0; config.gateway[2] = 0; config.gateway[3] = 1;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_ERR_INVALID_GW, "网关不在同一子网");
    
    // 恢复正常网关
    config.gateway[0] = 192; config.gateway[1] = 168; config.gateway[2] = 1; config.gateway[3] = 1;
    
    // IP地址为网络地址
    config.ip[0] = 192; config.ip[1] = 168; config.ip[2] = 1; config.ip[3] = 0;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_ERR_INVALID_IP, "IP为网络地址");
    
    // IP地址为广播地址
    config.ip[0] = 192; config.ip[1] = 168; config.ip[2] = 1; config.ip[3] = 255;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_ERR_INVALID_IP, "IP为广播地址");
    
    // IP地址和网关相同
    config.ip[0] = 192; config.ip[1] = 168; config.ip[2] = 1; config.ip[3] = 1;
    TEST_ASSERT(validate_network_params(&config) == NET_CONFIG_ERR_INVALID_GW, "IP和网关相同");
    
    TEST_ASSERT(validate_network_params(NULL) == NET_CONFIG_ERR_INVALID_PARAM, "空指针");
}

/* MAC地址验证测试 */
void test_validate_mac_address(void)
{
    printf("\n=== MAC地址验证测试 ===\n");
    
    uint8_t mac[6];
    
    // 有效MAC地址
    mac[0] = 0x00; mac[1] = 0x08; mac[2] = 0xDC; 
    mac[3] = 0x01; mac[4] = 0x02; mac[5] = 0x03;
    TEST_ASSERT(validate_mac_address(mac) == NET_CONFIG_OK, "有效MAC地址");
    
    // 全零MAC地址
    memset(mac, 0, 6);
    TEST_ASSERT(validate_mac_address(mac) == NET_CONFIG_ERR_INVALID_PARAM, "全零MAC地址");
    
    // 广播MAC地址
    memset(mac, 0xFF, 6);
    TEST_ASSERT(validate_mac_address(mac) == NET_CONFIG_ERR_INVALID_PARAM, "广播MAC地址");
    
    // 多播MAC地址 (第一个字节最低位为1)
    mac[0] = 0x01; mac[1] = 0x08; mac[2] = 0xDC; 
    mac[3] = 0x01; mac[4] = 0x02; mac[5] = 0x03;
    TEST_ASSERT(validate_mac_address(mac) == NET_CONFIG_ERR_INVALID_PARAM, "多播MAC地址");
    
    TEST_ASSERT(validate_mac_address(NULL) == NET_CONFIG_ERR_INVALID_PARAM, "空指针");
}

/* 运行所有测试 */
int main(void)
{
    printf("开始参数验证模块单元测试...\n");
    
    test_validate_ip_format();
    test_parse_ip_string();
    test_is_special_ip_address();
    test_validate_subnet_mask();
    test_validate_network_params();
    test_validate_mac_address();
    
    printf("\n=== 测试结果 ===\n");
    printf("总测试数: %d\n", test_count);
    printf("通过测试: %d\n", test_passed);
    printf("失败测试: %d\n", test_count - test_passed);
    printf("通过率: %.1f%%\n", (float)test_passed / test_count * 100);
    
    return (test_count == test_passed) ? 0 : 1;
}