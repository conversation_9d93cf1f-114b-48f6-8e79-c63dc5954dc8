# 串口配置以太网IP功能设计文档

## 概述

本设计文档描述了为100G误码仪项目添加串口配置以太网IP功能的技术实现方案。该功能允许用户通过串口命令动态配置网络参数，包括IP地址、子网掩码、网关和DNS等。

## 架构设计

### 系统架构图

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   串口终端      │    │   STM32F429      │    │    W5500        │
│   (PC/调试器)   │◄──►│  串口命令解析器   │◄──►│   以太网芯片     │
└─────────────────┘    │  网络配置管理器   │    └─────────────────┘
                       │  参数验证器       │    
                       │  存储管理器       │    ┌─────────────────┐
                       └──────────────────┘    │   Flash存储     │
                                ▲              │  (网络配置)     │
                                │              └─────────────────┘
                                ▼
                       ┌──────────────────┐
                       │   UART1接口      │
                       │  (115200,8,N,1)  │
                       └──────────────────┘
```

### 模块设计

#### 1. 串口命令解析器 (SerialCommandParser)
- **功能**: 解析串口接收的命令字符串
- **输入**: 串口接收缓冲区数据
- **输出**: 解析后的命令结构体
- **接口**: `parse_command(char* input, Command* cmd)`

#### 2. 网络配置管理器 (NetworkConfigManager)
- **功能**: 管理网络参数的设置和应用
- **输入**: 网络配置参数
- **输出**: 配置结果状态
- **接口**: `set_network_config(NetConfig* config)`

#### 3. 参数验证器 (ParameterValidator)
- **功能**: 验证网络参数的有效性
- **输入**: 待验证的网络参数
- **输出**: 验证结果和错误信息
- **接口**: `validate_ip(char* ip_str)`

#### 4. 存储管理器 (StorageManager)
- **功能**: 管理网络配置的持久化存储
- **输入**: 网络配置数据
- **输出**: 存储操作结果
- **接口**: `save_config(NetConfig* config)`

## 组件和接口

### 数据结构定义

```c
// 网络配置结构体
typedef struct {
    uint8_t ip[4];          // IP地址
    uint8_t subnet[4];      // 子网掩码
    uint8_t gateway[4];     // 网关地址
    uint8_t dns[4];         // DNS服务器
    uint8_t mac[6];         // MAC地址
    uint8_t dhcp_mode;      // DHCP模式
    uint32_t checksum;      // 配置校验和
} NetConfig_t;

// 命令结构体
typedef struct {
    uint8_t cmd_type;       // 命令类型
    char param[64];         // 命令参数
    uint8_t param_count;    // 参数个数
} SerialCommand_t;

// 命令类型枚举
typedef enum {
    CMD_NET_CONFIG = 0x01,  // 查看网络配置
    CMD_SET_IP,             // 设置IP地址
    CMD_SET_MASK,           // 设置子网掩码
    CMD_SET_GW,             // 设置网关
    CMD_SET_DNS,            // 设置DNS
    CMD_APPLY_NET,          // 应用网络配置
    CMD_RESET_NET,          // 重置网络配置
    CMD_AUTH,               // 身份验证
    CMD_UNKNOWN = 0xFF      // 未知命令
} CommandType_t;
```

### 核心接口函数

```c
// 串口命令处理主函数
void serial_config_task(void);

// 命令解析函数
CommandType_t parse_serial_command(char* input, SerialCommand_t* cmd);

// 网络配置函数
int set_ip_address(char* ip_str);
int set_subnet_mask(char* mask_str);
int set_gateway(char* gw_str);
int set_dns_server(char* dns_str);

// 配置应用函数
int apply_network_config(void);
int reset_network_config(void);

// 参数验证函数
int validate_ip_format(char* ip_str);
int validate_network_params(NetConfig_t* config);

// 存储管理函数
int save_network_config(NetConfig_t* config);
int load_network_config(NetConfig_t* config);

// 安全认证函数
int authenticate_user(char* password);
void lock_config_access(void);
```

## 数据模型

### Flash存储布局

```
Flash地址: 0x080E0000 (896KB位置)
┌─────────────────────────────────────┐
│ 配置头部 (16字节)                    │
│ - 魔数: 0x5A5A5A5A (4字节)          │
│ - 版本: 0x0001 (2字节)              │
│ - 长度: sizeof(NetConfig_t) (2字节)  │
│ - CRC32校验 (4字节)                 │
│ - 保留 (4字节)                      │
├─────────────────────────────────────┤
│ 网络配置数据 (32字节)                │
│ - IP地址 (4字节)                    │
│ - 子网掩码 (4字节)                  │
│ - 网关地址 (4字节)                  │
│ - DNS服务器 (4字节)                 │
│ - MAC地址 (6字节)                   │
│ - DHCP模式 (1字节)                  │
│ - 保留 (9字节)                      │
└─────────────────────────────────────┘
```

### 串口协议格式

```
命令格式: <COMMAND> [PARAM1] [PARAM2] ...\r\n
响应格式: <STATUS>:<MESSAGE>\r\n

示例:
输入: SET_IP ***********00\r\n
输出: OK:IP address set to ***********00\r\n

输入: NET_CONFIG\r\n
输出: OK:IP=***********00,MASK=*************,GW=***********\r\n
```

## 错误处理

### 错误代码定义

```c
typedef enum {
    NET_CONFIG_OK = 0,          // 操作成功
    NET_CONFIG_ERR_INVALID_IP,  // 无效IP地址
    NET_CONFIG_ERR_INVALID_CMD, // 无效命令
    NET_CONFIG_ERR_AUTH_FAIL,   // 认证失败
    NET_CONFIG_ERR_STORAGE,     // 存储错误
    NET_CONFIG_ERR_NETWORK,     // 网络错误
    NET_CONFIG_ERR_TIMEOUT,     // 操作超时
    NET_CONFIG_ERR_LOCKED       // 访问被锁定
} NetConfigError_t;
```

### 错误处理策略

1. **参数验证错误**: 返回具体错误信息，不修改当前配置
2. **存储错误**: 尝试重试3次，失败后使用默认配置
3. **网络应用错误**: 自动回滚到之前的有效配置
4. **认证错误**: 记录失败次数，超过阈值后锁定访问

## 测试策略

### 单元测试

1. **参数验证测试**
   - 测试各种有效和无效的IP地址格式
   - 测试边界值和特殊情况

2. **命令解析测试**
   - 测试所有支持的命令格式
   - 测试错误命令和参数

3. **存储功能测试**
   - 测试配置保存和读取
   - 测试存储错误恢复

### 集成测试

1. **串口通信测试**
   - 测试完整的命令-响应流程
   - 测试并发命令处理

2. **网络配置测试**
   - 测试配置应用后的网络连通性
   - 测试配置回滚功能

3. **安全功能测试**
   - 测试认证机制
   - 测试访问锁定功能

### 系统测试

1. **端到端测试**
   - 通过串口完整配置网络参数
   - 验证配置持久化和重启恢复

2. **压力测试**
   - 测试大量连续命令处理
   - 测试长时间运行稳定性

## 性能考虑

### 响应时间要求
- 命令解析: < 10ms
- 参数验证: < 5ms
- 配置应用: < 2s
- 存储操作: < 100ms

### 内存使用
- 命令缓冲区: 256字节
- 配置缓存: 64字节
- 临时变量: < 128字节

### 并发处理
- 串口命令处理采用状态机方式
- 网络配置操作互斥，避免冲突
- 支持命令队列，最多缓存3个命令

## 安全考虑

### 访问控制
- 默认管理员密码: "admin123"
- 密码错误锁定时间: 30分钟
- 会话超时时间: 10分钟

### 数据保护
- 配置数据CRC32校验
- 关键操作日志记录
- 敏感信息不在串口明文显示

### 故障恢复
- 配置损坏时自动使用默认配置
- 网络故障时自动回滚机制
- 看门狗保护防止死锁