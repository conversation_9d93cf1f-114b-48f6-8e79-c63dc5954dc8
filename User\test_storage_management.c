#include <stdio.h>
#include <string.h>
#include <assert.h>
#include <stdint.h>

// We need to define our mock functions before including the header
// Mock Flash functions for testing (since we can't actually write to <PERSON> in test environment)
static uint8_t mock_flash_memory[8192]; // 8KB mock Flash sector
static int mock_flash_unlocked = 0;

// Mock Flash function implementations
void FLASH_Unlock(void) {
    mock_flash_unlocked = 1;
}

void FLASH_Lock(void) {
    mock_flash_unlocked = 0;
}

void FLASH_DataCacheCmd(int enable) {
    // Mock implementation - do nothing
}

typedef enum {
    FLASH_COMPLETE = 0,
    FLASH_ERROR = 1
} FLASH_Status;

FLASH_Status FLASH_EraseSector(uint32_t sector, uint8_t voltage_range) {
    if (!mock_flash_unlocked) return FLASH_ERROR;
    
    // Clear the mock Flash memory
    memset(mock_flash_memory, 0xFF, sizeof(mock_flash_memory));
    return FLASH_COMPLETE;
}

FLASH_Status FLASH_ProgramWord(uint32_t address, uint32_t data) {
    if (!mock_flash_unlocked) return FLASH_ERROR;
    
    // Calculate offset in mock memory based on original Flash address
    uint32_t offset = address - 0x080E0000; // Original NET_CONFIG_FLASH_ADDR
    if (offset >= sizeof(mock_flash_memory) - 4) return FLASH_ERROR;
    
    // Write data to mock memory
    *(uint32_t*)(mock_flash_memory + offset) = data;
    return FLASH_COMPLETE;
}

// Now include the header and redefine the Flash address to point to our mock memory
#include "serial_net_config.h"

// Override the Flash address to point to our mock memory
#undef NET_CONFIG_FLASH_ADDR
#define NET_CONFIG_FLASH_ADDR ((uint32_t)mock_flash_memory)

/**
 * @brief Test CRC32 calculation
 */
void test_crc32_calculation(void) {
    printf("Testing CRC32 calculation...\n");
    
    // Test with known data
    uint8_t test_data[] = "Hello, World!";
    uint32_t crc = calculate_crc32(test_data, strlen((char*)test_data));
    
    printf("CRC32 of 'Hello, World!': 0x%08X\n", crc);
    
    // Test with NULL data
    uint32_t null_crc = calculate_crc32(NULL, 10);
    assert(null_crc == 0);
    
    // Test with empty data
    uint32_t empty_crc = calculate_crc32(test_data, 0);
    printf("CRC32 of empty data: 0x%08X\n", empty_crc);
    
    printf("CRC32 calculation tests passed!\n\n");
}

/**
 * @brief Test configuration validation
 */
void test_config_validation(void) {
    printf("Testing configuration validation...\n");
    
    NetConfig_t test_config;
    NetConfigHeader_t test_header;
    
    // Create valid test configuration
    uint8_t test_ip[] = {192, 168, 1, 100};
    uint8_t test_mask[] = {255, 255, 255, 0};
    uint8_t test_gw[] = {192, 168, 1, 1};
    uint8_t test_dns[] = {8, 8, 8, 8};
    uint8_t test_mac[] = {0x00, 0x08, 0xDC, 0x01, 0x02, 0x03};
    
    memcpy(test_config.ip, test_ip, 4);
    memcpy(test_config.subnet, test_mask, 4);
    memcpy(test_config.gateway, test_gw, 4);
    memcpy(test_config.dns, test_dns, 4);
    memcpy(test_config.mac, test_mac, 6);
    test_config.dhcp_mode = 0;
    memset(test_config.reserved, 0, sizeof(test_config.reserved));
    test_config.checksum = 0;
    
    // Test valid configuration
    NetConfigError_t result = validate_network_params(&test_config);
    assert(result == NET_CONFIG_OK);
    printf("Valid configuration validation passed\n");
    
    // Create valid header
    test_header.magic = NET_CONFIG_MAGIC_NUMBER;
    test_header.version = NET_CONFIG_VERSION;
    test_header.length = sizeof(NetConfig_t);
    test_header.crc32 = calculate_crc32((const uint8_t*)&test_config, sizeof(NetConfig_t));
    test_header.reserved = 0;
    
    // Test integrity verification
    result = verify_config_integrity(&test_header, &test_config);
    assert(result == NET_CONFIG_OK);
    printf("Configuration integrity verification passed\n");
    
    // Test with wrong magic number
    test_header.magic = 0x12345678;
    result = verify_config_integrity(&test_header, &test_config);
    assert(result == NET_CONFIG_ERR_STORAGE_CRC);
    printf("Wrong magic number detection passed\n");
    
    // Test with wrong CRC
    test_header.magic = NET_CONFIG_MAGIC_NUMBER;
    test_header.crc32 = 0x12345678;
    result = verify_config_integrity(&test_header, &test_config);
    assert(result == NET_CONFIG_ERR_STORAGE_CRC);
    printf("Wrong CRC detection passed\n");
    
    printf("Configuration validation tests passed!\n\n");
}

/**
 * @brief Test default configuration restore
 */
void test_default_config_restore(void) {
    printf("Testing default configuration restore...\n");
    
    NetConfigError_t result = restore_default_config();
    printf("restore_default_config() returned: %d\n", result);
    
    if (result != NET_CONFIG_OK) {
        printf("Error: %s\n", get_error_message(result));
    }
    
    assert(result == NET_CONFIG_OK);
    
    printf("Default configuration restore test passed!\n\n");
}

/**
 * @brief Test configuration save and load cycle
 */
void test_save_load_cycle(void) {
    printf("Testing configuration save and load cycle...\n");
    
    // Create test configuration
    NetConfig_t original_config;
    uint8_t test_ip[] = {10, 0, 0, 100};
    uint8_t test_mask[] = {255, 255, 0, 0};
    uint8_t test_gw[] = {10, 0, 0, 1};
    uint8_t test_dns[] = {1, 1, 1, 1};
    uint8_t test_mac[] = {0x02, 0x00, 0x00, 0x00, 0x00, 0x01};
    
    memcpy(original_config.ip, test_ip, 4);
    memcpy(original_config.subnet, test_mask, 4);
    memcpy(original_config.gateway, test_gw, 4);
    memcpy(original_config.dns, test_dns, 4);
    memcpy(original_config.mac, test_mac, 6);
    original_config.dhcp_mode = 1; // Enable DHCP
    memset(original_config.reserved, 0, sizeof(original_config.reserved));
    original_config.checksum = 0;
    
    // Save configuration
    NetConfigError_t result = save_network_config(&original_config);
    assert(result == NET_CONFIG_OK);
    printf("Configuration save passed\n");
    
    // Load configuration
    NetConfig_t loaded_config;
    result = load_network_config(&loaded_config);
    assert(result == NET_CONFIG_OK);
    printf("Configuration load passed\n");
    
    // Compare configurations (excluding checksum field)
    assert(memcmp(original_config.ip, loaded_config.ip, 4) == 0);
    assert(memcmp(original_config.subnet, loaded_config.subnet, 4) == 0);
    assert(memcmp(original_config.gateway, loaded_config.gateway, 4) == 0);
    assert(memcmp(original_config.dns, loaded_config.dns, 4) == 0);
    assert(memcmp(original_config.mac, loaded_config.mac, 6) == 0);
    assert(original_config.dhcp_mode == loaded_config.dhcp_mode);
    
    printf("Configuration comparison passed\n");
    printf("Save and load cycle test passed!\n\n");
}

/**
 * @brief Test backup functionality
 */
void test_backup_functionality(void) {
    printf("Testing backup functionality...\n");
    
    NetConfigError_t result = backup_current_config();
    assert(result == NET_CONFIG_OK);
    
    printf("Backup functionality test passed!\n\n");
}

/**
 * @brief Main test function
 */
int main(void) {
    printf("=== Storage Management Module Tests ===\n\n");
    
    // Initialize mock Flash memory
    memset(mock_flash_memory, 0xFF, sizeof(mock_flash_memory));
    
    // Run tests
    test_crc32_calculation();
    test_config_validation();
    test_default_config_restore();
    test_save_load_cycle();
    test_backup_functionality();
    
    printf("=== All Storage Management Tests Passed! ===\n");
    
    return 0;
}