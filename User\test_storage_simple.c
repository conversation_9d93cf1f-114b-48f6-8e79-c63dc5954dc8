#include <stdio.h>
#include <string.h>
#include <assert.h>
#include <stdint.h>

// Include only the header definitions we need, not the full implementation
typedef struct {
    uint8_t ip[4];          // IP地址
    uint8_t subnet[4];      // 子网掩码
    uint8_t gateway[4];     // 网关地址
    uint8_t dns[4];         // DNS服务器
    uint8_t mac[6];         // MAC地址
    uint8_t dhcp_mode;      // DHCP模式 (0=静态, 1=DHCP)
    uint8_t reserved[9];    // 保留字节
    uint32_t checksum;      // 配置校验和
} NetConfig_t;

typedef struct {
    uint32_t magic;         // 魔数
    uint16_t version;       // 版本
    uint16_t length;        // 数据长度
    uint32_t crc32;         // CRC32校验
    uint32_t reserved;      // 保留
} NetConfigHeader_t;

typedef enum {
    NET_CONFIG_OK = 0,              // 操作成功
    NET_CONFIG_ERR_INVALID_IP,      // 无效IP地址
    NET_CONFIG_ERR_INVALID_MASK,    // 无效子网掩码
    NET_CONFIG_ERR_INVALID_GW,      // 无效网关地址
    NET_CONFIG_ERR_INVALID_DNS,     // 无效DNS地址
    NET_CONFIG_ERR_INVALID_CMD,     // 无效命令
    NET_CONFIG_ERR_INVALID_PARAM,   // 无效参数
    NET_CONFIG_ERR_AUTH_FAIL,       // 认证失败
    NET_CONFIG_ERR_AUTH_REQUIRED,   // 需要认证
    NET_CONFIG_ERR_AUTH_LOCKED,     // 认证被锁定
    NET_CONFIG_ERR_SESSION_TIMEOUT, // 会话超时
    NET_CONFIG_ERR_STORAGE_READ,    // 存储读取错误
    NET_CONFIG_ERR_STORAGE_WRITE,   // 存储写入错误
    NET_CONFIG_ERR_STORAGE_ERASE,   // 存储擦除错误
    NET_CONFIG_ERR_STORAGE_CRC,     // 存储CRC校验错误
    NET_CONFIG_ERR_NETWORK_APPLY,   // 网络配置应用错误
    NET_CONFIG_ERR_NETWORK_TEST,    // 网络连通性测试错误
    NET_CONFIG_ERR_TIMEOUT,         // 操作超时
    NET_CONFIG_ERR_BUSY,            // 系统忙
    NET_CONFIG_ERR_NOT_SUPPORTED,   // 不支持的操作
    NET_CONFIG_ERR_UNKNOWN = 0xFF   // 未知错误
} NetConfigError_t;

#define NET_CONFIG_MAGIC_NUMBER     0x5A5A5A5A  // 配置魔数
#define NET_CONFIG_VERSION          0x0001      // 配置版本

// Declare only the functions we want to test
NetConfigError_t validate_ip_format(const char* ip_str);
NetConfigError_t parse_ip_string(const char* ip_str, uint8_t ip_bytes[4]);
int is_special_ip_address(const uint8_t ip_bytes[4]);
NetConfigError_t validate_subnet_mask(const uint8_t mask_bytes[4]);
NetConfigError_t validate_network_params(const NetConfig_t* config);
uint32_t calculate_crc32(const uint8_t* data, size_t length);
NetConfigError_t verify_config_integrity(const NetConfigHeader_t* header, const NetConfig_t* config);

/**
 * @brief Test CRC32 calculation functionality
 */
void test_crc32_functionality(void) {
    printf("Testing CRC32 calculation...\n");
    
    // Test with known data
    uint8_t test_data[] = "Hello, World!";
    uint32_t crc = calculate_crc32(test_data, strlen((char*)test_data));
    
    printf("CRC32 of 'Hello, World!': 0x%08X\n", crc);
    assert(crc != 0); // Should not be zero for this data
    
    // Test with NULL data
    uint32_t null_crc = calculate_crc32(NULL, 10);
    assert(null_crc == 0);
    
    // Test with empty data
    uint32_t empty_crc = calculate_crc32(test_data, 0);
    printf("CRC32 of empty data: 0x%08X\n", empty_crc);
    
    // Test consistency - same data should produce same CRC
    uint32_t crc2 = calculate_crc32(test_data, strlen((char*)test_data));
    assert(crc == crc2);
    
    printf("CRC32 calculation tests passed!\n\n");
}

/**
 * @brief Test network parameter validation
 */
void test_network_validation(void) {
    printf("Testing network parameter validation...\n");
    
    NetConfig_t test_config;
    
    // Create valid test configuration
    uint8_t test_ip[] = {192, 168, 1, 100};
    uint8_t test_mask[] = {255, 255, 255, 0};
    uint8_t test_gw[] = {192, 168, 1, 1};
    uint8_t test_dns[] = {8, 8, 8, 8};
    uint8_t test_mac[] = {0x00, 0x08, 0xDC, 0x01, 0x02, 0x03};
    
    memcpy(test_config.ip, test_ip, 4);
    memcpy(test_config.subnet, test_mask, 4);
    memcpy(test_config.gateway, test_gw, 4);
    memcpy(test_config.dns, test_dns, 4);
    memcpy(test_config.mac, test_mac, 6);
    test_config.dhcp_mode = 0;
    memset(test_config.reserved, 0, sizeof(test_config.reserved));
    test_config.checksum = 0;
    
    // Test valid configuration
    NetConfigError_t result = validate_network_params(&test_config);
    assert(result == NET_CONFIG_OK);
    printf("Valid configuration validation passed\n");
    
    // Test invalid IP (network address)
    uint8_t invalid_ip[] = {192, 168, 1, 0};
    memcpy(test_config.ip, invalid_ip, 4);
    result = validate_network_params(&test_config);
    assert(result != NET_CONFIG_OK);
    printf("Invalid IP (network address) detection passed\n");
    
    // Test invalid IP (broadcast address)
    uint8_t broadcast_ip[] = {192, 168, 1, 255};
    memcpy(test_config.ip, broadcast_ip, 4);
    result = validate_network_params(&test_config);
    assert(result != NET_CONFIG_OK);
    printf("Invalid IP (broadcast address) detection passed\n");
    
    // Test gateway not in same subnet
    memcpy(test_config.ip, test_ip, 4); // Restore valid IP
    uint8_t wrong_gw[] = {10, 0, 0, 1};
    memcpy(test_config.gateway, wrong_gw, 4);
    result = validate_network_params(&test_config);
    assert(result != NET_CONFIG_OK);
    printf("Gateway not in subnet detection passed\n");
    
    printf("Network parameter validation tests passed!\n\n");
}

/**
 * @brief Test configuration integrity verification
 */
void test_config_integrity(void) {
    printf("Testing configuration integrity verification...\n");
    
    NetConfig_t test_config;
    NetConfigHeader_t test_header;
    
    // Create valid test configuration
    uint8_t test_ip[] = {192, 168, 1, 100};
    uint8_t test_mask[] = {255, 255, 255, 0};
    uint8_t test_gw[] = {192, 168, 1, 1};
    uint8_t test_dns[] = {8, 8, 8, 8};
    uint8_t test_mac[] = {0x00, 0x08, 0xDC, 0x01, 0x02, 0x03};
    
    memcpy(test_config.ip, test_ip, 4);
    memcpy(test_config.subnet, test_mask, 4);
    memcpy(test_config.gateway, test_gw, 4);
    memcpy(test_config.dns, test_dns, 4);
    memcpy(test_config.mac, test_mac, 6);
    test_config.dhcp_mode = 0;
    memset(test_config.reserved, 0, sizeof(test_config.reserved));
    test_config.checksum = 0;
    
    // Create valid header
    test_header.magic = NET_CONFIG_MAGIC_NUMBER;
    test_header.version = NET_CONFIG_VERSION;
    test_header.length = sizeof(NetConfig_t);
    test_header.crc32 = calculate_crc32((const uint8_t*)&test_config, sizeof(NetConfig_t));
    test_header.reserved = 0;
    
    // Test valid integrity
    NetConfigError_t result = verify_config_integrity(&test_header, &test_config);
    assert(result == NET_CONFIG_OK);
    printf("Valid integrity verification passed\n");
    
    // Test with wrong magic number
    test_header.magic = 0x12345678;
    result = verify_config_integrity(&test_header, &test_config);
    assert(result == NET_CONFIG_ERR_STORAGE_CRC);
    printf("Wrong magic number detection passed\n");
    
    // Test with wrong version
    test_header.magic = NET_CONFIG_MAGIC_NUMBER;
    test_header.version = 0x9999;
    result = verify_config_integrity(&test_header, &test_config);
    assert(result == NET_CONFIG_ERR_STORAGE_CRC);
    printf("Wrong version detection passed\n");
    
    // Test with wrong length
    test_header.version = NET_CONFIG_VERSION;
    test_header.length = 999;
    result = verify_config_integrity(&test_header, &test_config);
    assert(result == NET_CONFIG_ERR_STORAGE_CRC);
    printf("Wrong length detection passed\n");
    
    // Test with wrong CRC
    test_header.length = sizeof(NetConfig_t);
    test_header.crc32 = 0x12345678;
    result = verify_config_integrity(&test_header, &test_config);
    assert(result == NET_CONFIG_ERR_STORAGE_CRC);
    printf("Wrong CRC detection passed\n");
    
    printf("Configuration integrity tests passed!\n\n");
}

/**
 * @brief Test IP address format validation
 */
void test_ip_validation(void) {
    printf("Testing IP address format validation...\n");
    
    // Test valid IP addresses
    assert(validate_ip_format("***********") == NET_CONFIG_OK);
    assert(validate_ip_format("*******") == NET_CONFIG_OK);
    assert(validate_ip_format("***************") == NET_CONFIG_OK);
    printf("Valid IP format tests passed\n");
    
    // Test invalid IP addresses
    assert(validate_ip_format(NULL) != NET_CONFIG_OK);
    assert(validate_ip_format("") != NET_CONFIG_OK);
    assert(validate_ip_format("256.1.1.1") != NET_CONFIG_OK);
    assert(validate_ip_format("1.1.1") != NET_CONFIG_OK);
    assert(validate_ip_format("*******.1") != NET_CONFIG_OK);
    assert(validate_ip_format("1..1.1") != NET_CONFIG_OK);
    assert(validate_ip_format(".1.1.1") != NET_CONFIG_OK);
    assert(validate_ip_format("1.1.1.") != NET_CONFIG_OK);
    assert(validate_ip_format("********") != NET_CONFIG_OK); // Leading zero
    assert(validate_ip_format("1.1.1.a") != NET_CONFIG_OK);
    printf("Invalid IP format detection tests passed\n");
    
    printf("IP address validation tests passed!\n\n");
}

/**
 * @brief Test subnet mask validation
 */
void test_subnet_validation(void) {
    printf("Testing subnet mask validation...\n");
    
    // Test valid subnet masks
    uint8_t valid_masks[][4] = {
        {255, 255, 255, 0},
        {255, 255, 0, 0},
        {255, 0, 0, 0},
        {255, 255, 255, 128},
        {255, 255, 255, 192},
        {255, 255, 255, 224},
        {255, 255, 255, 240},
        {255, 255, 255, 248},
        {255, 255, 255, 252}
    };
    
    for (int i = 0; i < sizeof(valid_masks) / sizeof(valid_masks[0]); i++) {
        NetConfigError_t result = validate_subnet_mask(valid_masks[i]);
        assert(result == NET_CONFIG_OK);
    }
    printf("Valid subnet mask tests passed\n");
    
    // Test invalid subnet masks
    uint8_t invalid_masks[][4] = {
        {0, 0, 0, 0},           // All zeros
        {255, 255, 255, 255},   // All ones
        {255, 0, 255, 0},       // Non-contiguous
        {128, 255, 255, 0},     // Non-contiguous
        {255, 255, 128, 255}    // Non-contiguous
    };
    
    for (int i = 0; i < sizeof(invalid_masks) / sizeof(invalid_masks[0]); i++) {
        NetConfigError_t result = validate_subnet_mask(invalid_masks[i]);
        assert(result != NET_CONFIG_OK);
    }
    printf("Invalid subnet mask detection tests passed\n");
    
    printf("Subnet mask validation tests passed!\n\n");
}

/**
 * @brief Main test function
 */
int main(void) {
    printf("=== Storage Management Module Tests (Core Functions) ===\n\n");
    
    // Run core functionality tests that don't require Flash operations
    test_crc32_functionality();
    test_ip_validation();
    test_subnet_validation();
    test_network_validation();
    test_config_integrity();
    
    printf("=== All Core Storage Management Tests Passed! ===\n");
    printf("Note: Flash storage operations require embedded environment for full testing.\n");
    
    return 0;
}