#include <string.h>
#include <stdlib.h>
#include <ctype.h>
#include <stdio.h>
#include <stdint.h>

typedef struct {
    uint8_t ip[4];          // IP地址
    uint8_t subnet[4];      // 子网掩码
    uint8_t gateway[4];     // 网关地址
    uint8_t dns[4];         // DNS服务器
    uint8_t mac[6];         // MAC地址
    uint8_t dhcp_mode;      // DHCP模式 (0=静态, 1=DHCP)
    uint8_t reserved[9];    // 保留字节
    uint32_t checksum;      // 配置校验和
} NetConfig_t;

typedef struct {
    uint32_t magic;         // 魔数
    uint16_t version;       // 版本
    uint16_t length;        // 数据长度
    uint32_t crc32;         // CRC32校验
    uint32_t reserved;      // 保留
} NetConfigHeader_t;

typedef enum {
    NET_CONFIG_OK = 0,              // 操作成功
    NET_CONFIG_ERR_INVALID_IP,      // 无效IP地址
    NET_CONFIG_ERR_INVALID_MASK,    // 无效子网掩码
    NET_CONFIG_ERR_INVALID_GW,      // 无效网关地址
    NET_CONFIG_ERR_INVALID_DNS,     // 无效DNS地址
    NET_CONFIG_ERR_INVALID_CMD,     // 无效命令
    NET_CONFIG_ERR_INVALID_PARAM,   // 无效参数
    NET_CONFIG_ERR_AUTH_FAIL,       // 认证失败
    NET_CONFIG_ERR_AUTH_REQUIRED,   // 需要认证
    NET_CONFIG_ERR_AUTH_LOCKED,     // 认证被锁定
    NET_CONFIG_ERR_SESSION_TIMEOUT, // 会话超时
    NET_CONFIG_ERR_STORAGE_READ,    // 存储读取错误
    NET_CONFIG_ERR_STORAGE_WRITE,   // 存储写入错误
    NET_CONFIG_ERR_STORAGE_ERASE,   // 存储擦除错误
    NET_CONFIG_ERR_STORAGE_CRC,     // 存储CRC校验错误
    NET_CONFIG_ERR_NETWORK_APPLY,   // 网络配置应用错误
    NET_CONFIG_ERR_NETWORK_TEST,    // 网络连通性测试错误
    NET_CONFIG_ERR_TIMEOUT,         // 操作超时
    NET_CONFIG_ERR_BUSY,            // 系统忙
    NET_CONFIG_ERR_NOT_SUPPORTED,   // 不支持的操作
    NET_CONFIG_ERR_UNKNOWN = 0xFF   // 未知错误
} NetConfigError_t;

#define NET_CONFIG_MAGIC_NUMBER     0x5A5A5A5A  // 配置魔数
#define NET_CONFIG_VERSION          0x0001      // 配置版本

// CRC32查找表
static const uint32_t crc32_table[256] = {
    0x00000000, 0x77073096, 0xEE0E612C, 0x990951BA, 0x076DC419, 0x706AF48F,
    0xE963A535, 0x9E6495A3, 0x0EDB8832, 0x79DCB8A4, 0xE0D5E91E, 0x97D2D988,
    0x09B64C2B, 0x7EB17CBD, 0xE7B82D07, 0x90BF1D91, 0x1DB71064, 0x6AB020F2,
    0xF3B97148, 0x84BE41DE, 0x1ADAD47D, 0x6DDDE4EB, 0xF4D4B551, 0x83D385C7,
    0x136C9856, 0x646BA8C0, 0xFD62F97A, 0x8A65C9EC, 0x14015C4F, 0x63066CD9,
    0xFA0F3D63, 0x8D080DF5, 0x3B6E20C8, 0x4C69105E, 0xD56041E4, 0xA2677172,
    0x3C03E4D1, 0x4B04D447, 0xD20D85FD, 0xA50AB56B, 0x35B5A8FA, 0x42B2986C,
    0xDBBBC9D6, 0xACBCF940, 0x32D86CE3, 0x45DF5C75, 0xDCD60DCF, 0xABD13D59,
    0x26D930AC, 0x51DE003A, 0xC8D75180, 0xBFD06116, 0x21B4F4B5, 0x56B3C423,
    0xCFBA9599, 0xB8BDA50F, 0x2802B89E, 0x5F058808, 0xC60CD9B2, 0xB10BE924,
    0x2F6F7C87, 0x58684C11, 0xC1611DAB, 0xB6662D3D, 0x76DC4190, 0x01DB7106,
    0x98D220BC, 0xEFD5102A, 0x71B18589, 0x06B6B51F, 0x9FBFE4A5, 0xE8B8D433,
    0x7807C9A2, 0x0F00F934, 0x9609A88E, 0xE10E9818, 0x7F6A0DBB, 0x086D3D2D,
    0x91646C97, 0xE6635C01, 0x6B6B51F4, 0x1C6C6162, 0x856530D8, 0xF262004E,
    0x6C0695ED, 0x1B01A57B, 0x8208F4C1, 0xF50FC457, 0x65B0D9C6, 0x12B7E950,
    0x8BBEB8EA, 0xFCB9887C, 0x62DD1DDF, 0x15DA2D49, 0x8CD37CF3, 0xFBD44C65,
    0x4DB26158, 0x3AB551CE, 0xA3BC0074, 0xD4BB30E2, 0x4ADFA541, 0x3DD895D7,
    0xA4D1C46D, 0xD3D6F4FB, 0x4369E96A, 0x346ED9FC, 0xAD678846, 0xDA60B8D0,
    0x44042D73, 0x33031DE5, 0xAA0A4C5F, 0xDD0D7CC9, 0x5005713C, 0x270241AA,
    0xBE0B1010, 0xC90C2086, 0x5768B525, 0x206F85B3, 0xB966D409, 0xCE61E49F,
    0x5EDEF90E, 0x29D9C998, 0xB0D09822, 0xC7D7A8B4, 0x59B33D17, 0x2EB40D81,
    0xB7BD5C3B, 0xC0BA6CAD, 0xEDB88320, 0x9ABFB3B6, 0x03B6E20C, 0x74B1D29A,
    0xEAD54739, 0x9DD277AF, 0x04DB2615, 0x73DC1683, 0xE3630B12, 0x94643B84,
    0x0D6D6A3E, 0x7A6A5AA8, 0xE40ECF0B, 0x9309FF9D, 0x0A00AE27, 0x7D079EB1,
    0xF00F9344, 0x8708A3D2, 0x1E01F268, 0x6906C2FE, 0xF762575D, 0x806567CB,
    0x196C3671, 0x6E6B06E7, 0xFED41B76, 0x89D32BE0, 0x10DA7A5A, 0x67DD4ACC,
    0xF9B9DF6F, 0x8EBEEFF9, 0x17B7BE43, 0x60B08ED5, 0xD6D6A3E8, 0xA1D1937E,
    0x38D8C2C4, 0x4FDFF252, 0xD1BB67F1, 0xA6BC5767, 0x3FB506DD, 0x48B2364B,
    0xD80D2BDA, 0xAF0A1B4C, 0x36034AF6, 0x41047A60, 0xDF60EFC3, 0xA867DF55,
    0x316E8EEF, 0x4669BE79, 0xCB61B38C, 0xBC66831A, 0x256FD2A0, 0x5268E236,
    0xCC0C7795, 0xBB0B4703, 0x220216B9, 0x5505262F, 0xC5BA3BBE, 0xB2BD0B28,
    0x2BB45A92, 0x5CB36A04, 0xC2D7FFA7, 0xB5D0CF31, 0x2CD99E8B, 0x5BDEAE1D,
    0x9B64C2B0, 0xEC63F226, 0x756AA39C, 0x026D930A, 0x9C0906A9, 0xEB0E363F,
    0x72076785, 0x05005713, 0x95BF4A82, 0xE2B87A14, 0x7BB12BAE, 0x0CB61B38,
    0x92D28E9B, 0xE5D5BE0D, 0x7CDCEFB7, 0x0BDBDF21, 0x86D3D2D4, 0xF1D4E242,
    0x68DDB3F8, 0x1FDA836E, 0x81BE16CD, 0xF6B9265B, 0x6FB077E1, 0x18B74777,
    0x88085AE6, 0xFF0F6A70, 0x66063BCA, 0x11010B5C, 0x8F659EFF, 0xF862AE69,
    0x616BFFD3, 0x166CCF45, 0xA00AE278, 0xD70DD2EE, 0x4E048354, 0x3903B3C2,
    0xA7672661, 0xD06016F7, 0x4969474D, 0x3E6E77DB, 0xAED16A4A, 0xD9D65ADC,
    0x40DF0B66, 0x37D83BF0, 0xA9BCAE53, 0xDEBB9EC5, 0x47B2CF7F, 0x30B5FFE9,
    0xBDBDF21C, 0xCABAC28A, 0x53B39330, 0x24B4A3A6, 0xBAD03605, 0xCDD70693,
    0x54DE5729, 0x23D967BF, 0xB3667A2E, 0xC4614AB8, 0x5D681B02, 0x2A6F2B94,
    0xB40BBE37, 0xC30C8EA1, 0x5A05DF1B, 0x2D02EF8D
};

/**
 * @brief 计算CRC32校验值
 */
uint32_t calculate_crc32(const uint8_t* data, size_t length)
{
    uint32_t crc = 0xFFFFFFFF;
    
    if (data == NULL) {
        return 0;
    }
    
    for (size_t i = 0; i < length; i++) {
        uint8_t byte = data[i];
        crc = crc32_table[(crc ^ byte) & 0xFF] ^ (crc >> 8);
    }
    
    return crc ^ 0xFFFFFFFF;
}

/**
 * @brief 验证IP地址字符串格式
 */
NetConfigError_t validate_ip_format(const char* ip_str)
{
    if (ip_str == NULL) {
        return NET_CONFIG_ERR_INVALID_IP;
    }
    
    // 检查字符串长度 (最小7字符: "0.0.0.0", 最大15字符: "***************")
    size_t len = strlen(ip_str);
    if (len < 7 || len > 15) {
        return NET_CONFIG_ERR_INVALID_IP;
    }
    
    // 创建工作副本以避免修改原字符串
    char ip_copy[16];
    strncpy(ip_copy, ip_str, sizeof(ip_copy) - 1);
    ip_copy[sizeof(ip_copy) - 1] = '\0';
    
    // 检查是否以点号开头或结尾
    if (ip_copy[0] == '.' || ip_copy[len-1] == '.') {
        return NET_CONFIG_ERR_INVALID_IP;
    }
    
    // 检查是否有连续的点号
    for (size_t i = 0; i < len - 1; i++) {
        if (ip_copy[i] == '.' && ip_copy[i+1] == '.') {
            return NET_CONFIG_ERR_INVALID_IP;
        }
    }
    
    // 分割IP地址的四个部分
    char* token;
    char* saveptr;
    int octet_count = 0;
    int octets[4];
    
    token = strtok_r(ip_copy, ".", &saveptr);
    while (token != NULL && octet_count < 4) {
        // 检查每个八位组是否为纯数字
        for (int i = 0; token[i] != '\0'; i++) {
            if (!isdigit(token[i])) {
                return NET_CONFIG_ERR_INVALID_IP;
            }
        }
        
        // 检查是否有前导零 (除了单独的"0")
        if (strlen(token) > 1 && token[0] == '0') {
            return NET_CONFIG_ERR_INVALID_IP;
        }
        
        // 转换为整数并检查范围
        int octet = atoi(token);
        if (octet < 0 || octet > 255) {
            return NET_CONFIG_ERR_INVALID_IP;
        }
        
        octets[octet_count] = octet;
        octet_count++;
        token = strtok_r(NULL, ".", &saveptr);
    }
    
    // 必须恰好有4个八位组
    if (octet_count != 4) {
        return NET_CONFIG_ERR_INVALID_IP;
    }
    
    // 检查是否还有多余的字符
    if (token != NULL) {
        return NET_CONFIG_ERR_INVALID_IP;
    }
    
    return NET_CONFIG_OK;
}

/**
 * @brief 将IP地址字符串转换为字节数组
 */
NetConfigError_t parse_ip_string(const char* ip_str, uint8_t ip_bytes[4])
{
    NetConfigError_t result = validate_ip_format(ip_str);
    if (result != NET_CONFIG_OK) {
        return result;
    }
    
    if (ip_bytes == NULL) {
        return NET_CONFIG_ERR_INVALID_PARAM;
    }
    
    // 创建工作副本
    char ip_copy[16];
    strncpy(ip_copy, ip_str, sizeof(ip_copy) - 1);
    ip_copy[sizeof(ip_copy) - 1] = '\0';
    
    // 解析四个八位组
    char* token;
    char* saveptr;
    int octet_count = 0;
    
    token = strtok_r(ip_copy, ".", &saveptr);
    while (token != NULL && octet_count < 4) {
        ip_bytes[octet_count] = (uint8_t)atoi(token);
        octet_count++;
        token = strtok_r(NULL, ".", &saveptr);
    }
    
    return NET_CONFIG_OK;
}

/**
 * @brief 检查IP地址是否为特殊地址
 */
int is_special_ip_address(const uint8_t ip_bytes[4])
{
    if (ip_bytes == NULL) {
        return 1; // 空指针视为特殊地址
    }
    
    uint32_t ip = (ip_bytes[0] << 24) | (ip_bytes[1] << 16) | 
                  (ip_bytes[2] << 8) | ip_bytes[3];
    
    // 检查各种特殊地址范围
    
    // 0.0.0.0/8 - 本网络
    if ((ip & 0xFF000000) == 0x00000000) {
        return 1;
    }
    
    // *********/8 - 回环地址
    if ((ip & 0xFF000000) == 0x7F000000) {
        return 1;
    }
    
    // *********/4 - 多播地址 (224-239)
    if ((ip & 0xF0000000) == 0xE0000000) {
        return 1;
    }
    
    // 240.0.0.0/4 - 保留地址 (240-255)
    if ((ip & 0xF0000000) == 0xF0000000) {
        return 1;
    }
    
    // *************** - 广播地址
    if (ip == 0xFFFFFFFF) {
        return 1;
    }
    
    return 0; // 普通地址
}

/**
 * @brief 检查子网掩码是否有效
 */
NetConfigError_t validate_subnet_mask(const uint8_t mask_bytes[4])
{
    if (mask_bytes == NULL) {
        return NET_CONFIG_ERR_INVALID_MASK;
    }
    
    uint32_t mask = (mask_bytes[0] << 24) | (mask_bytes[1] << 16) | 
                    (mask_bytes[2] << 8) | mask_bytes[3];
    
    // 子网掩码不能为0
    if (mask == 0) {
        return NET_CONFIG_ERR_INVALID_MASK;
    }
    
    // 子网掩码不能为全1 (***************)
    if (mask == 0xFFFFFFFF) {
        return NET_CONFIG_ERR_INVALID_MASK;
    }
    
    // 检查子网掩码是否为连续的1后跟连续的0
    // 将掩码取反，然后加1，结果应该是2的幂
    uint32_t inverted = ~mask;
    uint32_t test = inverted + 1;
    
    // 检查是否为2的幂 (只有一个bit为1)
    if ((test & (test - 1)) != 0) {
        return NET_CONFIG_ERR_INVALID_MASK;
    }
    
    return NET_CONFIG_OK;
}

/**
 * @brief 验证网络参数的完整性和逻辑关系
 */
NetConfigError_t validate_network_params(const NetConfig_t* config)
{
    if (config == NULL) {
        return NET_CONFIG_ERR_INVALID_PARAM;
    }
    
    // 验证IP地址
    if (is_special_ip_address(config->ip)) {
        return NET_CONFIG_ERR_INVALID_IP;
    }
    
    // 验证子网掩码
    NetConfigError_t mask_result = validate_subnet_mask(config->subnet);
    if (mask_result != NET_CONFIG_OK) {
        return mask_result;
    }
    
    // 验证网关地址
    if (is_special_ip_address(config->gateway)) {
        return NET_CONFIG_ERR_INVALID_GW;
    }
    
    // 验证DNS服务器地址
    if (is_special_ip_address(config->dns)) {
        return NET_CONFIG_ERR_INVALID_DNS;
    }
    
    // 检查IP地址和网关是否在同一子网
    uint32_t ip = (config->ip[0] << 24) | (config->ip[1] << 16) | 
                  (config->ip[2] << 8) | config->ip[3];
    uint32_t gateway = (config->gateway[0] << 24) | (config->gateway[1] << 16) | 
                       (config->gateway[2] << 8) | config->gateway[3];
    uint32_t mask = (config->subnet[0] << 24) | (config->subnet[1] << 16) | 
                    (config->subnet[2] << 8) | config->subnet[3];
    
    uint32_t ip_network = ip & mask;
    uint32_t gw_network = gateway & mask;
    
    if (ip_network != gw_network) {
        return NET_CONFIG_ERR_INVALID_GW; // 网关不在同一子网
    }
    
    // 检查IP地址是否为网络地址或广播地址
    uint32_t broadcast = ip_network | (~mask);
    if (ip == ip_network || ip == broadcast) {
        return NET_CONFIG_ERR_INVALID_IP; // IP不能是网络地址或广播地址
    }
    
    // 检查网关是否为网络地址或广播地址
    if (gateway == ip_network || gateway == broadcast) {
        return NET_CONFIG_ERR_INVALID_GW; // 网关不能是网络地址或广播地址
    }
    
    // 检查IP地址和网关是否相同
    if (ip == gateway) {
        return NET_CONFIG_ERR_INVALID_GW; // IP地址和网关不能相同
    }
    
    return NET_CONFIG_OK;
}

/**
 * @brief 验证配置数据完整性
 */
NetConfigError_t verify_config_integrity(const NetConfigHeader_t* header, const NetConfig_t* config)
{
    if (header == NULL || config == NULL) {
        return NET_CONFIG_ERR_INVALID_PARAM;
    }
    
    // 检查魔数
    if (header->magic != NET_CONFIG_MAGIC_NUMBER) {
        return NET_CONFIG_ERR_STORAGE_CRC;
    }
    
    // 检查版本
    if (header->version != NET_CONFIG_VERSION) {
        return NET_CONFIG_ERR_STORAGE_CRC;
    }
    
    // 检查数据长度
    if (header->length != sizeof(NetConfig_t)) {
        return NET_CONFIG_ERR_STORAGE_CRC;
    }
    
    // 计算并验证CRC32
    uint32_t calculated_crc = calculate_crc32((const uint8_t*)config, sizeof(NetConfig_t));
    if (calculated_crc != header->crc32) {
        return NET_CONFIG_ERR_STORAGE_CRC;
    }
    
    return NET_CONFIG_OK;
}