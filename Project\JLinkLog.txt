T316C 000:059.488   SEGGER J-Link V7.96h Log File
T316C 000:059.598   DLL Compiled: May 15 2024 15:33:59
T316C 000:059.613   Logging started @ 2025-09-02 06:53
T316C 000:059.627   Process: D:\Keil\UV4\UV4.exe
T316C 000:059.643 - 59.642ms
T316C 000:059.661 JLINK_SetWarnOutHandler(...)
T316C 000:059.676 - 0.015ms
T316C 000:059.693 JLINK_OpenEx(...)
T316C 000:061.468   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T316C 000:062.865   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T316C 000:063.049   Decompressing FW timestamp took 110 us
T316C 000:072.922   Hardware: V9.40
T316C 000:072.971   S/N: 69400832
T316C 000:072.992   OEM: SEGGER
T316C 000:073.013   Feature(s): R<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JFlash
T316C 000:074.166   Bootloader: (Could not read)
T316C 000:075.704   TELNET listener socket opened on port 19021
T316C 000:075.800   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T316C 000:076.018   WEBSRV Webserver running on local port 19080
T316C 000:076.148   Looking for J-Link GUI Server exe at: D:\Keil\ARM\Segger\JLinkGUIServer.exe
T316C 000:076.327   Looking for J-Link GUI Server exe at: \JLinkGUIServer.exe
T316C 000:377.086   Failed to connect to J-Link GUI Server.
T316C 000:377.145 - 317.445ms returns "O.K."
T316C 000:377.168 JLINK_GetEmuCaps()
T316C 000:377.185 - 0.015ms returns 0xB9FF7BBF
T316C 000:377.203 JLINK_TIF_GetAvailable(...)
T316C 000:377.633 - 0.431ms
T316C 000:377.651 JLINK_SetErrorOutHandler(...)
T316C 000:377.665 - 0.014ms
T316C 000:377.692 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\F013_V1.52\Project\JLinkSettings.ini"", ...). 
T316C 000:401.605   Device "CORTEX-M4" selected.
T316C 000:401.944 - 24.253ms returns 0x00
T316C 000:401.982 JLINK_ExecCommand("Device = STM32F429IGTx", ...). 
T316C 000:402.499   Flash bank @ 0x90000000: SFL: Parsing sectorization info from ELF file
T316C 000:402.531     FlashDevice.SectorInfo[0]: .SectorSize = 0x00010000, .SectorStartAddr = 0x00000000
T316C 000:405.272   Device "STM32F429IG" selected.
T316C 000:405.580 - 3.581ms returns 0x00
T316C 000:405.602 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T316C 000:405.621 - 0.002ms returns 0x01
T316C 000:405.638 JLINK_GetHardwareVersion()
T316C 000:405.652 - 0.014ms returns 94000
T316C 000:405.668 JLINK_GetDLLVersion()
T316C 000:405.682 - 0.013ms returns 79608
T316C 000:405.697 JLINK_GetOEMString(...)
T316C 000:405.713 JLINK_GetFirmwareString(...)
T316C 000:405.727 - 0.014ms
T316C 000:405.771 JLINK_GetDLLVersion()
T316C 000:405.786 - 0.015ms returns 79608
T316C 000:405.800 JLINK_GetCompileDateTime()
T316C 000:405.814 - 0.013ms
T316C 000:405.833 JLINK_GetFirmwareString(...)
T316C 000:405.847 - 0.013ms
T316C 000:405.864 JLINK_GetHardwareVersion()
T316C 000:405.878 - 0.013ms returns 94000
T316C 000:405.895 JLINK_GetSN()
T316C 000:405.910 - 0.014ms returns 69400832
T316C 000:405.926 JLINK_GetOEMString(...)
T316C 000:405.947 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T316C 000:407.526 - 1.579ms returns 0x00
T316C 000:407.550 JLINK_HasError()
T316C 000:407.571 JLINK_SetSpeed(5000)
T316C 000:407.877 - 0.307ms
T316C 000:407.894 JLINK_GetId()
T316C 000:408.274   InitTarget() start
T316C 000:408.298    J-Link Script File: Executing InitTarget()
T316C 000:408.738   SWD selected. Executing JTAG -> SWD switching sequence.
T316C 000:411.535   DAP initialized successfully.
T316C 000:421.151   InitTarget() end - Took 12.8ms
T316C 000:422.074   Found SW-DP with ID 0x2BA01477
T316C 000:425.404   DPIDR: 0x2BA01477
T316C 000:425.430   CoreSight SoC-400 or earlier
T316C 000:425.455   Scanning AP map to find all available APs
T316C 000:426.362   AP[1]: Stopped AP scan as end of AP map has been reached
T316C 000:426.391   AP[0]: AHB-AP (IDR: 0x24770011)
T316C 000:426.415   Iterating through AP map to find AHB-AP to use
T316C 000:427.684   AP[0]: Core found
T316C 000:427.711   AP[0]: AHB-AP ROM base: 0xE00FF000
T316C 000:428.349   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T316C 000:428.443   Found Cortex-M4 r0p1, Little endian.
T316C 000:429.328   -- Max. mem block: 0x00010E60
T316C 000:429.994   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T316C 000:430.534   CPU_ReadMem(4 bytes @ 0x********)
T316C 000:430.967   FPUnit: 6 code (BP) slots and 2 literal slots
T316C 000:430.989   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T316C 000:431.564   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T316C 000:432.045   CPU_ReadMem(4 bytes @ 0xE0001000)
T316C 000:432.567   CPU_WriteMem(4 bytes @ 0xE0001000)
T316C 000:433.085   CPU_ReadMem(4 bytes @ 0xE000ED88)
T316C 000:433.731   CPU_WriteMem(4 bytes @ 0xE000ED88)
T316C 000:434.249   CPU_ReadMem(4 bytes @ 0xE000ED88)
T316C 000:434.748   CPU_WriteMem(4 bytes @ 0xE000ED88)
T316C 000:435.278   CoreSight components:
T316C 000:435.302   ROMTbl[0] @ E00FF000
T316C 000:435.323   CPU_ReadMem(64 bytes @ 0xE00FF000)
T316C 000:436.191   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T316C 000:436.935   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
T316C 000:436.958   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T316C 000:437.707   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
T316C 000:437.730   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T316C 000:438.423   [0][2]: ******** CID B105E00D PID 002BB003 FPB
T316C 000:438.445   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T316C 000:439.195   [0][3]: ******** CID B105E00D PID 003BB001 ITM
T316C 000:439.217   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T316C 000:439.938   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
T316C 000:439.960   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T316C 000:440.823   [0][5]: ******** CID B105900D PID 000BB925 ETM
T316C 000:441.320 - 33.425ms returns 0x2BA01477
T316C 000:441.344 JLINK_GetDLLVersion()
T316C 000:441.359 - 0.014ms returns 79608
T316C 000:441.374 JLINK_CORE_GetFound()
T316C 000:441.388 - 0.013ms returns 0xE0000FF
T316C 000:441.404 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T316C 000:441.419   Value=0xE00FF000
T316C 000:441.440 - 0.036ms returns 0
T316C 000:441.484 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T316C 000:441.499   Value=0xE00FF000
T316C 000:441.520 - 0.035ms returns 0
T316C 000:441.534 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T316C 000:441.548   Value=0x********
T316C 000:441.568 - 0.034ms returns 0
T316C 000:441.584 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T316C 000:441.606   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T316C 000:442.302   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T316C 000:442.333 - 0.749ms returns 32 (0x20)
T316C 000:442.350 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T316C 000:442.364   Value=0x00000000
T316C 000:442.384 - 0.035ms returns 0
T316C 000:442.399 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T316C 000:442.413   Value=0x********
T316C 000:442.434 - 0.035ms returns 0
T316C 000:442.449 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T316C 000:442.463   Value=0x********
T316C 000:442.483 - 0.034ms returns 0
T316C 000:442.498 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T316C 000:442.511   Value=0xE0001000
T316C 000:442.532 - 0.034ms returns 0
T316C 000:442.546 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T316C 000:442.559   Value=0x********
T316C 000:442.580 - 0.034ms returns 0
T316C 000:442.595 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T316C 000:442.608   Value=0xE000E000
T316C 000:442.629 - 0.034ms returns 0
T316C 000:442.643 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T316C 000:442.657   Value=0xE000EDF0
T316C 000:442.677 - 0.034ms returns 0
T316C 000:442.692 JLINK_GetDebugInfo(0x01 = Unknown)
T316C 000:442.706   Value=0x00000001
T316C 000:442.726 - 0.034ms returns 0
T316C 000:442.741 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T316C 000:442.759   CPU_ReadMem(4 bytes @ 0xE000ED00)
T316C 000:443.295   Data:  41 C2 0F 41
T316C 000:443.333   Debug reg: CPUID
T316C 000:443.353 - 0.612ms returns 1 (0x1)
T316C 000:443.369 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T316C 000:443.383   Value=0x00000000
T316C 000:443.406 - 0.037ms returns 0
T316C 000:443.420 JLINK_HasError()
T316C 000:443.436 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T316C 000:443.450 - 0.014ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T316C 000:443.465 JLINK_Reset()
T316C 000:443.482   CPU is running
T316C 000:443.504   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T316C 000:444.005   CPU is running
T316C 000:444.040   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T316C 000:444.577   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T316C 000:445.375   Reset: Reset device via AIRCR.SYSRESETREQ.
T316C 000:445.420   CPU is running
T316C 000:445.444   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T316C 000:498.528   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T316C 000:499.043   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T316C 000:502.559   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T316C 000:508.496   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T316C 000:512.128   CPU_WriteMem(4 bytes @ 0x********)
T316C 000:512.775   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T316C 000:513.305   CPU_ReadMem(4 bytes @ 0xE0001000)
T316C 000:513.813 - 70.347ms
T316C 000:513.838 JLINK_HasError()
T316C 000:513.886 JLINK_ReadReg(R15 (PC))
T316C 000:513.904 - 0.019ms returns 0x080001C0
T316C 000:513.919 JLINK_ReadReg(XPSR)
T316C 000:513.934 - 0.014ms returns 0x01000000
T316C 000:513.949 JLINK_Halt()
T316C 000:513.963 - 0.013ms returns 0x00
T316C 000:513.978 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T316C 000:513.996   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T316C 000:514.538   Data:  03 00 03 00
T316C 000:514.560   Debug reg: DHCSR
T316C 000:514.581 - 0.602ms returns 1 (0x1)
T316C 000:514.596 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T316C 000:514.610   Debug reg: DHCSR
T316C 000:514.842   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T316C 000:515.437 - 0.840ms returns 0 (0x00000000)
T316C 000:515.456 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T316C 000:515.470   Debug reg: DEMCR
T316C 000:515.492   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T316C 000:516.034 - 0.578ms returns 0 (0x00000000)
T316C 000:516.084 JLINK_GetHWStatus(...)
T316C 000:516.457 - 0.373ms returns 0
T316C 000:516.483 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T316C 000:516.497 - 0.014ms returns 0x06
T316C 000:516.512 JLINK_GetNumBPUnits(Type = 0xF0)
T316C 000:516.525 - 0.013ms returns 0x2000
T316C 000:516.541 JLINK_GetNumWPUnits()
T316C 000:516.555 - 0.014ms returns 4
T316C 000:516.576 JLINK_GetSpeed()
T316C 000:516.590 - 0.013ms returns 4000
T316C 000:516.608 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T316C 000:516.624   CPU_ReadMem(4 bytes @ 0xE000E004)
T316C 000:517.142   Data:  02 00 00 00
T316C 000:517.164 - 0.556ms returns 1 (0x1)
T316C 000:517.179 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T316C 000:517.194   CPU_ReadMem(4 bytes @ 0xE000E004)
T316C 000:517.745   Data:  02 00 00 00
T316C 000:517.766 - 0.586ms returns 1 (0x1)
T316C 000:517.782 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T316C 000:517.796   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T316C 000:517.819   CPU_WriteMem(28 bytes @ 0xE0001000)
T316C 000:518.618 - 0.836ms returns 0x1C
T316C 000:518.634 JLINK_HasError()
T316C 000:518.649 JLINK_ReadReg(R15 (PC))
T316C 000:518.664 - 0.014ms returns 0x080001C0
T316C 000:518.678 JLINK_ReadReg(XPSR)
T316C 000:518.692 - 0.013ms returns 0x01000000
T316C 000:523.227 JLINK_ReadMemEx(0xE0001004, 0x4 Bytes, Flags = 0x02000000)
T316C 000:523.259   Data:  00 00 00 00
T316C 000:523.280   Debug reg: DWT_CYCCNT
T316C 000:523.301 - 0.075ms returns 4 (0x4)
T316C 000:637.770 JLINK_HasError()
T316C 000:637.821 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T316C 000:637.837 - 0.015ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T316C 000:637.852 JLINK_Reset()
T316C 000:637.876   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T316C 000:638.496   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T316C 000:639.135   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T316C 000:640.068   Reset: Reset device via AIRCR.SYSRESETREQ.
T316C 000:640.091   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T316C 000:694.256   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T316C 000:694.785   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T316C 000:695.302   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T316C 000:701.650   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T316C 000:705.457   CPU_WriteMem(4 bytes @ 0x********)
T316C 000:706.207   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T316C 000:706.767   CPU_ReadMem(4 bytes @ 0xE0001000)
T316C 000:707.301 - 69.449ms
T316C 000:707.394 JLINK_HasError()
T316C 000:707.411 JLINK_ReadReg(R15 (PC))
T316C 000:707.427 - 0.016ms returns 0x080001C0
T316C 000:707.441 JLINK_ReadReg(XPSR)
T316C 000:707.456 - 0.014ms returns 0x01000000
T316C 000:707.580 JLINK_ReadMemEx(0x080001C0, 0x3C Bytes, Flags = 0x02000000)
T316C 000:707.638   CPU_ReadMem(64 bytes @ 0x080001C0)
T316C 000:708.600    -- Updating C cache (64 bytes @ 0x080001C0)
T316C 000:708.636    -- Read from C cache (60 bytes @ 0x080001C0)
T316C 000:708.658   Data:  06 48 80 47 06 48 00 47 FE E7 FE E7 FE E7 FE E7 ...
T316C 000:708.680 - 1.100ms returns 60 (0x3C)
T316C 000:708.697 JLINK_ReadMemEx(0x080001C0, 0x2 Bytes, Flags = 0x02000000)
T316C 000:708.713    -- Read from C cache (2 bytes @ 0x080001C0)
T316C 000:708.734   Data:  06 48
T316C 000:708.755 - 0.058ms returns 2 (0x2)
T316C 000:708.823 JLINK_ReadMemEx(0x080001C2, 0x2 Bytes, Flags = 0x02000000)
T316C 000:708.839    -- Read from C cache (2 bytes @ 0x080001C2)
T316C 000:708.859   Data:  80 47
T316C 000:708.880 - 0.056ms returns 2 (0x2)
T316C 001:659.005 JLINK_ReadMemEx(0x080001C2, 0x2 Bytes, Flags = 0x02000000)
T316C 001:659.060    -- Read from C cache (2 bytes @ 0x080001C2)
T316C 001:659.082   Data:  80 47
T316C 001:659.103 - 0.099ms returns 2 (0x2)
T316C 001:659.119 JLINK_ReadMemEx(0x080001C4, 0x3C Bytes, Flags = 0x02000000)
T316C 001:659.134    -- Read from C cache (60 bytes @ 0x080001C4)
T316C 001:659.155   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T316C 001:659.176 - 0.057ms returns 60 (0x3C)
T316C 001:659.191 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T316C 001:659.206    -- Read from C cache (2 bytes @ 0x080001C4)
T316C 001:659.227   Data:  06 48
T316C 001:659.247 - 0.056ms returns 2 (0x2)
T316C 001:659.274 JLINK_ReadMemEx(0x080001C4, 0x3C Bytes, Flags = 0x02000000)
T316C 001:659.289    -- Read from C cache (60 bytes @ 0x080001C4)
T316C 001:659.310   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T316C 001:659.331 - 0.057ms returns 60 (0x3C)
T316C 001:659.346 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T316C 001:659.360    -- Read from C cache (2 bytes @ 0x080001C4)
T316C 001:659.381   Data:  06 48
T316C 001:659.402 - 0.056ms returns 2 (0x2)
T316C 001:659.417 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T316C 001:659.431    -- Read from C cache (2 bytes @ 0x080001C6)
T316C 001:659.452   Data:  00 47
T316C 001:659.472 - 0.055ms returns 2 (0x2)
T316C 001:880.526 JLINK_HasError()
T316C 001:880.574 JLINK_ReadReg(R0)
T316C 001:881.142 - 0.566ms returns 0x00000000
T316C 001:881.206 JLINK_ReadReg(R1)
T316C 001:881.221 - 0.015ms returns 0x00000000
T316C 001:881.235 JLINK_ReadReg(R2)
T316C 001:881.250 - 0.014ms returns 0x00000000
T316C 001:881.264 JLINK_ReadReg(R3)
T316C 001:881.278 - 0.014ms returns 0x00000000
T316C 001:881.293 JLINK_ReadReg(R4)
T316C 001:881.307 - 0.013ms returns 0x00000000
T316C 001:881.321 JLINK_ReadReg(R5)
T316C 001:881.335 - 0.013ms returns 0x00000000
T316C 001:881.350 JLINK_ReadReg(R6)
T316C 001:881.364 - 0.013ms returns 0x00000000
T316C 001:881.378 JLINK_ReadReg(R7)
T316C 001:881.392 - 0.014ms returns 0x00000000
T316C 001:881.407 JLINK_ReadReg(R8)
T316C 001:881.422 - 0.014ms returns 0x00000000
T316C 001:881.436 JLINK_ReadReg(R9)
T316C 001:881.450 - 0.014ms returns 0x00000000
T316C 001:881.464 JLINK_ReadReg(R10)
T316C 001:881.479 - 0.014ms returns 0x00000000
T316C 001:881.493 JLINK_ReadReg(R11)
T316C 001:881.508 - 0.014ms returns 0x00000000
T316C 001:881.523 JLINK_ReadReg(R12)
T316C 001:881.537 - 0.014ms returns 0x00000000
T316C 001:881.552 JLINK_ReadReg(R13 (SP))
T316C 001:881.566 - 0.014ms returns 0x20001940
T316C 001:881.628 JLINK_ReadReg(R14)
T316C 001:881.644 - 0.016ms returns 0xFFFFFFFF
T316C 001:881.660 JLINK_ReadReg(R15 (PC))
T316C 001:881.674 - 0.014ms returns 0x080001C0
T316C 001:881.689 JLINK_ReadReg(XPSR)
T316C 001:881.703 - 0.014ms returns 0x01000000
T316C 001:881.718 JLINK_ReadReg(MSP)
T316C 001:881.732 - 0.014ms returns 0x20001940
T316C 001:881.747 JLINK_ReadReg(PSP)
T316C 001:881.761 - 0.014ms returns 0x00000000
T316C 001:881.776 JLINK_ReadReg(CFBP)
T316C 001:881.790 - 0.014ms returns 0x00000000
T316C 001:881.804 JLINK_ReadReg(FPSCR)
T316C 001:886.264 - 4.459ms returns 0x00000000
T316C 001:886.283 JLINK_ReadReg(FPS0)
T316C 001:886.298 - 0.014ms returns 0x00000000
T316C 001:886.313 JLINK_ReadReg(FPS1)
T316C 001:886.327 - 0.014ms returns 0x00000000
T316C 001:886.341 JLINK_ReadReg(FPS2)
T316C 001:886.356 - 0.014ms returns 0x00000000
T316C 001:886.370 JLINK_ReadReg(FPS3)
T316C 001:886.384 - 0.014ms returns 0x00000000
T316C 001:886.399 JLINK_ReadReg(FPS4)
T316C 001:886.413 - 0.014ms returns 0x00000000
T316C 001:886.428 JLINK_ReadReg(FPS5)
T316C 001:886.442 - 0.014ms returns 0x00000000
T316C 001:886.456 JLINK_ReadReg(FPS6)
T316C 001:886.470 - 0.014ms returns 0x00000000
T316C 001:886.484 JLINK_ReadReg(FPS7)
T316C 001:886.499 - 0.014ms returns 0x00000000
T316C 001:886.513 JLINK_ReadReg(FPS8)
T316C 001:886.527 - 0.014ms returns 0x00000000
T316C 001:886.542 JLINK_ReadReg(FPS9)
T316C 001:886.556 - 0.014ms returns 0x00000000
T316C 001:886.570 JLINK_ReadReg(FPS10)
T316C 001:886.585 - 0.014ms returns 0x00000000
T316C 001:886.599 JLINK_ReadReg(FPS11)
T316C 001:886.613 - 0.014ms returns 0x00000000
T316C 001:886.628 JLINK_ReadReg(FPS12)
T316C 001:886.642 - 0.014ms returns 0x00000000
T316C 001:886.656 JLINK_ReadReg(FPS13)
T316C 001:886.670 - 0.014ms returns 0x00000000
T316C 001:886.685 JLINK_ReadReg(FPS14)
T316C 001:886.699 - 0.014ms returns 0x00000000
T316C 001:886.714 JLINK_ReadReg(FPS15)
T316C 001:886.728 - 0.013ms returns 0x00000000
T316C 001:886.742 JLINK_ReadReg(FPS16)
T316C 001:886.756 - 0.013ms returns 0x00000000
T316C 001:886.770 JLINK_ReadReg(FPS17)
T316C 001:886.784 - 0.013ms returns 0x00000000
T316C 001:886.799 JLINK_ReadReg(FPS18)
T316C 001:886.813 - 0.013ms returns 0x00000000
T316C 001:886.827 JLINK_ReadReg(FPS19)
T316C 001:886.841 - 0.013ms returns 0x00000000
T316C 001:886.856 JLINK_ReadReg(FPS20)
T316C 001:886.870 - 0.014ms returns 0x00000000
T316C 001:886.884 JLINK_ReadReg(FPS21)
T316C 001:886.898 - 0.013ms returns 0x00000000
T316C 001:886.913 JLINK_ReadReg(FPS22)
T316C 001:886.927 - 0.014ms returns 0x00000000
T316C 001:886.941 JLINK_ReadReg(FPS23)
T316C 001:886.955 - 0.014ms returns 0x00000000
T316C 001:886.970 JLINK_ReadReg(FPS24)
T316C 001:886.984 - 0.014ms returns 0x00000000
T316C 001:886.999 JLINK_ReadReg(FPS25)
T316C 001:887.013 - 0.014ms returns 0x00000000
T316C 001:887.027 JLINK_ReadReg(FPS26)
T316C 001:887.042 - 0.014ms returns 0x00000000
T316C 001:887.056 JLINK_ReadReg(FPS27)
T316C 001:887.070 - 0.014ms returns 0x00000000
T316C 001:887.085 JLINK_ReadReg(FPS28)
T316C 001:887.099 - 0.013ms returns 0x00000000
T316C 001:887.113 JLINK_ReadReg(FPS29)
T316C 001:887.127 - 0.014ms returns 0x00000000
T316C 001:887.142 JLINK_ReadReg(FPS30)
T316C 001:887.156 - 0.014ms returns 0x00000000
T316C 001:887.170 JLINK_ReadReg(FPS31)
T316C 001:887.185 - 0.014ms returns 0x00000000
T316C 001:887.378 JLINK_ReadMemEx(0x200133E4, 0x4 Bytes, Flags = 0x02000000)
T316C 001:887.401   CPU_ReadMem(64 bytes @ 0x200133C0)
T316C 001:888.352    -- Updating C cache (64 bytes @ 0x200133C0)
T316C 001:888.375    -- Read from C cache (4 bytes @ 0x200133E4)
T316C 001:888.396   Data:  FF FF FF FF
T316C 001:888.417 - 1.039ms returns 4 (0x4)
T316C 001:888.517 JLINK_ReadMemEx(0x200133E4, 0x4 Bytes, Flags = 0x02000000)
T316C 001:888.533    -- Read from C cache (4 bytes @ 0x200133E4)
T316C 001:888.554   Data:  FF FF FF FF
T316C 001:888.574 - 0.057ms returns 4 (0x4)
T316C 001:888.600 JLINK_ReadMemEx(0x200133E4, 0x4 Bytes, Flags = 0x02000000)
T316C 001:888.620    -- Read from C cache (4 bytes @ 0x200133E4)
T316C 001:888.642   Data:  FF FF FF FF
T316C 001:888.665 - 0.065ms returns 4 (0x4)
T316C 001:910.619 JLINK_ReadMemEx(0x20012BD4, 0x1 Bytes, Flags = 0x02000000)
T316C 001:910.667   CPU_ReadMem(64 bytes @ 0x20012BC0)
T316C 001:911.600    -- Updating C cache (64 bytes @ 0x20012BC0)
T316C 001:911.632    -- Read from C cache (1 bytes @ 0x20012BD4)
T316C 001:911.654   Data:  00
T316C 001:911.677 - 1.058ms returns 1 (0x1)
T316C 001:911.742 JLINK_ReadMemEx(0x20012BD4, 0x1 Bytes, Flags = 0x02000000)
T316C 001:911.763    -- Read from C cache (1 bytes @ 0x20012BD4)
T316C 001:911.784   Data:  00
T316C 001:911.805 - 0.063ms returns 1 (0x1)
T316C 001:911.836 JLINK_ReadMemEx(0x20012BD4, 0x1 Bytes, Flags = 0x02000000)
T316C 001:911.852    -- Read from C cache (1 bytes @ 0x20012BD4)
T316C 001:911.873   Data:  00
T316C 001:911.894 - 0.058ms returns 1 (0x1)
T6068 001:985.668 JLINK_ReadMemEx(0x080001C0, 0x2 Bytes, Flags = 0x02000000)
T6068 001:985.718    -- Read from C cache (2 bytes @ 0x080001C0)
T6068 001:985.740   Data:  06 48
T6068 001:985.765 - 0.097ms returns 2 (0x2)
T6068 001:985.780 JLINK_HasError()
T6068 001:985.797 JLINK_SetBPEx(Addr = 0x0803BB68, Type = 0xFFFFFFF2)
T6068 001:985.815 - 0.017ms returns 0x00000001
T6068 001:985.829 JLINK_HasError()
T6068 001:985.844 JLINK_HasError()
T6068 001:985.859 JLINK_Go()
T6068 001:985.879   CPU_WriteMem(4 bytes @ 0x********)
T6068 001:986.454   CPU_ReadMem(4 bytes @ 0xE0001000)
T6068 001:986.917   CPU_WriteMem(4 bytes @ 0xE0002008)
T6068 001:986.943   CPU_WriteMem(4 bytes @ 0xE000200C)
T6068 001:986.964   CPU_WriteMem(4 bytes @ 0xE0002010)
T6068 001:986.985   CPU_WriteMem(4 bytes @ 0xE0002014)
T6068 001:987.006   CPU_WriteMem(4 bytes @ 0xE0002018)
T6068 001:987.026   CPU_WriteMem(4 bytes @ 0xE000201C)
T6068 001:988.575   CPU_WriteMem(4 bytes @ 0xE0001004)
T6068 001:989.801   Memory map 'after startup completion point' is active
T6068 001:989.845 - 3.985ms
T6068 002:090.980 JLINK_HasError()
T6068 002:091.102 JLINK_IsHalted()
T6068 002:094.467 - 3.364ms returns TRUE
T6068 002:094.502 JLINK_HasError()
T6068 002:094.518 JLINK_Halt()
T6068 002:094.532 - 0.014ms returns 0x00
T6068 002:094.547 JLINK_IsHalted()
T6068 002:094.562 - 0.014ms returns TRUE
T6068 002:094.576 JLINK_IsHalted()
T6068 002:094.590 - 0.014ms returns TRUE
T6068 002:094.605 JLINK_IsHalted()
T6068 002:094.619 - 0.013ms returns TRUE
T6068 002:094.634 JLINK_HasError()
T6068 002:094.649 JLINK_ReadReg(R15 (PC))
T6068 002:094.665 - 0.016ms returns 0x0803BB68
T6068 002:094.680 JLINK_ReadReg(XPSR)
T6068 002:094.694 - 0.014ms returns 0x61000000
T6068 002:094.712 JLINK_HasError()
T6068 002:094.727 JLINK_ClrBPEx(BPHandle = 0x00000001)
T6068 002:094.742 - 0.014ms returns 0x00
T6068 002:094.756 JLINK_HasError()
T6068 002:094.771 JLINK_HasError()
T6068 002:094.786 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T6068 002:094.806   CPU_ReadMem(4 bytes @ 0xE000ED30)
T6068 002:095.282   Data:  02 00 00 00
T6068 002:095.305 - 0.518ms returns 1 (0x1)
T6068 002:095.320 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T6068 002:095.336   CPU_ReadMem(4 bytes @ 0xE0001028)
T6068 002:095.875   Data:  00 00 00 00
T6068 002:095.897   Debug reg: DWT_FUNC[0]
T6068 002:095.918 - 0.597ms returns 1 (0x1)
T6068 002:095.933 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T6068 002:095.949   CPU_ReadMem(4 bytes @ 0xE0001038)
T6068 002:096.522   Data:  00 02 00 00
T6068 002:096.544   Debug reg: DWT_FUNC[1]
T6068 002:096.565 - 0.631ms returns 1 (0x1)
T6068 002:096.580 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T6068 002:096.595   CPU_ReadMem(4 bytes @ 0xE0001048)
T6068 002:097.121   Data:  00 00 00 00
T6068 002:097.142   Debug reg: DWT_FUNC[2]
T6068 002:097.163 - 0.583ms returns 1 (0x1)
T6068 002:097.179 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T6068 002:097.194   CPU_ReadMem(4 bytes @ 0xE0001058)
T6068 002:097.707   Data:  00 00 00 00
T6068 002:097.729   Debug reg: DWT_FUNC[3]
T6068 002:097.750 - 0.571ms returns 1 (0x1)
T6068 002:097.839 JLINK_HasError()
T6068 002:097.858 JLINK_ReadReg(R0)
T6068 002:097.875 - 0.017ms returns 0x0803BB69
T6068 002:097.890 JLINK_ReadReg(R1)
T6068 002:097.904 - 0.014ms returns 0x20015AB0
T6068 002:097.919 JLINK_ReadReg(R2)
T6068 002:097.933 - 0.014ms returns 0x00000000
T6068 002:097.948 JLINK_ReadReg(R3)
T6068 002:097.962 - 0.014ms returns 0x0803E489
T6068 002:097.976 JLINK_ReadReg(R4)
T6068 002:097.990 - 0.014ms returns 0x08040534
T6068 002:098.005 JLINK_ReadReg(R5)
T6068 002:098.019 - 0.014ms returns 0x08040534
T6068 002:098.034 JLINK_ReadReg(R6)
T6068 002:098.048 - 0.014ms returns 0x00000000
T6068 002:098.063 JLINK_ReadReg(R7)
T6068 002:098.077 - 0.014ms returns 0x00000000
T6068 002:098.094 JLINK_ReadReg(R8)
T6068 002:098.109 - 0.016ms returns 0x00000000
T6068 002:098.123 JLINK_ReadReg(R9)
T6068 002:098.138 - 0.014ms returns 0x00000000
T6068 002:098.152 JLINK_ReadReg(R10)
T6068 002:098.167 - 0.014ms returns 0x00000000
T6068 002:098.181 JLINK_ReadReg(R11)
T6068 002:098.196 - 0.014ms returns 0x00000000
T6068 002:098.210 JLINK_ReadReg(R12)
T6068 002:098.224 - 0.014ms returns 0x00000150
T6068 002:098.239 JLINK_ReadReg(R13 (SP))
T6068 002:098.253 - 0.014ms returns 0x20015AA0
T6068 002:098.267 JLINK_ReadReg(R14)
T6068 002:098.281 - 0.014ms returns 0x080109E3
T6068 002:098.296 JLINK_ReadReg(R15 (PC))
T6068 002:098.310 - 0.014ms returns 0x0803BB68
T6068 002:098.325 JLINK_ReadReg(XPSR)
T6068 002:098.339 - 0.014ms returns 0x61000000
T6068 002:098.354 JLINK_ReadReg(MSP)
T6068 002:098.368 - 0.013ms returns 0x20015AA0
T6068 002:098.382 JLINK_ReadReg(PSP)
T6068 002:098.396 - 0.013ms returns 0x00000000
T6068 002:098.411 JLINK_ReadReg(CFBP)
T6068 002:098.425 - 0.014ms returns 0x00000000
T6068 002:098.440 JLINK_ReadReg(FPSCR)
T6068 002:102.720 - 4.279ms returns 0x00000000
T6068 002:102.738 JLINK_ReadReg(FPS0)
T6068 002:102.752 - 0.014ms returns 0x00000000
T6068 002:102.767 JLINK_ReadReg(FPS1)
T6068 002:102.781 - 0.014ms returns 0x00000000
T6068 002:102.796 JLINK_ReadReg(FPS2)
T6068 002:102.810 - 0.014ms returns 0x00000000
T6068 002:102.825 JLINK_ReadReg(FPS3)
T6068 002:102.839 - 0.014ms returns 0x00000000
T6068 002:102.854 JLINK_ReadReg(FPS4)
T6068 002:102.868 - 0.014ms returns 0x00000000
T6068 002:102.882 JLINK_ReadReg(FPS5)
T6068 002:102.897 - 0.014ms returns 0x00000000
T6068 002:102.911 JLINK_ReadReg(FPS6)
T6068 002:102.926 - 0.014ms returns 0x00000000
T6068 002:102.940 JLINK_ReadReg(FPS7)
T6068 002:102.954 - 0.014ms returns 0x00000000
T6068 002:102.969 JLINK_ReadReg(FPS8)
T6068 002:102.983 - 0.014ms returns 0x00000000
T6068 002:102.998 JLINK_ReadReg(FPS9)
T6068 002:103.012 - 0.014ms returns 0x00000000
T6068 002:103.026 JLINK_ReadReg(FPS10)
T6068 002:103.040 - 0.013ms returns 0x00000000
T6068 002:103.055 JLINK_ReadReg(FPS11)
T6068 002:103.069 - 0.014ms returns 0x00000000
T6068 002:103.088 JLINK_ReadReg(FPS12)
T6068 002:103.102 - 0.014ms returns 0x00000000
T6068 002:103.116 JLINK_ReadReg(FPS13)
T6068 002:103.130 - 0.013ms returns 0x00000000
T6068 002:103.145 JLINK_ReadReg(FPS14)
T6068 002:103.159 - 0.014ms returns 0x00000000
T6068 002:103.174 JLINK_ReadReg(FPS15)
T6068 002:103.188 - 0.014ms returns 0x00000000
T6068 002:103.203 JLINK_ReadReg(FPS16)
T6068 002:103.217 - 0.014ms returns 0x00000000
T6068 002:103.231 JLINK_ReadReg(FPS17)
T6068 002:103.245 - 0.014ms returns 0x00000000
T6068 002:103.260 JLINK_ReadReg(FPS18)
T6068 002:103.274 - 0.014ms returns 0x00000000
T6068 002:103.289 JLINK_ReadReg(FPS19)
T6068 002:103.303 - 0.013ms returns 0x00000000
T6068 002:103.317 JLINK_ReadReg(FPS20)
T6068 002:103.331 - 0.014ms returns 0x00000000
T6068 002:103.346 JLINK_ReadReg(FPS21)
T6068 002:103.362 - 0.016ms returns 0x00000000
T6068 002:103.377 JLINK_ReadReg(FPS22)
T6068 002:103.391 - 0.014ms returns 0x00000000
T6068 002:103.408 JLINK_ReadReg(FPS23)
T6068 002:103.422 - 0.014ms returns 0x00000000
T6068 002:103.437 JLINK_ReadReg(FPS24)
T6068 002:103.451 - 0.014ms returns 0x00000000
T6068 002:103.465 JLINK_ReadReg(FPS25)
T6068 002:103.480 - 0.014ms returns 0x00000000
T6068 002:103.495 JLINK_ReadReg(FPS26)
T6068 002:103.510 - 0.014ms returns 0x00000000
T6068 002:103.525 JLINK_ReadReg(FPS27)
T6068 002:103.539 - 0.014ms returns 0x00000000
T6068 002:103.553 JLINK_ReadReg(FPS28)
T6068 002:103.567 - 0.014ms returns 0x00000000
T6068 002:103.582 JLINK_ReadReg(FPS29)
T6068 002:103.596 - 0.014ms returns 0x00000000
T6068 002:103.610 JLINK_ReadReg(FPS30)
T6068 002:103.624 - 0.013ms returns 0x00000000
T6068 002:103.639 JLINK_ReadReg(FPS31)
T6068 002:103.653 - 0.014ms returns 0x00000000
T316C 002:103.733 JLINK_HasError()
T316C 002:103.759 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T316C 002:103.778   CPU_ReadMem(4 bytes @ 0xE0001004)
T316C 002:104.261   Data:  46 11 A9 00
T316C 002:104.297   Debug reg: DWT_CYCCNT
T316C 002:104.327 - 0.568ms returns 1 (0x1)
T6068 005:272.457 JLINK_ReadMemEx(0x0803BB68, 0x2 Bytes, Flags = 0x02000000)
T6068 005:272.541   CPU_ReadMem(64 bytes @ 0x0803BB40)
T6068 005:273.557    -- Updating C cache (64 bytes @ 0x0803BB40)
T6068 005:273.597    -- Read from C cache (2 bytes @ 0x0803BB68)
T6068 005:273.619   Data:  2D E9
T6068 005:273.641 - 1.184ms returns 2 (0x2)
T6068 005:273.663 JLINK_HasError()
T6068 005:273.687 JLINK_HasError()
T6068 005:273.703 JLINK_Go()
T6068 005:274.230   CPU_ReadMem(4 bytes @ 0xE0001000)
T6068 005:274.803   CPU_WriteMem(4 bytes @ 0xE0002008)
T6068 005:276.029 - 2.325ms
T6068 005:376.173 JLINK_HasError()
T6068 005:376.225 JLINK_IsHalted()
T6068 005:376.750 - 0.525ms returns FALSE
T6068 005:477.697 JLINK_HasError()
T6068 005:477.754 JLINK_IsHalted()
T6068 005:478.619 - 0.864ms returns FALSE
T6068 005:578.760 JLINK_HasError()
T6068 005:578.815 JLINK_IsHalted()
T6068 005:579.706 - 0.888ms returns FALSE
T6068 005:680.026 JLINK_HasError()
T6068 005:680.149 JLINK_IsHalted()
T6068 005:681.139 - 0.987ms returns FALSE
T6068 005:782.335 JLINK_HasError()
T6068 005:782.455 JLINK_IsHalted()
T6068 005:783.328 - 0.870ms returns FALSE
T6068 005:883.711 JLINK_HasError()
T6068 005:883.760 JLINK_HasError()
T6068 005:883.776 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 005:883.802   CPU_ReadMem(4 bytes @ 0xE0001004)
T6068 005:884.352   Data:  47 A7 C0 06
T6068 005:884.376   Debug reg: DWT_CYCCNT
T6068 005:884.397 - 0.621ms returns 1 (0x1)
T6068 005:884.489 JLINK_IsHalted()
T6068 005:885.157 - 0.667ms returns FALSE
T6068 005:985.276 JLINK_HasError()
T6068 005:985.324 JLINK_IsHalted()
T6068 005:986.039 - 0.714ms returns FALSE
T6068 006:086.253 JLINK_HasError()
T6068 006:086.302 JLINK_IsHalted()
T6068 006:086.861 - 0.558ms returns FALSE
T6068 006:187.641 JLINK_HasError()
T6068 006:187.790 JLINK_IsHalted()
T6068 006:188.673 - 0.880ms returns FALSE
T6068 006:289.179 JLINK_HasError()
T6068 006:289.302 JLINK_IsHalted()
T6068 006:290.250 - 0.946ms returns FALSE
T6068 006:390.786 JLINK_HasError()
T6068 006:390.903 JLINK_IsHalted()
T6068 006:391.923 - 1.018ms returns FALSE
T6068 006:492.169 JLINK_HasError()
T6068 006:492.283 JLINK_HasError()
T6068 006:492.333 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 006:492.399   Data:  47 A7 C0 06
T6068 006:492.470   Debug reg: DWT_CYCCNT
T6068 006:492.540 - 0.206ms returns 1 (0x1)
T6068 006:492.893 JLINK_IsHalted()
T6068 006:493.912 - 1.018ms returns FALSE
T6068 006:595.108 JLINK_HasError()
T6068 006:595.240 JLINK_IsHalted()
T6068 006:596.122 - 0.881ms returns FALSE
T6068 006:696.339 JLINK_HasError()
T6068 006:696.388 JLINK_IsHalted()
T6068 006:696.983 - 0.594ms returns FALSE
T6068 006:797.096 JLINK_HasError()
T6068 006:797.147 JLINK_IsHalted()
T6068 006:797.731 - 0.583ms returns FALSE
T6068 006:897.767 JLINK_HasError()
T6068 006:897.817 JLINK_IsHalted()
T6068 006:898.779 - 0.960ms returns FALSE
T6068 006:999.696 JLINK_HasError()
T6068 006:999.815 JLINK_IsHalted()
T6068 007:000.698 - 0.880ms returns FALSE
T6068 007:101.016 JLINK_HasError()
T6068 007:101.143 JLINK_HasError()
T6068 007:101.199 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 007:101.271   Data:  47 A7 C0 06
T6068 007:101.347   Debug reg: DWT_CYCCNT
T6068 007:101.423 - 0.223ms returns 1 (0x1)
T6068 007:101.922 JLINK_IsHalted()
T6068 007:102.942 - 1.018ms returns FALSE
T6068 007:203.318 JLINK_HasError()
T6068 007:203.375 JLINK_IsHalted()
T6068 007:204.009 - 0.633ms returns FALSE
T6068 007:304.979 JLINK_HasError()
T6068 007:305.056 JLINK_IsHalted()
T6068 007:305.625 - 0.569ms returns FALSE
T6068 007:406.549 JLINK_HasError()
T6068 007:406.673 JLINK_IsHalted()
T6068 007:407.651 - 0.976ms returns FALSE
T6068 007:508.681 JLINK_HasError()
T6068 007:508.793 JLINK_IsHalted()
T6068 007:509.781 - 0.985ms returns FALSE
T6068 007:610.761 JLINK_HasError()
T6068 007:610.872 JLINK_HasError()
T6068 007:610.924 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 007:610.989   Data:  47 A7 C0 06
T6068 007:611.061   Debug reg: DWT_CYCCNT
T6068 007:611.130 - 0.206ms returns 1 (0x1)
T6068 007:611.510 JLINK_IsHalted()
T6068 007:612.582 - 1.069ms returns FALSE
T6068 007:713.581 JLINK_HasError()
T6068 007:713.629 JLINK_IsHalted()
T6068 007:714.333 - 0.703ms returns FALSE
T6068 007:814.814 JLINK_HasError()
T6068 007:814.869 JLINK_IsHalted()
T6068 007:815.453 - 0.582ms returns FALSE
T6068 007:915.561 JLINK_HasError()
T6068 007:915.613 JLINK_IsHalted()
T6068 007:916.507 - 0.893ms returns FALSE
T6068 008:016.817 JLINK_HasError()
T6068 008:016.968 JLINK_IsHalted()
T6068 008:017.953 - 0.984ms returns FALSE
T6068 008:118.236 JLINK_HasError()
T6068 008:118.291 JLINK_IsHalted()
T6068 008:119.128 - 0.835ms returns FALSE
T6068 008:219.390 JLINK_HasError()
T6068 008:219.517 JLINK_HasError()
T6068 008:219.574 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 008:219.648   Data:  47 A7 C0 06
T6068 008:219.725   Debug reg: DWT_CYCCNT
T6068 008:219.804 - 0.229ms returns 1 (0x1)
T6068 008:220.169 JLINK_IsHalted()
T6068 008:221.059 - 0.888ms returns FALSE
T6068 008:321.343 JLINK_HasError()
T6068 008:321.466 JLINK_IsHalted()
T6068 008:322.595 - 1.126ms returns FALSE
T6068 008:422.917 JLINK_HasError()
T6068 008:423.035 JLINK_IsHalted()
T6068 008:423.786 - 0.750ms returns FALSE
T6068 008:524.446 JLINK_HasError()
T6068 008:524.496 JLINK_IsHalted()
T6068 008:525.032 - 0.535ms returns FALSE
T6068 008:625.685 JLINK_HasError()
T6068 008:625.738 JLINK_IsHalted()
T6068 008:626.419 - 0.681ms returns FALSE
T6068 008:727.201 JLINK_HasError()
T6068 008:727.299 JLINK_HasError()
T6068 008:727.346 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 008:727.406   Data:  47 A7 C0 06
T6068 008:727.472   Debug reg: DWT_CYCCNT
T6068 008:727.537 - 0.190ms returns 1 (0x1)
T6068 008:727.748 JLINK_IsHalted()
T6068 008:728.402 - 0.653ms returns FALSE
T6068 008:829.696 JLINK_HasError()
T6068 008:829.810 JLINK_IsHalted()
T6068 008:830.789 - 0.977ms returns FALSE
T6068 008:931.382 JLINK_HasError()
T6068 008:931.511 JLINK_IsHalted()
T6068 008:932.358 - 0.845ms returns FALSE
T6068 009:033.342 JLINK_HasError()
T6068 009:033.379 JLINK_IsHalted()
T6068 009:033.872 - 0.492ms returns FALSE
T6068 009:134.746 JLINK_HasError()
T6068 009:134.803 JLINK_IsHalted()
T6068 009:135.401 - 0.596ms returns FALSE
T6068 009:236.138 JLINK_HasError()
T6068 009:236.255 JLINK_HasError()
T6068 009:236.313 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 009:236.380   Data:  47 A7 C0 06
T6068 009:236.452   Debug reg: DWT_CYCCNT
T6068 009:236.524 - 0.211ms returns 1 (0x1)
T6068 009:236.877 JLINK_IsHalted()
T6068 009:237.922 - 1.042ms returns FALSE
T6068 009:338.287 JLINK_HasError()
T6068 009:338.402 JLINK_IsHalted()
T6068 009:339.665 - 1.261ms returns FALSE
T6068 009:440.018 JLINK_HasError()
T6068 009:440.122 JLINK_IsHalted()
T6068 009:441.178 - 1.054ms returns FALSE
T6068 009:541.768 JLINK_HasError()
T6068 009:541.888 JLINK_IsHalted()
T6068 009:542.737 - 0.846ms returns FALSE
T6068 009:642.933 JLINK_HasError()
T6068 009:642.982 JLINK_IsHalted()
T6068 009:643.538 - 0.554ms returns FALSE
T6068 009:743.670 JLINK_HasError()
T6068 009:743.727 JLINK_IsHalted()
T6068 009:744.250 - 0.522ms returns FALSE
T6068 009:845.202 JLINK_HasError()
T6068 009:845.258 JLINK_HasError()
T6068 009:845.273 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 009:845.302   Data:  47 A7 C0 06
T6068 009:845.326   Debug reg: DWT_CYCCNT
T6068 009:845.346 - 0.073ms returns 1 (0x1)
T6068 009:845.568 JLINK_IsHalted()
T6068 009:846.260 - 0.690ms returns FALSE
T6068 009:946.592 JLINK_HasError()
T6068 009:946.706 JLINK_IsHalted()
T6068 009:947.712 - 1.003ms returns FALSE
T6068 010:048.002 JLINK_HasError()
T6068 010:048.118 JLINK_IsHalted()
T6068 010:049.189 - 1.069ms returns FALSE
T6068 010:150.153 JLINK_HasError()
T6068 010:150.257 JLINK_IsHalted()
T6068 010:151.012 - 0.753ms returns FALSE
T6068 010:251.145 JLINK_HasError()
T6068 010:251.202 JLINK_IsHalted()
T6068 010:251.816 - 0.613ms returns FALSE
T6068 010:352.445 JLINK_HasError()
T6068 010:352.486 JLINK_IsHalted()
T6068 010:353.057 - 0.569ms returns FALSE
T6068 010:453.325 JLINK_HasError()
T6068 010:453.439 JLINK_HasError()
T6068 010:453.490 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 010:453.556   Data:  47 A7 C0 06
T6068 010:453.627   Debug reg: DWT_CYCCNT
T6068 010:453.698 - 0.207ms returns 1 (0x1)
T6068 010:454.070 JLINK_IsHalted()
T6068 010:455.105 - 1.032ms returns FALSE
T6068 010:556.106 JLINK_HasError()
T6068 010:556.220 JLINK_IsHalted()
T6068 010:557.298 - 1.076ms returns FALSE
T6068 010:657.676 JLINK_HasError()
T6068 010:657.775 JLINK_IsHalted()
T6068 010:658.325 - 0.548ms returns FALSE
T6068 010:759.385 JLINK_HasError()
T6068 010:759.436 JLINK_IsHalted()
T6068 010:760.019 - 0.582ms returns FALSE
T6068 010:860.838 JLINK_HasError()
T6068 010:860.887 JLINK_IsHalted()
T6068 010:861.562 - 0.667ms returns FALSE
T6068 010:961.763 JLINK_HasError()
T6068 010:961.827 JLINK_HasError()
T6068 010:961.842 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 010:961.867   Data:  47 A7 C0 06
T6068 010:961.890   Debug reg: DWT_CYCCNT
T6068 010:961.910 - 0.067ms returns 1 (0x1)
T6068 010:962.018 JLINK_IsHalted()
T6068 010:962.606 - 0.587ms returns FALSE
T6068 011:062.779 JLINK_HasError()
T6068 011:062.893 JLINK_IsHalted()
T6068 011:063.862 - 0.967ms returns FALSE
T6068 011:164.163 JLINK_HasError()
T6068 011:164.285 JLINK_IsHalted()
T6068 011:165.276 - 0.988ms returns FALSE
T6068 011:265.644 JLINK_HasError()
T6068 011:265.766 JLINK_IsHalted()
T6068 011:266.834 - 1.065ms returns FALSE
T6068 011:367.549 JLINK_HasError()
T6068 011:367.600 JLINK_IsHalted()
T6068 011:368.134 - 0.533ms returns FALSE
T6068 011:468.234 JLINK_HasError()
T6068 011:468.285 JLINK_IsHalted()
T6068 011:468.852 - 0.567ms returns FALSE
T6068 011:569.755 JLINK_HasError()
T6068 011:569.806 JLINK_HasError()
T6068 011:569.822 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 011:569.846   Data:  47 A7 C0 06
T6068 011:569.869   Debug reg: DWT_CYCCNT
T6068 011:569.890 - 0.067ms returns 1 (0x1)
T6068 011:569.978 JLINK_IsHalted()
T6068 011:570.701 - 0.722ms returns FALSE
T6068 011:670.880 JLINK_HasError()
T6068 011:670.995 JLINK_IsHalted()
T6068 011:672.102 - 1.104ms returns FALSE
T6068 011:772.370 JLINK_HasError()
T6068 011:772.421 JLINK_IsHalted()
T6068 011:772.975 - 0.553ms returns FALSE
T6068 011:873.170 JLINK_HasError()
T6068 011:873.322 JLINK_IsHalted()
T6068 011:874.218 - 0.893ms returns FALSE
T6068 011:974.564 JLINK_HasError()
T6068 011:974.678 JLINK_IsHalted()
T6068 011:975.680 - 1.000ms returns FALSE
T6068 012:076.657 JLINK_HasError()
T6068 012:076.773 JLINK_IsHalted()
T6068 012:077.661 - 0.888ms returns FALSE
T6068 012:177.795 JLINK_HasError()
T6068 012:177.835 JLINK_HasError()
T6068 012:177.851 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 012:177.875   Data:  47 A7 C0 06
T6068 012:177.898   Debug reg: DWT_CYCCNT
T6068 012:177.919 - 0.068ms returns 1 (0x1)
T6068 012:178.016 JLINK_IsHalted()
T6068 012:178.526 - 0.509ms returns FALSE
T6068 012:278.849 JLINK_HasError()
T6068 012:278.894 JLINK_IsHalted()
T6068 012:279.429 - 0.534ms returns FALSE
T6068 012:380.024 JLINK_HasError()
T6068 012:380.109 JLINK_IsHalted()
T6068 012:380.896 - 0.786ms returns FALSE
T6068 012:481.492 JLINK_HasError()
T6068 012:481.621 JLINK_IsHalted()
T6068 012:482.607 - 0.984ms returns FALSE
T6068 012:583.131 JLINK_HasError()
T6068 012:583.190 JLINK_IsHalted()
T6068 012:584.136 - 0.943ms returns FALSE
T6068 012:684.878 JLINK_HasError()
T6068 012:684.987 JLINK_IsHalted()
T6068 012:685.762 - 0.772ms returns FALSE
T6068 012:785.975 JLINK_HasError()
T6068 012:786.027 JLINK_HasError()
T6068 012:786.042 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 012:786.068   Data:  47 A7 C0 06
T6068 012:786.090   Debug reg: DWT_CYCCNT
T6068 012:786.112 - 0.069ms returns 1 (0x1)
T6068 012:786.215 JLINK_IsHalted()
T6068 012:786.757 - 0.541ms returns FALSE
T6068 012:887.562 JLINK_HasError()
T6068 012:887.613 JLINK_IsHalted()
T6068 012:888.390 - 0.773ms returns FALSE
T6068 012:988.618 JLINK_HasError()
T6068 012:988.674 JLINK_IsHalted()
T6068 012:989.671 - 0.996ms returns FALSE
T6068 013:090.030 JLINK_HasError()
T6068 013:090.142 JLINK_IsHalted()
T6068 013:091.009 - 0.865ms returns FALSE
T6068 013:191.292 JLINK_HasError()
T6068 013:191.414 JLINK_IsHalted()
T6068 013:192.390 - 0.974ms returns FALSE
T6068 013:292.685 JLINK_HasError()
T6068 013:292.806 JLINK_IsHalted()
T6068 013:293.817 - 1.007ms returns FALSE
T6068 013:394.051 JLINK_HasError()
T6068 013:394.102 JLINK_HasError()
T6068 013:394.118 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 013:394.142   Data:  47 A7 C0 06
T6068 013:394.165   Debug reg: DWT_CYCCNT
T6068 013:394.186 - 0.067ms returns 1 (0x1)
T6068 013:394.277 JLINK_IsHalted()
T6068 013:394.849 - 0.572ms returns FALSE
T6068 013:495.125 JLINK_HasError()
T6068 013:495.230 JLINK_IsHalted()
T6068 013:496.102 - 0.871ms returns FALSE
T6068 013:596.374 JLINK_HasError()
T6068 013:596.490 JLINK_IsHalted()
T6068 013:597.421 - 0.929ms returns FALSE
T6068 013:698.014 JLINK_HasError()
T6068 013:698.173 JLINK_IsHalted()
T6068 013:698.952 - 0.776ms returns FALSE
T6068 013:799.190 JLINK_HasError()
T6068 013:799.283 JLINK_IsHalted()
T6068 013:800.192 - 0.907ms returns FALSE
T6068 013:900.642 JLINK_HasError()
T6068 013:900.685 JLINK_IsHalted()
T6068 013:901.198 - 0.513ms returns FALSE
T6068 014:001.731 JLINK_HasError()
T6068 014:001.782 JLINK_HasError()
T6068 014:001.798 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 014:001.822   Data:  47 A7 C0 06
T6068 014:001.845   Debug reg: DWT_CYCCNT
T6068 014:001.866 - 0.068ms returns 1 (0x1)
T6068 014:001.989 JLINK_IsHalted()
T6068 014:002.538 - 0.549ms returns FALSE
T6068 014:103.374 JLINK_HasError()
T6068 014:103.486 JLINK_IsHalted()
T6068 014:104.422 - 0.934ms returns FALSE
T6068 014:204.658 JLINK_HasError()
T6068 014:204.774 JLINK_IsHalted()
T6068 014:205.645 - 0.870ms returns FALSE
T6068 014:306.650 JLINK_HasError()
T6068 014:306.706 JLINK_IsHalted()
T6068 014:307.534 - 0.827ms returns FALSE
T6068 014:407.685 JLINK_HasError()
T6068 014:407.741 JLINK_IsHalted()
T6068 014:408.597 - 0.855ms returns FALSE
T6068 014:508.776 JLINK_HasError()
T6068 014:508.894 JLINK_IsHalted()
T6068 014:509.650 - 0.753ms returns FALSE
T6068 014:609.856 JLINK_HasError()
T6068 014:609.906 JLINK_HasError()
T6068 014:609.920 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 014:609.946   Data:  47 A7 C0 06
T6068 014:609.966   Debug reg: DWT_CYCCNT
T6068 014:609.987 - 0.067ms returns 1 (0x1)
T6068 014:610.082 JLINK_IsHalted()
T6068 014:610.712 - 0.630ms returns FALSE
T6068 014:711.235 JLINK_HasError()
T6068 014:711.283 JLINK_IsHalted()
T6068 014:711.886 - 0.603ms returns FALSE
T6068 014:812.176 JLINK_HasError()
T6068 014:812.290 JLINK_IsHalted()
T6068 014:813.342 - 1.049ms returns FALSE
T6068 014:914.077 JLINK_HasError()
T6068 014:914.123 JLINK_IsHalted()
T6068 014:914.904 - 0.779ms returns FALSE
T6068 015:015.781 JLINK_HasError()
T6068 015:015.902 JLINK_IsHalted()
T6068 015:016.861 - 0.956ms returns FALSE
T6068 015:117.160 JLINK_HasError()
T6068 015:117.210 JLINK_Halt()
T6068 015:120.197 - 2.985ms returns 0x00
T6068 015:120.216 JLINK_IsHalted()
T6068 015:120.230 - 0.014ms returns TRUE
T6068 015:120.246 JLINK_IsHalted()
T6068 015:120.261 - 0.014ms returns TRUE
T6068 015:120.277 JLINK_IsHalted()
T6068 015:120.290 - 0.013ms returns TRUE
T6068 015:120.309 JLINK_HasError()
T6068 015:120.326 JLINK_ReadReg(R15 (PC))
T6068 015:120.342 - 0.016ms returns 0x0802CB6A
T6068 015:120.358 JLINK_ReadReg(XPSR)
T6068 015:120.373 - 0.014ms returns 0x01000000
T6068 015:120.416 JLINK_HasError()
T6068 015:120.432 JLINK_HasError()
T6068 015:120.446 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T6068 015:120.466   CPU_ReadMem(4 bytes @ 0xE000ED30)
T6068 015:121.030   Data:  01 00 00 00
T6068 015:121.053 - 0.606ms returns 1 (0x1)
T6068 015:121.069 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T6068 015:121.085   CPU_ReadMem(4 bytes @ 0xE0001028)
T6068 015:121.613   Data:  00 00 00 00
T6068 015:121.635   Debug reg: DWT_FUNC[0]
T6068 015:121.656 - 0.586ms returns 1 (0x1)
T6068 015:121.670 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T6068 015:121.686   CPU_ReadMem(4 bytes @ 0xE0001038)
T6068 015:122.235   Data:  00 02 00 00
T6068 015:122.258   Debug reg: DWT_FUNC[1]
T6068 015:122.278 - 0.607ms returns 1 (0x1)
T6068 015:122.293 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T6068 015:122.309   CPU_ReadMem(4 bytes @ 0xE0001048)
T6068 015:122.838   Data:  00 00 00 00
T6068 015:122.861   Debug reg: DWT_FUNC[2]
T6068 015:122.882 - 0.588ms returns 1 (0x1)
T6068 015:122.896 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T6068 015:122.912   CPU_ReadMem(4 bytes @ 0xE0001058)
T6068 015:123.565   Data:  00 00 00 00
T6068 015:123.587   Debug reg: DWT_FUNC[3]
T6068 015:123.608 - 0.711ms returns 1 (0x1)
T6068 015:123.707 JLINK_HasError()
T6068 015:123.723 JLINK_ReadReg(R0)
T6068 015:123.738 - 0.014ms returns 0x000000B0
T6068 015:123.752 JLINK_ReadReg(R1)
T6068 015:123.766 - 0.014ms returns 0x000000B0
T6068 015:123.781 JLINK_ReadReg(R2)
T6068 015:123.795 - 0.014ms returns 0x00001F90
T6068 015:123.810 JLINK_ReadReg(R3)
T6068 015:123.824 - 0.013ms returns 0x00000000
T6068 015:123.838 JLINK_ReadReg(R4)
T6068 015:123.853 - 0.014ms returns 0x08040534
T6068 015:123.867 JLINK_ReadReg(R5)
T6068 015:123.882 - 0.014ms returns 0x08040534
T6068 015:123.896 JLINK_ReadReg(R6)
T6068 015:123.910 - 0.014ms returns 0x00000000
T6068 015:123.925 JLINK_ReadReg(R7)
T6068 015:123.939 - 0.014ms returns 0x00000000
T6068 015:123.954 JLINK_ReadReg(R8)
T6068 015:123.968 - 0.014ms returns 0x00000000
T6068 015:123.982 JLINK_ReadReg(R9)
T6068 015:123.995 - 0.013ms returns 0x00000000
T6068 015:124.010 JLINK_ReadReg(R10)
T6068 015:124.024 - 0.014ms returns 0x00000000
T6068 015:124.038 JLINK_ReadReg(R11)
T6068 015:124.053 - 0.014ms returns 0x00000000
T6068 015:124.067 JLINK_ReadReg(R12)
T6068 015:124.082 - 0.014ms returns 0x00000000
T6068 015:124.096 JLINK_ReadReg(R13 (SP))
T6068 015:124.110 - 0.014ms returns 0x20015888
T6068 015:124.126 JLINK_ReadReg(R14)
T6068 015:124.139 - 0.014ms returns 0x0802B5E9
T6068 015:124.155 JLINK_ReadReg(R15 (PC))
T6068 015:124.170 - 0.014ms returns 0x0802CB6A
T6068 015:124.184 JLINK_ReadReg(XPSR)
T6068 015:124.198 - 0.014ms returns 0x01000000
T6068 015:124.214 JLINK_ReadReg(MSP)
T6068 015:124.229 - 0.014ms returns 0x20015888
T6068 015:124.243 JLINK_ReadReg(PSP)
T6068 015:124.258 - 0.014ms returns 0x00000000
T6068 015:124.272 JLINK_ReadReg(CFBP)
T6068 015:124.286 - 0.014ms returns 0x00000000
T6068 015:124.302 JLINK_ReadReg(FPSCR)
T6068 015:128.563 - 4.260ms returns 0x00000000
T6068 015:128.589 JLINK_ReadReg(FPS0)
T6068 015:128.605 - 0.015ms returns 0x00000000
T6068 015:128.619 JLINK_ReadReg(FPS1)
T6068 015:128.634 - 0.014ms returns 0x00000000
T6068 015:128.648 JLINK_ReadReg(FPS2)
T6068 015:128.662 - 0.014ms returns 0x00000000
T6068 015:128.677 JLINK_ReadReg(FPS3)
T6068 015:128.691 - 0.014ms returns 0x00000000
T6068 015:128.706 JLINK_ReadReg(FPS4)
T6068 015:128.720 - 0.014ms returns 0x00000000
T6068 015:128.734 JLINK_ReadReg(FPS5)
T6068 015:128.749 - 0.014ms returns 0x00000000
T6068 015:128.763 JLINK_ReadReg(FPS6)
T6068 015:128.778 - 0.014ms returns 0x00000000
T6068 015:128.792 JLINK_ReadReg(FPS7)
T6068 015:128.806 - 0.014ms returns 0x00000000
T6068 015:128.821 JLINK_ReadReg(FPS8)
T6068 015:128.835 - 0.014ms returns 0x00000000
T6068 015:128.853 JLINK_ReadReg(FPS9)
T6068 015:128.869 - 0.015ms returns 0x00000000
T6068 015:128.883 JLINK_ReadReg(FPS10)
T6068 015:128.898 - 0.014ms returns 0x00000000
T6068 015:128.912 JLINK_ReadReg(FPS11)
T6068 015:128.926 - 0.014ms returns 0x00000000
T6068 015:128.941 JLINK_ReadReg(FPS12)
T6068 015:128.955 - 0.014ms returns 0x00000000
T6068 015:128.970 JLINK_ReadReg(FPS13)
T6068 015:128.984 - 0.014ms returns 0x00000000
T6068 015:128.998 JLINK_ReadReg(FPS14)
T6068 015:129.013 - 0.014ms returns 0x00000000
T6068 015:129.027 JLINK_ReadReg(FPS15)
T6068 015:129.042 - 0.013ms returns 0x00000000
T6068 015:129.056 JLINK_ReadReg(FPS16)
T6068 015:129.069 - 0.014ms returns 0x00000000
T6068 015:129.085 JLINK_ReadReg(FPS17)
T6068 015:129.098 - 0.014ms returns 0x00000000
T6068 015:129.114 JLINK_ReadReg(FPS18)
T6068 015:129.128 - 0.014ms returns 0x00000000
T6068 015:129.142 JLINK_ReadReg(FPS19)
T6068 015:129.157 - 0.014ms returns 0x00000000
T6068 015:129.171 JLINK_ReadReg(FPS20)
T6068 015:129.184 - 0.014ms returns 0x00000000
T6068 015:129.200 JLINK_ReadReg(FPS21)
T6068 015:129.213 - 0.014ms returns 0x00000000
T6068 015:129.229 JLINK_ReadReg(FPS22)
T6068 015:129.243 - 0.014ms returns 0x00000000
T6068 015:129.258 JLINK_ReadReg(FPS23)
T6068 015:129.272 - 0.014ms returns 0x00000000
T6068 015:129.286 JLINK_ReadReg(FPS24)
T6068 015:129.301 - 0.014ms returns 0x00000000
T6068 015:129.315 JLINK_ReadReg(FPS25)
T6068 015:129.330 - 0.014ms returns 0x00000000
T6068 015:129.344 JLINK_ReadReg(FPS26)
T6068 015:129.358 - 0.014ms returns 0x00000000
T6068 015:129.373 JLINK_ReadReg(FPS27)
T6068 015:129.387 - 0.014ms returns 0x00000000
T6068 015:129.402 JLINK_ReadReg(FPS28)
T6068 015:129.416 - 0.014ms returns 0x00000000
T6068 015:129.430 JLINK_ReadReg(FPS29)
T6068 015:129.445 - 0.014ms returns 0x00000000
T6068 015:129.459 JLINK_ReadReg(FPS30)
T6068 015:129.474 - 0.014ms returns 0x00000000
T6068 015:129.488 JLINK_ReadReg(FPS31)
T6068 015:129.501 - 0.013ms returns 0x00000000
T316C 015:129.590 JLINK_HasError()
T316C 015:129.619 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T316C 015:129.640   CPU_ReadMem(4 bytes @ 0xE0001004)
T316C 015:130.078   Data:  05 7C 37 63
T316C 015:130.102   Debug reg: DWT_CYCCNT
T316C 015:130.123 - 0.503ms returns 1 (0x1)
T316C 015:130.565 JLINK_ReadMemEx(0x0802CB6A, 0x2 Bytes, Flags = 0x02000000)
T316C 015:130.590   CPU_ReadMem(64 bytes @ 0x0802CB40)
T316C 015:131.502    -- Updating C cache (64 bytes @ 0x0802CB40)
T316C 015:131.530    -- Read from C cache (2 bytes @ 0x0802CB6A)
T316C 015:131.550   Data:  08 B1
T316C 015:131.571 - 1.006ms returns 2 (0x2)
T316C 015:131.627 JLINK_ReadMemEx(0x0802CB6C, 0x3C Bytes, Flags = 0x02000000)
T316C 015:131.643   CPU_ReadMem(64 bytes @ 0x0802CB80)
T316C 015:132.637    -- Updating C cache (64 bytes @ 0x0802CB80)
T316C 015:132.659    -- Read from C cache (60 bytes @ 0x0802CB6C)
T316C 015:132.682   Data:  FF E7 F4 E7 9D F8 06 10 4F F4 02 70 00 EB 41 10 ...
T316C 015:132.702 - 1.075ms returns 60 (0x3C)
T316C 015:132.718 JLINK_ReadMemEx(0x0802CB6C, 0x2 Bytes, Flags = 0x02000000)
T316C 015:132.733    -- Read from C cache (2 bytes @ 0x0802CB6C)
T316C 015:132.754   Data:  FF E7
T316C 015:132.774 - 0.056ms returns 2 (0x2)
T6068 016:550.490 JLINK_ReadMemEx(0x0802CB6A, 0x2 Bytes, Flags = 0x02000000)
T6068 016:550.542    -- Read from C cache (2 bytes @ 0x0802CB6A)
T6068 016:550.565   Data:  08 B1
T6068 016:550.586 - 0.097ms returns 2 (0x2)
T6068 016:550.602 JLINK_HasError()
T6068 016:550.619 JLINK_HasError()
T6068 016:550.635 JLINK_Go()
T6068 016:551.198   CPU_ReadMem(4 bytes @ 0xE0001000)
T6068 016:552.448 - 1.812ms
T6068 016:653.046 JLINK_HasError()
T6068 016:653.163 JLINK_IsHalted()
T6068 016:654.229 - 1.064ms returns FALSE
T6068 016:754.461 JLINK_HasError()
T6068 016:754.554 JLINK_IsHalted()
T6068 016:755.392 - 0.838ms returns FALSE
T6068 016:855.550 JLINK_HasError()
T6068 016:855.660 JLINK_IsHalted()
T6068 016:856.342 - 0.682ms returns FALSE
T6068 016:956.436 JLINK_HasError()
T6068 016:956.486 JLINK_IsHalted()
T6068 016:957.062 - 0.576ms returns FALSE
T6068 017:057.770 JLINK_HasError()
T6068 017:057.812 JLINK_IsHalted()
T6068 017:058.352 - 0.537ms returns FALSE
T6068 017:158.678 JLINK_HasError()
T6068 017:158.794 JLINK_HasError()
T6068 017:158.844 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 017:158.914   CPU_ReadMem(4 bytes @ 0xE0001004)
T6068 017:159.976   Data:  CF C4 4C 69
T6068 017:160.100   Debug reg: DWT_CYCCNT
T6068 017:160.172 - 1.325ms returns 1 (0x1)
T6068 017:160.564 JLINK_IsHalted()
T6068 017:161.650 - 1.084ms returns FALSE
T6068 017:261.880 JLINK_HasError()
T6068 017:261.996 JLINK_IsHalted()
T6068 017:262.850 - 0.849ms returns FALSE
T6068 017:363.640 JLINK_HasError()
T6068 017:363.764 JLINK_IsHalted()
T6068 017:364.766 - 1.000ms returns FALSE
T6068 017:464.960 JLINK_HasError()
T6068 017:465.004 JLINK_IsHalted()
T6068 017:465.516 - 0.512ms returns FALSE
T6068 017:565.620 JLINK_HasError()
T6068 017:565.672 JLINK_IsHalted()
T6068 017:566.218 - 0.545ms returns FALSE
T6068 017:666.346 JLINK_HasError()
T6068 017:666.396 JLINK_IsHalted()
T6068 017:667.052 - 0.655ms returns FALSE
T6068 017:767.780 JLINK_HasError()
T6068 017:767.896 JLINK_HasError()
T6068 017:767.946 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 017:768.014   Data:  CF C4 4C 69
T6068 017:768.086   Debug reg: DWT_CYCCNT
T6068 017:768.156 - 0.209ms returns 1 (0x1)
T6068 017:768.540 JLINK_IsHalted()
T6068 017:769.514 - 0.973ms returns FALSE
T6068 017:869.712 JLINK_HasError()
T6068 017:869.762 JLINK_IsHalted()
T6068 017:870.380 - 0.618ms returns FALSE
T6068 017:970.658 JLINK_HasError()
T6068 017:970.748 JLINK_IsHalted()
T6068 017:971.598 - 0.849ms returns FALSE
T6068 018:072.020 JLINK_HasError()
T6068 018:072.068 JLINK_IsHalted()
T6068 018:072.614 - 0.547ms returns FALSE
T6068 018:172.746 JLINK_HasError()
T6068 018:172.796 JLINK_IsHalted()
T6068 018:173.386 - 0.588ms returns FALSE
T6068 018:273.500 JLINK_HasError()
T6068 018:273.548 JLINK_IsHalted()
T6068 018:274.108 - 0.556ms returns FALSE
T6068 018:374.966 JLINK_HasError()
T6068 018:375.068 JLINK_HasError()
T6068 018:375.114 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 018:375.172   Data:  CF C4 4C 69
T6068 018:375.236   Debug reg: DWT_CYCCNT
T6068 018:375.296 - 0.182ms returns 1 (0x1)
T6068 018:375.638 JLINK_IsHalted()
T6068 018:376.744 - 1.088ms returns FALSE
T6068 018:477.646 JLINK_HasError()
T6068 018:477.768 JLINK_IsHalted()
T6068 018:478.768 - 0.998ms returns FALSE
T6068 018:579.274 JLINK_HasError()
T6068 018:579.400 JLINK_IsHalted()
T6068 018:580.358 - 0.957ms returns FALSE
T6068 018:681.512 JLINK_HasError()
T6068 018:681.628 JLINK_IsHalted()
T6068 018:682.644 - 1.014ms returns FALSE
T6068 018:782.974 JLINK_HasError()
T6068 018:783.116 JLINK_IsHalted()
T6068 018:783.882 - 0.761ms returns FALSE
T6068 018:884.524 JLINK_HasError()
T6068 018:884.606 JLINK_HasError()
T6068 018:884.640 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 018:884.684   Data:  CF C4 4C 69
T6068 018:884.732   Debug reg: DWT_CYCCNT
T6068 018:884.778 - 0.138ms returns 1 (0x1)
T6068 018:885.082 JLINK_IsHalted()
T6068 018:886.050 - 0.965ms returns FALSE
T6068 018:986.452 JLINK_HasError()
T6068 018:986.500 JLINK_IsHalted()
T6068 018:987.040 - 0.539ms returns FALSE
T6068 019:087.884 JLINK_HasError()
T6068 019:087.936 JLINK_Halt()
T6068 019:091.028 - 3.091ms returns 0x00
T6068 019:091.044 JLINK_IsHalted()
T6068 019:091.058 - 0.014ms returns TRUE
T6068 019:091.072 JLINK_IsHalted()
T6068 019:091.086 - 0.013ms returns TRUE
T6068 019:091.100 JLINK_IsHalted()
T6068 019:091.116 - 0.013ms returns TRUE
T6068 019:091.130 JLINK_HasError()
T6068 019:091.146 JLINK_ReadReg(R15 (PC))
T6068 019:091.160 - 0.015ms returns 0x0802B5A2
T6068 019:091.174 JLINK_ReadReg(XPSR)
T6068 019:091.188 - 0.014ms returns 0x01000000
T6068 019:091.204 JLINK_HasError()
T6068 019:091.220 JLINK_HasError()
T6068 019:091.236 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T6068 019:091.256   CPU_ReadMem(4 bytes @ 0xE000ED30)
T6068 019:091.798   Data:  01 00 00 00
T6068 019:091.820 - 0.585ms returns 1 (0x1)
T6068 019:091.838 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T6068 019:091.856   CPU_ReadMem(4 bytes @ 0xE0001028)
T6068 019:092.360   Data:  00 00 00 00
T6068 019:092.380   Debug reg: DWT_FUNC[0]
T6068 019:092.404 - 0.564ms returns 1 (0x1)
T6068 019:092.418 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T6068 019:092.434   CPU_ReadMem(4 bytes @ 0xE0001038)
T6068 019:093.268   Data:  00 02 00 00
T6068 019:093.346   Debug reg: DWT_FUNC[1]
T6068 019:093.368 - 0.950ms returns 1 (0x1)
T6068 019:093.390 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T6068 019:093.420   CPU_ReadMem(4 bytes @ 0xE0001048)
T6068 019:094.010   Data:  00 00 00 00
T6068 019:094.038   Debug reg: DWT_FUNC[2]
T6068 019:094.060 - 0.669ms returns 1 (0x1)
T6068 019:094.076 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T6068 019:094.094   CPU_ReadMem(4 bytes @ 0xE0001058)
T6068 019:094.652   Data:  00 00 00 00
T6068 019:094.676   Debug reg: DWT_FUNC[3]
T6068 019:094.700 - 0.625ms returns 1 (0x1)
T6068 019:094.798 JLINK_HasError()
T6068 019:094.814 JLINK_ReadReg(R0)
T6068 019:094.830 - 0.015ms returns 0x000000F4
T6068 019:094.844 JLINK_ReadReg(R1)
T6068 019:094.860 - 0.014ms returns 0x00000007
T6068 019:094.874 JLINK_ReadReg(R2)
T6068 019:094.888 - 0.014ms returns 0x00001F90
T6068 019:094.902 JLINK_ReadReg(R3)
T6068 019:094.916 - 0.013ms returns 0x00000000
T6068 019:094.932 JLINK_ReadReg(R4)
T6068 019:094.944 - 0.014ms returns 0x08040534
T6068 019:094.960 JLINK_ReadReg(R5)
T6068 019:094.972 - 0.014ms returns 0x08040534
T6068 019:094.988 JLINK_ReadReg(R6)
T6068 019:095.002 - 0.014ms returns 0x00000000
T6068 019:095.016 JLINK_ReadReg(R7)
T6068 019:095.030 - 0.014ms returns 0x00000000
T6068 019:095.044 JLINK_ReadReg(R8)
T6068 019:095.060 - 0.014ms returns 0x00000000
T6068 019:095.074 JLINK_ReadReg(R9)
T6068 019:095.088 - 0.014ms returns 0x00000000
T6068 019:095.102 JLINK_ReadReg(R10)
T6068 019:095.116 - 0.013ms returns 0x00000000
T6068 019:095.132 JLINK_ReadReg(R11)
T6068 019:095.146 - 0.014ms returns 0x00000000
T6068 019:095.160 JLINK_ReadReg(R12)
T6068 019:095.174 - 0.014ms returns 0x00000000
T6068 019:095.188 JLINK_ReadReg(R13 (SP))
T6068 019:095.204 - 0.014ms returns 0x20015870
T6068 019:095.218 JLINK_ReadReg(R14)
T6068 019:095.232 - 0.014ms returns 0x0802CB6B
T6068 019:095.246 JLINK_ReadReg(R15 (PC))
T6068 019:095.260 - 0.014ms returns 0x0802B5A2
T6068 019:095.276 JLINK_ReadReg(XPSR)
T6068 019:095.290 - 0.014ms returns 0x01000000
T6068 019:095.308 JLINK_ReadReg(MSP)
T6068 019:095.322 - 0.014ms returns 0x20015870
T6068 019:095.336 JLINK_ReadReg(PSP)
T6068 019:095.350 - 0.014ms returns 0x00000000
T6068 019:095.364 JLINK_ReadReg(CFBP)
T6068 019:095.380 - 0.014ms returns 0x00000000
T6068 019:095.394 JLINK_ReadReg(FPSCR)
T6068 019:099.890 - 4.495ms returns 0x00000000
T6068 019:099.908 JLINK_ReadReg(FPS0)
T6068 019:099.922 - 0.014ms returns 0x00000000
T6068 019:099.936 JLINK_ReadReg(FPS1)
T6068 019:099.950 - 0.014ms returns 0x00000000
T6068 019:099.964 JLINK_ReadReg(FPS2)
T6068 019:099.980 - 0.013ms returns 0x00000000
T6068 019:099.994 JLINK_ReadReg(FPS3)
T6068 019:100.008 - 0.014ms returns 0x00000000
T6068 019:100.022 JLINK_ReadReg(FPS4)
T6068 019:100.036 - 0.013ms returns 0x00000000
T6068 019:100.052 JLINK_ReadReg(FPS5)
T6068 019:100.064 - 0.014ms returns 0x00000000
T6068 019:100.080 JLINK_ReadReg(FPS6)
T6068 019:100.092 - 0.014ms returns 0x00000000
T6068 019:100.108 JLINK_ReadReg(FPS7)
T6068 019:100.124 - 0.014ms returns 0x00000000
T6068 019:100.138 JLINK_ReadReg(FPS8)
T6068 019:100.150 - 0.014ms returns 0x00000000
T6068 019:100.166 JLINK_ReadReg(FPS9)
T6068 019:100.180 - 0.014ms returns 0x00000000
T6068 019:100.194 JLINK_ReadReg(FPS10)
T6068 019:100.208 - 0.014ms returns 0x00000000
T6068 019:100.224 JLINK_ReadReg(FPS11)
T6068 019:100.236 - 0.014ms returns 0x00000000
T6068 019:100.252 JLINK_ReadReg(FPS12)
T6068 019:100.266 - 0.013ms returns 0x00000000
T6068 019:100.280 JLINK_ReadReg(FPS13)
T6068 019:100.294 - 0.014ms returns 0x00000000
T6068 019:100.308 JLINK_ReadReg(FPS14)
T6068 019:100.326 - 0.017ms returns 0x00000000
T6068 019:100.342 JLINK_ReadReg(FPS15)
T6068 019:100.356 - 0.014ms returns 0x00000000
T6068 019:100.372 JLINK_ReadReg(FPS16)
T6068 019:100.386 - 0.014ms returns 0x00000000
T6068 019:100.400 JLINK_ReadReg(FPS17)
T6068 019:100.414 - 0.014ms returns 0x00000000
T6068 019:100.428 JLINK_ReadReg(FPS18)
T6068 019:100.444 - 0.014ms returns 0x00000000
T6068 019:100.458 JLINK_ReadReg(FPS19)
T6068 019:100.472 - 0.014ms returns 0x00000000
T6068 019:100.486 JLINK_ReadReg(FPS20)
T6068 019:100.500 - 0.014ms returns 0x00000000
T6068 019:100.516 JLINK_ReadReg(FPS21)
T6068 019:100.530 - 0.014ms returns 0x00000000
T6068 019:100.544 JLINK_ReadReg(FPS22)
T6068 019:100.558 - 0.014ms returns 0x00000000
T6068 019:100.572 JLINK_ReadReg(FPS23)
T6068 019:100.588 - 0.014ms returns 0x00000000
T6068 019:100.602 JLINK_ReadReg(FPS24)
T6068 019:100.616 - 0.014ms returns 0x00000000
T6068 019:100.630 JLINK_ReadReg(FPS25)
T6068 019:100.644 - 0.014ms returns 0x00000000
T6068 019:100.660 JLINK_ReadReg(FPS26)
T6068 019:100.672 - 0.014ms returns 0x00000000
T6068 019:100.688 JLINK_ReadReg(FPS27)
T6068 019:100.700 - 0.014ms returns 0x00000000
T6068 019:100.716 JLINK_ReadReg(FPS28)
T6068 019:100.730 - 0.014ms returns 0x00000000
T6068 019:100.746 JLINK_ReadReg(FPS29)
T6068 019:100.758 - 0.014ms returns 0x00000000
T6068 019:100.774 JLINK_ReadReg(FPS30)
T6068 019:100.788 - 0.014ms returns 0x00000000
T6068 019:100.804 JLINK_ReadReg(FPS31)
T6068 019:100.820 - 0.014ms returns 0x00000000
T316C 019:100.888 JLINK_HasError()
T316C 019:100.908 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T316C 019:100.924   CPU_ReadMem(4 bytes @ 0xE0001004)
T316C 019:101.442   Data:  D9 24 9D 7C
T316C 019:101.470   Debug reg: DWT_CYCCNT
T316C 019:101.492 - 0.582ms returns 1 (0x1)
T316C 019:101.900 JLINK_ReadMemEx(0x0802B5A2, 0x2 Bytes, Flags = 0x02000000)
T316C 019:101.924   CPU_ReadMem(64 bytes @ 0x0802B580)
T316C 019:102.828    -- Updating C cache (64 bytes @ 0x0802B580)
T316C 019:102.850    -- Read from C cache (2 bytes @ 0x0802B5A2)
T316C 019:102.870   Data:  C2 F2
T316C 019:102.892 - 0.990ms returns 2 (0x2)
T316C 019:102.908 JLINK_ReadMemEx(0x0802B5A4, 0x3C Bytes, Flags = 0x02000000)
T316C 019:102.924   CPU_ReadMem(64 bytes @ 0x0802B5C0)
T316C 019:103.856    -- Updating C cache (64 bytes @ 0x0802B5C0)
T316C 019:103.878    -- Read from C cache (60 bytes @ 0x0802B5A4)
T316C 019:103.900   Data:  00 00 01 90 80 68 80 47 01 98 00 69 80 47 01 98 ...
T316C 019:103.920 - 1.013ms returns 60 (0x3C)
T316C 019:103.936 JLINK_ReadMemEx(0x0802B5A4, 0x2 Bytes, Flags = 0x02000000)
T316C 019:103.950    -- Read from C cache (2 bytes @ 0x0802B5A4)
T316C 019:103.972   Data:  00 00
T316C 019:103.992 - 0.056ms returns 2 (0x2)
T6068 020:079.606 JLINK_ReadMemEx(0x0802B5A2, 0x2 Bytes, Flags = 0x02000000)
T6068 020:079.678    -- Read from C cache (2 bytes @ 0x0802B5A2)
T6068 020:079.716   Data:  C2 F2
T6068 020:079.756 - 0.150ms returns 2 (0x2)
T6068 020:079.784 JLINK_HasError()
T6068 020:079.812 JLINK_HasError()
T6068 020:079.838 JLINK_Go()
T6068 020:080.592   CPU_ReadMem(4 bytes @ 0xE0001000)
T6068 020:082.218 - 2.379ms
T6068 020:182.450 JLINK_HasError()
T6068 020:182.574 JLINK_IsHalted()
T6068 020:183.268 - 0.692ms returns FALSE
T6068 020:283.388 JLINK_HasError()
T6068 020:283.436 JLINK_IsHalted()
T6068 020:283.934 - 0.496ms returns FALSE
T6068 020:384.788 JLINK_HasError()
T6068 020:384.840 JLINK_IsHalted()
T6068 020:385.380 - 0.538ms returns FALSE
T6068 020:486.524 JLINK_HasError()
T6068 020:486.640 JLINK_IsHalted()
T6068 020:487.718 - 1.077ms returns FALSE
T6068 020:588.040 JLINK_HasError()
T6068 020:588.160 JLINK_IsHalted()
T6068 020:589.164 - 1.003ms returns FALSE
T6068 020:689.536 JLINK_HasError()
T6068 020:689.666 JLINK_HasError()
T6068 020:689.722 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 020:689.796   CPU_ReadMem(4 bytes @ 0xE0001004)
T6068 020:690.844   Data:  AB 55 B5 82
T6068 020:690.976   Debug reg: DWT_CYCCNT
T6068 020:691.052 - 1.331ms returns 1 (0x1)
T6068 020:691.438 JLINK_IsHalted()
T6068 020:692.484 - 1.043ms returns FALSE
T6068 020:792.618 JLINK_HasError()
T6068 020:792.676 JLINK_IsHalted()
T6068 020:793.284 - 0.610ms returns FALSE
T6068 020:893.840 JLINK_HasError()
T6068 020:893.896 JLINK_IsHalted()
T6068 020:894.554 - 0.657ms returns FALSE
T6068 020:994.668 JLINK_HasError()
T6068 020:994.716 JLINK_IsHalted()
T6068 020:995.254 - 0.538ms returns FALSE
T6068 021:096.074 JLINK_HasError()
T6068 021:096.196 JLINK_IsHalted()
T6068 021:097.254 - 1.057ms returns FALSE
T6068 021:197.628 JLINK_HasError()
T6068 021:197.742 JLINK_IsHalted()
T6068 021:198.812 - 1.069ms returns FALSE
T6068 021:299.378 JLINK_HasError()
T6068 021:299.500 JLINK_HasError()
T6068 021:299.556 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 021:299.628   Data:  AB 55 B5 82
T6068 021:299.704   Debug reg: DWT_CYCCNT
T6068 021:299.780 - 0.225ms returns 1 (0x1)
T6068 021:300.160 JLINK_IsHalted()
T6068 021:301.182 - 1.020ms returns FALSE
T6068 021:401.476 JLINK_HasError()
T6068 021:401.590 JLINK_IsHalted()
T6068 021:402.588 - 0.994ms returns FALSE
T6068 021:503.228 JLINK_HasError()
T6068 021:503.280 JLINK_Halt()
T6068 021:506.268 - 2.989ms returns 0x00
T6068 021:506.288 JLINK_IsHalted()
T6068 021:506.310 - 0.021ms returns TRUE
T6068 021:506.332 JLINK_IsHalted()
T6068 021:506.354 - 0.021ms returns TRUE
T6068 021:506.376 JLINK_IsHalted()
T6068 021:506.398 - 0.021ms returns TRUE
T6068 021:506.420 JLINK_HasError()
T6068 021:506.444 JLINK_ReadReg(R15 (PC))
T6068 021:506.468 - 0.023ms returns 0x0803D960
T6068 021:506.492 JLINK_ReadReg(XPSR)
T6068 021:506.512 - 0.021ms returns 0x01000000
T6068 021:506.538 JLINK_HasError()
T6068 021:506.560 JLINK_HasError()
T6068 021:506.582 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T6068 021:506.612   CPU_ReadMem(4 bytes @ 0xE000ED30)
T6068 021:507.194   Data:  01 00 00 00
T6068 021:507.216 - 0.633ms returns 1 (0x1)
T6068 021:507.230 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T6068 021:507.246   CPU_ReadMem(4 bytes @ 0xE0001028)
T6068 021:507.760   Data:  00 00 00 00
T6068 021:507.796   Debug reg: DWT_FUNC[0]
T6068 021:507.816 - 0.584ms returns 1 (0x1)
T6068 021:507.834 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T6068 021:507.852   CPU_ReadMem(4 bytes @ 0xE0001038)
T6068 021:508.408   Data:  00 02 00 00
T6068 021:508.436   Debug reg: DWT_FUNC[1]
T6068 021:508.460 - 0.625ms returns 1 (0x1)
T6068 021:508.476 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T6068 021:508.492   CPU_ReadMem(4 bytes @ 0xE0001048)
T6068 021:509.062   Data:  00 00 00 00
T6068 021:509.094   Debug reg: DWT_FUNC[2]
T6068 021:509.116 - 0.641ms returns 1 (0x1)
T6068 021:509.134 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T6068 021:509.152   CPU_ReadMem(4 bytes @ 0xE0001058)
T6068 021:509.714   Data:  00 00 00 00
T6068 021:509.736   Debug reg: DWT_FUNC[3]
T6068 021:509.756 - 0.623ms returns 1 (0x1)
T6068 021:509.850 JLINK_HasError()
T6068 021:509.866 JLINK_ReadReg(R0)
T6068 021:509.880 - 0.015ms returns 0x00000000
T6068 021:509.894 JLINK_ReadReg(R1)
T6068 021:509.908 - 0.014ms returns 0x0803D961
T6068 021:509.924 JLINK_ReadReg(R2)
T6068 021:509.938 - 0.013ms returns 0x00001F90
T6068 021:509.952 JLINK_ReadReg(R3)
T6068 021:509.966 - 0.014ms returns 0x00000000
T6068 021:509.980 JLINK_ReadReg(R4)
T6068 021:509.996 - 0.014ms returns 0x08040534
T6068 021:510.010 JLINK_ReadReg(R5)
T6068 021:510.024 - 0.014ms returns 0x08040534
T6068 021:510.038 JLINK_ReadReg(R6)
T6068 021:510.052 - 0.014ms returns 0x00000000
T6068 021:510.068 JLINK_ReadReg(R7)
T6068 021:510.082 - 0.014ms returns 0x00000000
T6068 021:510.096 JLINK_ReadReg(R8)
T6068 021:510.110 - 0.014ms returns 0x00000000
T6068 021:510.124 JLINK_ReadReg(R9)
T6068 021:510.140 - 0.014ms returns 0x00000000
T6068 021:510.154 JLINK_ReadReg(R10)
T6068 021:510.166 - 0.013ms returns 0x00000000
T6068 021:510.182 JLINK_ReadReg(R11)
T6068 021:510.196 - 0.014ms returns 0x00000000
T6068 021:510.210 JLINK_ReadReg(R12)
T6068 021:510.224 - 0.014ms returns 0x00000000
T6068 021:510.238 JLINK_ReadReg(R13 (SP))
T6068 021:510.252 - 0.014ms returns 0x20015870
T6068 021:510.276 JLINK_ReadReg(R14)
T6068 021:510.290 - 0.014ms returns 0x0802B5BD
T6068 021:510.304 JLINK_ReadReg(R15 (PC))
T6068 021:510.318 - 0.014ms returns 0x0803D960
T6068 021:510.332 JLINK_ReadReg(XPSR)
T6068 021:510.348 - 0.014ms returns 0x01000000
T6068 021:510.362 JLINK_ReadReg(MSP)
T6068 021:510.376 - 0.014ms returns 0x20015870
T6068 021:510.390 JLINK_ReadReg(PSP)
T6068 021:510.404 - 0.014ms returns 0x00000000
T6068 021:510.420 JLINK_ReadReg(CFBP)
T6068 021:510.432 - 0.014ms returns 0x00000000
T6068 021:510.448 JLINK_ReadReg(FPSCR)
T6068 021:515.152 - 4.704ms returns 0x00000000
T6068 021:515.184 JLINK_ReadReg(FPS0)
T6068 021:515.204 - 0.019ms returns 0x00000000
T6068 021:515.222 JLINK_ReadReg(FPS1)
T6068 021:515.240 - 0.018ms returns 0x00000000
T6068 021:515.260 JLINK_ReadReg(FPS2)
T6068 021:515.276 - 0.018ms returns 0x00000000
T6068 021:515.296 JLINK_ReadReg(FPS3)
T6068 021:515.314 - 0.017ms returns 0x00000000
T6068 021:515.332 JLINK_ReadReg(FPS4)
T6068 021:515.346 - 0.014ms returns 0x00000000
T6068 021:515.360 JLINK_ReadReg(FPS5)
T6068 021:515.376 - 0.014ms returns 0x00000000
T6068 021:515.390 JLINK_ReadReg(FPS6)
T6068 021:515.404 - 0.014ms returns 0x00000000
T6068 021:515.420 JLINK_ReadReg(FPS7)
T6068 021:515.434 - 0.014ms returns 0x00000000
T6068 021:515.446 JLINK_ReadReg(FPS8)
T6068 021:515.460 - 0.014ms returns 0x00000000
T6068 021:515.476 JLINK_ReadReg(FPS9)
T6068 021:515.490 - 0.014ms returns 0x00000000
T6068 021:515.504 JLINK_ReadReg(FPS10)
T6068 021:515.518 - 0.014ms returns 0x00000000
T6068 021:515.532 JLINK_ReadReg(FPS11)
T6068 021:515.548 - 0.014ms returns 0x00000000
T6068 021:515.562 JLINK_ReadReg(FPS12)
T6068 021:515.594 - 0.031ms returns 0x00000000
T6068 021:515.608 JLINK_ReadReg(FPS13)
T6068 021:515.622 - 0.014ms returns 0x00000000
T6068 021:515.636 JLINK_ReadReg(FPS14)
T6068 021:515.652 - 0.014ms returns 0x00000000
T6068 021:515.666 JLINK_ReadReg(FPS15)
T6068 021:515.680 - 0.014ms returns 0x00000000
T6068 021:515.694 JLINK_ReadReg(FPS16)
T6068 021:515.708 - 0.014ms returns 0x00000000
T6068 021:515.724 JLINK_ReadReg(FPS17)
T6068 021:515.738 - 0.014ms returns 0x00000000
T6068 021:515.754 JLINK_ReadReg(FPS18)
T6068 021:515.772 - 0.019ms returns 0x00000000
T6068 021:515.792 JLINK_ReadReg(FPS19)
T6068 021:515.806 - 0.014ms returns 0x00000000
T6068 021:515.820 JLINK_ReadReg(FPS20)
T6068 021:515.836 - 0.014ms returns 0x00000000
T6068 021:515.850 JLINK_ReadReg(FPS21)
T6068 021:515.864 - 0.013ms returns 0x00000000
T6068 021:515.878 JLINK_ReadReg(FPS22)
T6068 021:515.892 - 0.014ms returns 0x00000000
T6068 021:515.908 JLINK_ReadReg(FPS23)
T6068 021:515.922 - 0.014ms returns 0x00000000
T6068 021:515.936 JLINK_ReadReg(FPS24)
T6068 021:515.950 - 0.013ms returns 0x00000000
T6068 021:515.964 JLINK_ReadReg(FPS25)
T6068 021:515.980 - 0.014ms returns 0x00000000
T6068 021:515.994 JLINK_ReadReg(FPS26)
T6068 021:516.008 - 0.013ms returns 0x00000000
T6068 021:516.022 JLINK_ReadReg(FPS27)
T6068 021:516.036 - 0.014ms returns 0x00000000
T6068 021:516.052 JLINK_ReadReg(FPS28)
T6068 021:516.066 - 0.014ms returns 0x00000000
T6068 021:516.080 JLINK_ReadReg(FPS29)
T6068 021:516.094 - 0.014ms returns 0x00000000
T6068 021:516.108 JLINK_ReadReg(FPS30)
T6068 021:516.124 - 0.014ms returns 0x00000000
T6068 021:516.138 JLINK_ReadReg(FPS31)
T6068 021:516.152 - 0.014ms returns 0x00000000
T316C 021:516.228 JLINK_HasError()
T316C 021:516.252 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T316C 021:516.268   CPU_ReadMem(4 bytes @ 0xE0001004)
T316C 021:516.828   Data:  7E BF D9 8A
T316C 021:516.850   Debug reg: DWT_CYCCNT
T316C 021:516.870 - 0.619ms returns 1 (0x1)
T6068 022:231.126 JLINK_ReadMemEx(0x0803D960, 0x2 Bytes, Flags = 0x02000000)
T6068 022:231.206   CPU_ReadMem(64 bytes @ 0x0803D940)
T6068 022:232.070    -- Updating C cache (64 bytes @ 0x0803D940)
T6068 022:232.106    -- Read from C cache (2 bytes @ 0x0803D960)
T6068 022:232.132   Data:  82 B0
T6068 022:232.154 - 1.028ms returns 2 (0x2)
T6068 022:232.176 JLINK_HasError()
T6068 022:232.198 JLINK_HasError()
T6068 022:232.214 JLINK_Go()
T6068 022:232.762   CPU_ReadMem(4 bytes @ 0xE0001000)
T6068 022:233.836 - 1.622ms
T6068 022:334.308 JLINK_HasError()
T6068 022:334.356 JLINK_IsHalted()
T6068 022:334.956 - 0.599ms returns FALSE
T6068 022:435.234 JLINK_HasError()
T6068 022:435.418 JLINK_IsHalted()
T6068 022:436.396 - 0.977ms returns FALSE
T6068 022:536.608 JLINK_HasError()
T6068 022:536.654 JLINK_IsHalted()
T6068 022:537.180 - 0.524ms returns FALSE
T6068 022:637.468 JLINK_HasError()
T6068 022:637.588 JLINK_IsHalted()
T6068 022:638.438 - 0.848ms returns FALSE
T6068 022:738.948 JLINK_HasError()
T6068 022:739.062 JLINK_HasError()
T6068 022:739.114 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 022:739.182   CPU_ReadMem(4 bytes @ 0xE0001004)
T6068 022:740.172   Data:  C4 76 EB 8F
T6068 022:740.334   Debug reg: DWT_CYCCNT
T6068 022:740.404 - 1.291ms returns 1 (0x1)
T6068 022:740.812 JLINK_IsHalted()
T6068 022:741.764 - 0.948ms returns FALSE
T6068 022:842.364 JLINK_HasError()
T6068 022:842.486 JLINK_IsHalted()
T6068 022:843.486 - 0.998ms returns FALSE
T6068 022:943.762 JLINK_HasError()
T6068 022:943.884 JLINK_IsHalted()
T6068 022:944.900 - 1.015ms returns FALSE
T6068 023:045.344 JLINK_HasError()
T6068 023:045.468 JLINK_Halt()
T6068 023:048.750 - 3.282ms returns 0x00
T6068 023:048.786 JLINK_IsHalted()
T6068 023:048.800 - 0.014ms returns TRUE
T6068 023:048.814 JLINK_IsHalted()
T6068 023:048.828 - 0.013ms returns TRUE
T6068 023:048.844 JLINK_IsHalted()
T6068 023:048.858 - 0.014ms returns TRUE
T6068 023:048.872 JLINK_HasError()
T6068 023:048.888 JLINK_ReadReg(R15 (PC))
T6068 023:048.904 - 0.016ms returns 0x0803D9B0
T6068 023:048.918 JLINK_ReadReg(XPSR)
T6068 023:048.932 - 0.014ms returns 0x01000000
T6068 023:048.950 JLINK_HasError()
T6068 023:048.966 JLINK_HasError()
T6068 023:048.980 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T6068 023:049.000   CPU_ReadMem(4 bytes @ 0xE000ED30)
T6068 023:049.610   Data:  01 00 00 00
T6068 023:049.632 - 0.652ms returns 1 (0x1)
T6068 023:049.648 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T6068 023:049.662   CPU_ReadMem(4 bytes @ 0xE0001028)
T6068 023:050.214   Data:  00 00 00 00
T6068 023:050.236   Debug reg: DWT_FUNC[0]
T6068 023:050.258 - 0.609ms returns 1 (0x1)
T6068 023:050.272 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T6068 023:050.288   CPU_ReadMem(4 bytes @ 0xE0001038)
T6068 023:050.838   Data:  00 02 00 00
T6068 023:050.860   Debug reg: DWT_FUNC[1]
T6068 023:050.882 - 0.609ms returns 1 (0x1)
T6068 023:050.896 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T6068 023:050.912   CPU_ReadMem(4 bytes @ 0xE0001048)
T6068 023:051.420   Data:  00 00 00 00
T6068 023:051.444   Debug reg: DWT_FUNC[2]
T6068 023:051.464 - 0.567ms returns 1 (0x1)
T6068 023:051.478 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T6068 023:051.492   CPU_ReadMem(4 bytes @ 0xE0001058)
T6068 023:052.046   Data:  00 00 00 00
T6068 023:052.068   Debug reg: DWT_FUNC[3]
T6068 023:052.090 - 0.610ms returns 1 (0x1)
T6068 023:052.180 JLINK_HasError()
T6068 023:052.194 JLINK_ReadReg(R0)
T6068 023:052.210 - 0.014ms returns 0x0803D9B1
T6068 023:052.224 JLINK_ReadReg(R1)
T6068 023:052.238 - 0.014ms returns 0x000000B0
T6068 023:052.252 JLINK_ReadReg(R2)
T6068 023:052.268 - 0.014ms returns 0x00001F90
T6068 023:052.282 JLINK_ReadReg(R3)
T6068 023:052.294 - 0.013ms returns 0x00000000
T6068 023:052.308 JLINK_ReadReg(R4)
T6068 023:052.324 - 0.014ms returns 0x08040534
T6068 023:052.338 JLINK_ReadReg(R5)
T6068 023:052.352 - 0.014ms returns 0x08040534
T6068 023:052.366 JLINK_ReadReg(R6)
T6068 023:052.380 - 0.014ms returns 0x00000000
T6068 023:052.396 JLINK_ReadReg(R7)
T6068 023:052.410 - 0.014ms returns 0x00000000
T6068 023:052.424 JLINK_ReadReg(R8)
T6068 023:052.438 - 0.014ms returns 0x00000000
T6068 023:052.452 JLINK_ReadReg(R9)
T6068 023:052.468 - 0.014ms returns 0x00000000
T6068 023:052.482 JLINK_ReadReg(R10)
T6068 023:052.496 - 0.014ms returns 0x00000000
T6068 023:052.510 JLINK_ReadReg(R11)
T6068 023:052.524 - 0.014ms returns 0x00000000
T6068 023:052.540 JLINK_ReadReg(R12)
T6068 023:052.556 - 0.015ms returns 0x00000000
T6068 023:052.572 JLINK_ReadReg(R13 (SP))
T6068 023:052.586 - 0.014ms returns 0x20015870
T6068 023:052.600 JLINK_ReadReg(R14)
T6068 023:052.614 - 0.013ms returns 0x0802B5E9
T6068 023:052.628 JLINK_ReadReg(R15 (PC))
T6068 023:052.644 - 0.014ms returns 0x0803D9B0
T6068 023:052.658 JLINK_ReadReg(XPSR)
T6068 023:052.670 - 0.013ms returns 0x01000000
T6068 023:052.684 JLINK_ReadReg(MSP)
T6068 023:052.700 - 0.013ms returns 0x20015870
T6068 023:052.714 JLINK_ReadReg(PSP)
T6068 023:052.728 - 0.013ms returns 0x00000000
T6068 023:052.742 JLINK_ReadReg(CFBP)
T6068 023:052.756 - 0.013ms returns 0x00000000
T6068 023:052.770 JLINK_ReadReg(FPSCR)
T6068 023:057.288 - 4.517ms returns 0x00000000
T6068 023:057.308 JLINK_ReadReg(FPS0)
T6068 023:057.324 - 0.016ms returns 0x00000000
T6068 023:057.340 JLINK_ReadReg(FPS1)
T6068 023:057.354 - 0.015ms returns 0x00000000
T6068 023:057.374 JLINK_ReadReg(FPS2)
T6068 023:057.388 - 0.014ms returns 0x00000000
T6068 023:057.404 JLINK_ReadReg(FPS3)
T6068 023:057.418 - 0.014ms returns 0x00000000
T6068 023:057.434 JLINK_ReadReg(FPS4)
T6068 023:057.452 - 0.018ms returns 0x00000000
T6068 023:057.472 JLINK_ReadReg(FPS5)
T6068 023:057.484 - 0.014ms returns 0x00000000
T6068 023:057.500 JLINK_ReadReg(FPS6)
T6068 023:057.514 - 0.014ms returns 0x00000000
T6068 023:057.528 JLINK_ReadReg(FPS7)
T6068 023:057.542 - 0.014ms returns 0x00000000
T6068 023:057.556 JLINK_ReadReg(FPS8)
T6068 023:057.622 - 0.064ms returns 0x00000000
T6068 023:057.638 JLINK_ReadReg(FPS9)
T6068 023:057.652 - 0.014ms returns 0x00000000
T6068 023:057.668 JLINK_ReadReg(FPS10)
T6068 023:057.682 - 0.014ms returns 0x00000000
T6068 023:057.696 JLINK_ReadReg(FPS11)
T6068 023:057.710 - 0.014ms returns 0x00000000
T6068 023:057.724 JLINK_ReadReg(FPS12)
T6068 023:057.740 - 0.015ms returns 0x00000000
T6068 023:057.760 JLINK_ReadReg(FPS13)
T6068 023:057.778 - 0.017ms returns 0x00000000
T6068 023:057.792 JLINK_ReadReg(FPS14)
T6068 023:057.808 - 0.014ms returns 0x00000000
T6068 023:057.822 JLINK_ReadReg(FPS15)
T6068 023:057.836 - 0.014ms returns 0x00000000
T6068 023:057.852 JLINK_ReadReg(FPS16)
T6068 023:057.866 - 0.014ms returns 0x00000000
T6068 023:057.882 JLINK_ReadReg(FPS17)
T6068 023:057.896 - 0.014ms returns 0x00000000
T6068 023:057.910 JLINK_ReadReg(FPS18)
T6068 023:057.926 - 0.014ms returns 0x00000000
T6068 023:057.940 JLINK_ReadReg(FPS19)
T6068 023:057.956 - 0.013ms returns 0x00000000
T6068 023:057.970 JLINK_ReadReg(FPS20)
T6068 023:057.984 - 0.014ms returns 0x00000000
T6068 023:058.000 JLINK_ReadReg(FPS21)
T6068 023:058.014 - 0.014ms returns 0x00000000
T6068 023:058.028 JLINK_ReadReg(FPS22)
T6068 023:058.044 - 0.014ms returns 0x00000000
T6068 023:058.058 JLINK_ReadReg(FPS23)
T6068 023:058.072 - 0.014ms returns 0x00000000
T6068 023:058.086 JLINK_ReadReg(FPS24)
T6068 023:058.100 - 0.014ms returns 0x00000000
T6068 023:058.116 JLINK_ReadReg(FPS25)
T6068 023:058.128 - 0.013ms returns 0x00000000
T6068 023:058.142 JLINK_ReadReg(FPS26)
T6068 023:058.156 - 0.014ms returns 0x00000000
T6068 023:058.172 JLINK_ReadReg(FPS27)
T6068 023:058.184 - 0.013ms returns 0x00000000
T6068 023:058.198 JLINK_ReadReg(FPS28)
T6068 023:058.212 - 0.013ms returns 0x00000000
T6068 023:058.228 JLINK_ReadReg(FPS29)
T6068 023:058.240 - 0.013ms returns 0x00000000
T6068 023:058.256 JLINK_ReadReg(FPS30)
T6068 023:058.268 - 0.014ms returns 0x00000000
T6068 023:058.284 JLINK_ReadReg(FPS31)
T6068 023:058.298 - 0.013ms returns 0x00000000
T316C 023:058.356 JLINK_HasError()
T316C 023:058.382 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T316C 023:058.404   CPU_ReadMem(4 bytes @ 0xE0001004)
T316C 023:058.966   Data:  B6 0B FC 92
T316C 023:058.988   Debug reg: DWT_CYCCNT
T316C 023:059.012 - 0.627ms returns 1 (0x1)
T6068 025:341.156 JLINK_ReadMemEx(0x0803D9B0, 0x2 Bytes, Flags = 0x02000000)
T6068 025:341.226   CPU_ReadMem(64 bytes @ 0x0803D980)
T6068 025:342.118    -- Updating C cache (64 bytes @ 0x0803D980)
T6068 025:342.142    -- Read from C cache (2 bytes @ 0x0803D9B0)
T6068 025:342.168   Data:  70 47
T6068 025:342.190 - 1.036ms returns 2 (0x2)
T6068 025:342.208 JLINK_HasError()
T6068 025:342.222 JLINK_HasError()
T6068 025:342.238 JLINK_Go()
T6068 025:342.826   CPU_ReadMem(4 bytes @ 0xE0001000)
T6068 025:343.930 - 1.692ms
T6068 025:444.740 JLINK_HasError()
T6068 025:444.790 JLINK_IsHalted()
T6068 025:445.526 - 0.735ms returns FALSE
T6068 025:545.656 JLINK_HasError()
T6068 025:545.742 JLINK_IsHalted()
T6068 025:546.302 - 0.559ms returns FALSE
T6068 025:647.358 JLINK_HasError()
T6068 025:647.472 JLINK_IsHalted()
T6068 025:648.566 - 1.091ms returns FALSE
T6068 025:748.852 JLINK_HasError()
T6068 025:748.970 JLINK_IsHalted()
T6068 025:750.204 - 1.232ms returns FALSE
T6068 025:851.370 JLINK_HasError()
T6068 025:851.420 JLINK_HasError()
T6068 025:851.434 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 025:851.460   CPU_ReadMem(4 bytes @ 0xE0001004)
T6068 025:852.054   Data:  A4 BA 12 98
T6068 025:852.078   Debug reg: DWT_CYCCNT
T6068 025:852.100 - 0.664ms returns 1 (0x1)
T6068 025:852.162 JLINK_IsHalted()
T6068 025:852.754 - 0.592ms returns FALSE
T6068 025:952.888 JLINK_HasError()
T6068 025:952.938 JLINK_IsHalted()
T6068 025:953.532 - 0.595ms returns FALSE
T6068 026:054.382 JLINK_HasError()
T6068 026:054.436 JLINK_IsHalted()
T6068 026:055.012 - 0.573ms returns FALSE
T6068 026:155.060 JLINK_HasError()
T6068 026:155.116 JLINK_IsHalted()
T6068 026:155.636 - 0.517ms returns FALSE
T6068 026:256.200 JLINK_HasError()
T6068 026:256.250 JLINK_IsHalted()
T6068 026:256.796 - 0.547ms returns FALSE
T6068 026:357.430 JLINK_HasError()
T6068 026:357.554 JLINK_IsHalted()
T6068 026:358.570 - 1.015ms returns FALSE
T6068 026:458.714 JLINK_HasError()
T6068 026:458.768 JLINK_HasError()
T6068 026:458.784 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 026:458.812   Data:  A4 BA 12 98
T6068 026:458.836   Debug reg: DWT_CYCCNT
T6068 026:458.858 - 0.073ms returns 1 (0x1)
T6068 026:459.032 JLINK_IsHalted()
T6068 026:459.652 - 0.620ms returns FALSE
T6068 026:561.382 JLINK_HasError()
T6068 026:561.494 JLINK_IsHalted()
T6068 026:562.468 - 0.971ms returns FALSE
T6068 026:662.720 JLINK_HasError()
T6068 026:662.844 JLINK_IsHalted()
T6068 026:663.988 - 1.141ms returns FALSE
T6068 026:764.240 JLINK_HasError()
T6068 026:764.294 JLINK_Halt()
T6068 026:767.308 - 3.012ms returns 0x00
T6068 026:767.330 JLINK_IsHalted()
T6068 026:767.344 - 0.014ms returns TRUE
T6068 026:767.358 JLINK_IsHalted()
T6068 026:767.372 - 0.014ms returns TRUE
T6068 026:767.388 JLINK_IsHalted()
T6068 026:767.402 - 0.013ms returns TRUE
T6068 026:767.416 JLINK_HasError()
T6068 026:767.432 JLINK_ReadReg(R15 (PC))
T6068 026:767.446 - 0.015ms returns 0x0802B5C0
T6068 026:767.462 JLINK_ReadReg(XPSR)
T6068 026:767.476 - 0.014ms returns 0x01000000
T6068 026:767.492 JLINK_HasError()
T6068 026:767.508 JLINK_HasError()
T6068 026:767.524 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T6068 026:767.542   CPU_ReadMem(4 bytes @ 0xE000ED30)
T6068 026:768.046   Data:  01 00 00 00
T6068 026:768.068 - 0.545ms returns 1 (0x1)
T6068 026:768.084 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T6068 026:768.100   CPU_ReadMem(4 bytes @ 0xE0001028)
T6068 026:768.650   Data:  00 00 00 00
T6068 026:768.670   Debug reg: DWT_FUNC[0]
T6068 026:768.692 - 0.608ms returns 1 (0x1)
T6068 026:768.708 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T6068 026:768.724   CPU_ReadMem(4 bytes @ 0xE0001038)
T6068 026:769.274   Data:  00 02 00 00
T6068 026:769.298   Debug reg: DWT_FUNC[1]
T6068 026:769.318 - 0.610ms returns 1 (0x1)
T6068 026:769.334 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T6068 026:769.350   CPU_ReadMem(4 bytes @ 0xE0001048)
T6068 026:769.876   Data:  00 00 00 00
T6068 026:769.900   Debug reg: DWT_FUNC[2]
T6068 026:769.920 - 0.584ms returns 1 (0x1)
T6068 026:769.936 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T6068 026:769.952   CPU_ReadMem(4 bytes @ 0xE0001058)
T6068 026:770.530   Data:  00 00 00 00
T6068 026:770.556   Debug reg: DWT_FUNC[3]
T6068 026:770.576 - 0.639ms returns 1 (0x1)
T6068 026:770.694 JLINK_HasError()
T6068 026:770.712 JLINK_ReadReg(R0)
T6068 026:770.728 - 0.015ms returns 0x200000F4
T6068 026:770.742 JLINK_ReadReg(R1)
T6068 026:770.756 - 0.014ms returns 0x0803D961
T6068 026:770.772 JLINK_ReadReg(R2)
T6068 026:770.788 - 0.014ms returns 0x00001F90
T6068 026:770.802 JLINK_ReadReg(R3)
T6068 026:770.816 - 0.014ms returns 0x00000000
T6068 026:770.830 JLINK_ReadReg(R4)
T6068 026:770.844 - 0.014ms returns 0x08040534
T6068 026:770.860 JLINK_ReadReg(R5)
T6068 026:770.874 - 0.014ms returns 0x08040534
T6068 026:770.888 JLINK_ReadReg(R6)
T6068 026:770.902 - 0.014ms returns 0x00000000
T6068 026:770.916 JLINK_ReadReg(R7)
T6068 026:770.932 - 0.014ms returns 0x00000000
T6068 026:770.946 JLINK_ReadReg(R8)
T6068 026:770.960 - 0.014ms returns 0x00000000
T6068 026:770.976 JLINK_ReadReg(R9)
T6068 026:770.990 - 0.014ms returns 0x00000000
T6068 026:771.004 JLINK_ReadReg(R10)
T6068 026:771.020 - 0.014ms returns 0x00000000
T6068 026:771.034 JLINK_ReadReg(R11)
T6068 026:771.050 - 0.014ms returns 0x00000000
T6068 026:771.064 JLINK_ReadReg(R12)
T6068 026:771.078 - 0.014ms returns 0x00000000
T6068 026:771.092 JLINK_ReadReg(R13 (SP))
T6068 026:771.108 - 0.014ms returns 0x20015870
T6068 026:771.122 JLINK_ReadReg(R14)
T6068 026:771.136 - 0.014ms returns 0x0802B5BD
T6068 026:771.150 JLINK_ReadReg(R15 (PC))
T6068 026:771.168 - 0.017ms returns 0x0802B5C0
T6068 026:771.188 JLINK_ReadReg(XPSR)
T6068 026:771.208 - 0.020ms returns 0x01000000
T6068 026:771.228 JLINK_ReadReg(MSP)
T6068 026:771.244 - 0.014ms returns 0x20015870
T6068 026:771.258 JLINK_ReadReg(PSP)
T6068 026:771.272 - 0.013ms returns 0x00000000
T6068 026:771.286 JLINK_ReadReg(CFBP)
T6068 026:771.300 - 0.014ms returns 0x00000000
T6068 026:771.316 JLINK_ReadReg(FPSCR)
T6068 026:775.802 - 4.484ms returns 0x00000000
T6068 026:775.848 JLINK_ReadReg(FPS0)
T6068 026:775.862 - 0.014ms returns 0x00000000
T6068 026:775.876 JLINK_ReadReg(FPS1)
T6068 026:775.892 - 0.013ms returns 0x00000000
T6068 026:775.906 JLINK_ReadReg(FPS2)
T6068 026:775.920 - 0.014ms returns 0x00000000
T6068 026:775.934 JLINK_ReadReg(FPS3)
T6068 026:775.948 - 0.013ms returns 0x00000000
T6068 026:775.964 JLINK_ReadReg(FPS4)
T6068 026:775.976 - 0.014ms returns 0x00000000
T6068 026:775.990 JLINK_ReadReg(FPS5)
T6068 026:776.004 - 0.014ms returns 0x00000000
T6068 026:776.020 JLINK_ReadReg(FPS6)
T6068 026:776.034 - 0.014ms returns 0x00000000
T6068 026:776.048 JLINK_ReadReg(FPS7)
T6068 026:776.064 - 0.015ms returns 0x00000000
T6068 026:776.080 JLINK_ReadReg(FPS8)
T6068 026:776.094 - 0.014ms returns 0x00000000
T6068 026:776.112 JLINK_ReadReg(FPS9)
T6068 026:776.132 - 0.019ms returns 0x00000000
T6068 026:776.150 JLINK_ReadReg(FPS10)
T6068 026:776.170 - 0.018ms returns 0x00000000
T6068 026:776.188 JLINK_ReadReg(FPS11)
T6068 026:776.206 - 0.017ms returns 0x00000000
T6068 026:776.224 JLINK_ReadReg(FPS12)
T6068 026:776.244 - 0.018ms returns 0x00000000
T6068 026:776.262 JLINK_ReadReg(FPS13)
T6068 026:776.282 - 0.018ms returns 0x00000000
T6068 026:776.300 JLINK_ReadReg(FPS14)
T6068 026:776.314 - 0.014ms returns 0x00000000
T6068 026:776.328 JLINK_ReadReg(FPS15)
T6068 026:776.342 - 0.014ms returns 0x00000000
T6068 026:776.360 JLINK_ReadReg(FPS16)
T6068 026:776.374 - 0.014ms returns 0x00000000
T6068 026:776.390 JLINK_ReadReg(FPS17)
T6068 026:776.404 - 0.014ms returns 0x00000000
T6068 026:776.418 JLINK_ReadReg(FPS18)
T6068 026:776.432 - 0.014ms returns 0x00000000
T6068 026:776.446 JLINK_ReadReg(FPS19)
T6068 026:776.460 - 0.014ms returns 0x00000000
T6068 026:776.476 JLINK_ReadReg(FPS20)
T6068 026:776.490 - 0.013ms returns 0x00000000
T6068 026:776.504 JLINK_ReadReg(FPS21)
T6068 026:776.518 - 0.014ms returns 0x00000000
T6068 026:776.532 JLINK_ReadReg(FPS22)
T6068 026:776.548 - 0.014ms returns 0x00000000
T6068 026:776.562 JLINK_ReadReg(FPS23)
T6068 026:776.576 - 0.014ms returns 0x00000000
T6068 026:776.590 JLINK_ReadReg(FPS24)
T6068 026:776.604 - 0.014ms returns 0x00000000
T6068 026:776.620 JLINK_ReadReg(FPS25)
T6068 026:776.636 - 0.016ms returns 0x00000000
T6068 026:776.650 JLINK_ReadReg(FPS26)
T6068 026:776.664 - 0.014ms returns 0x00000000
T6068 026:776.678 JLINK_ReadReg(FPS27)
T6068 026:776.692 - 0.014ms returns 0x00000000
T6068 026:776.708 JLINK_ReadReg(FPS28)
T6068 026:776.722 - 0.014ms returns 0x00000000
T6068 026:776.736 JLINK_ReadReg(FPS29)
T6068 026:776.750 - 0.014ms returns 0x00000000
T6068 026:776.764 JLINK_ReadReg(FPS30)
T6068 026:776.780 - 0.013ms returns 0x00000000
T6068 026:776.794 JLINK_ReadReg(FPS31)
T6068 026:776.808 - 0.014ms returns 0x00000000
T316C 026:776.888 JLINK_HasError()
T316C 026:776.916 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T316C 026:776.934   CPU_ReadMem(4 bytes @ 0xE0001004)
T316C 026:777.548   Data:  AF CF 36 A1
T316C 026:777.572   Debug reg: DWT_CYCCNT
T316C 026:777.592 - 0.677ms returns 1 (0x1)
T316C 026:777.700 JLINK_ReadMemEx(0x0802B5C0, 0x3C Bytes, Flags = 0x02000000)
T316C 026:777.720   CPU_ReadMem(64 bytes @ 0x0802B5C0)
T316C 026:778.680    -- Updating C cache (64 bytes @ 0x0802B5C0)
T316C 026:778.712    -- Read from C cache (60 bytes @ 0x0802B5C0)
T316C 026:778.736   Data:  9D F8 0D 00 88 47 01 98 C1 69 9D F8 0C 00 88 47 ...
T316C 026:778.758 - 1.056ms returns 60 (0x3C)
T316C 026:778.776 JLINK_ReadMemEx(0x0802B5C0, 0x2 Bytes, Flags = 0x02000000)
T316C 026:778.792    -- Read from C cache (2 bytes @ 0x0802B5C0)
T316C 026:778.812   Data:  9D F8
T316C 026:778.836 - 0.059ms returns 2 (0x2)
T316C 026:778.852 JLINK_ReadMemEx(0x0802B5C2, 0x2 Bytes, Flags = 0x02000000)
T316C 026:778.866    -- Read from C cache (2 bytes @ 0x0802B5C2)
T316C 026:778.886   Data:  0D 00
T316C 026:778.908 - 0.056ms returns 2 (0x2)
T6068 027:647.066 JLINK_ReadMemEx(0x0802B5C0, 0x2 Bytes, Flags = 0x02000000)
T6068 027:647.140    -- Read from C cache (2 bytes @ 0x0802B5C0)
T6068 027:647.174   Data:  9D F8
T6068 027:647.216 - 0.149ms returns 2 (0x2)
T6068 027:647.244 JLINK_HasError()
T6068 027:647.276 JLINK_HasError()
T6068 027:647.308 JLINK_Go()
T6068 027:648.186   CPU_ReadMem(4 bytes @ 0xE0001000)
T6068 027:649.700 - 2.390ms
T6068 027:749.860 JLINK_HasError()
T6068 027:749.908 JLINK_IsHalted()
T6068 027:750.560 - 0.651ms returns FALSE
T6068 027:851.516 JLINK_HasError()
T6068 027:851.594 JLINK_IsHalted()
T6068 027:852.292 - 0.696ms returns FALSE
T6068 027:952.416 JLINK_HasError()
T6068 027:952.464 JLINK_IsHalted()
T6068 027:953.028 - 0.563ms returns FALSE
T6068 028:053.184 JLINK_HasError()
T6068 028:053.244 JLINK_IsHalted()
T6068 028:054.266 - 1.016ms returns FALSE
T6068 028:155.050 JLINK_HasError()
T6068 028:155.164 JLINK_IsHalted()
T6068 028:156.100 - 0.934ms returns FALSE
T6068 028:257.684 JLINK_HasError()
T6068 028:257.802 JLINK_HasError()
T6068 028:257.856 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 028:257.932   CPU_ReadMem(4 bytes @ 0xE0001004)
T6068 028:258.714   Data:  6C 25 50 A7
T6068 028:258.752   Debug reg: DWT_CYCCNT
T6068 028:258.774 - 0.917ms returns 1 (0x1)
T6068 028:259.172 JLINK_IsHalted()
T6068 028:259.914 - 0.739ms returns FALSE
T6068 028:360.042 JLINK_HasError()
T6068 028:360.092 JLINK_IsHalted()
T6068 028:360.596 - 0.500ms returns FALSE
T6068 028:460.676 JLINK_HasError()
T6068 028:460.724 JLINK_IsHalted()
T6068 028:461.274 - 0.551ms returns FALSE
T6068 028:561.658 JLINK_HasError()
T6068 028:561.712 JLINK_IsHalted()
T6068 028:562.698 - 0.983ms returns FALSE
T6068 028:663.082 JLINK_HasError()
T6068 028:663.204 JLINK_IsHalted()
T6068 028:664.144 - 0.939ms returns FALSE
T6068 028:764.410 JLINK_HasError()
T6068 028:764.516 JLINK_Halt()
T6068 028:768.372 - 3.855ms returns 0x00
T6068 028:768.476 JLINK_IsHalted()
T6068 028:768.524 - 0.048ms returns TRUE
T6068 028:768.634 JLINK_IsHalted()
T6068 028:768.682 - 0.048ms returns TRUE
T6068 028:768.730 JLINK_IsHalted()
T6068 028:768.774 - 0.046ms returns TRUE
T6068 028:768.826 JLINK_HasError()
T6068 028:768.876 JLINK_ReadReg(R15 (PC))
T6068 028:768.928 - 0.051ms returns 0x0802CB6A
T6068 028:768.976 JLINK_ReadReg(XPSR)
T6068 028:769.024 - 0.047ms returns 0x01000000
T6068 028:769.088 JLINK_HasError()
T6068 028:769.142 JLINK_HasError()
T6068 028:769.190 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T6068 028:769.252   CPU_ReadMem(4 bytes @ 0xE000ED30)
T6068 028:770.202   Data:  01 00 00 00
T6068 028:770.316 - 1.124ms returns 1 (0x1)
T6068 028:770.372 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T6068 028:770.432   CPU_ReadMem(4 bytes @ 0xE0001028)
T6068 028:771.376   Data:  00 00 00 00
T6068 028:771.504   Debug reg: DWT_FUNC[0]
T6068 028:771.600 - 1.226ms returns 1 (0x1)
T6068 028:771.654 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T6068 028:771.716   CPU_ReadMem(4 bytes @ 0xE0001038)
T6068 028:772.404   Data:  00 02 00 00
T6068 028:772.496   Debug reg: DWT_FUNC[1]
T6068 028:772.588 - 0.932ms returns 1 (0x1)
T6068 028:772.646 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T6068 028:772.678   CPU_ReadMem(4 bytes @ 0xE0001048)
T6068 028:773.258   Data:  00 00 00 00
T6068 028:773.292   Debug reg: DWT_FUNC[2]
T6068 028:773.316 - 0.669ms returns 1 (0x1)
T6068 028:773.334 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T6068 028:773.356   CPU_ReadMem(4 bytes @ 0xE0001058)
T6068 028:773.882   Data:  00 00 00 00
T6068 028:773.910   Debug reg: DWT_FUNC[3]
T6068 028:773.932 - 0.598ms returns 1 (0x1)
T6068 028:774.036 JLINK_HasError()
T6068 028:774.054 JLINK_ReadReg(R0)
T6068 028:774.070 - 0.016ms returns 0x000000B0
T6068 028:774.084 JLINK_ReadReg(R1)
T6068 028:774.100 - 0.014ms returns 0x000000B0
T6068 028:774.112 JLINK_ReadReg(R2)
T6068 028:774.124 - 0.014ms returns 0x00001F90
T6068 028:774.140 JLINK_ReadReg(R3)
T6068 028:774.154 - 0.014ms returns 0x00000000
T6068 028:774.170 JLINK_ReadReg(R4)
T6068 028:774.182 - 0.014ms returns 0x08040534
T6068 028:774.198 JLINK_ReadReg(R5)
T6068 028:774.212 - 0.013ms returns 0x08040534
T6068 028:774.228 JLINK_ReadReg(R6)
T6068 028:774.240 - 0.014ms returns 0x00000000
T6068 028:774.256 JLINK_ReadReg(R7)
T6068 028:774.268 - 0.013ms returns 0x00000000
T6068 028:774.284 JLINK_ReadReg(R8)
T6068 028:774.298 - 0.014ms returns 0x00000000
T6068 028:774.314 JLINK_ReadReg(R9)
T6068 028:774.326 - 0.014ms returns 0x00000000
T6068 028:774.342 JLINK_ReadReg(R10)
T6068 028:774.356 - 0.014ms returns 0x00000000
T6068 028:774.368 JLINK_ReadReg(R11)
T6068 028:774.384 - 0.014ms returns 0x00000000
T6068 028:774.396 JLINK_ReadReg(R12)
T6068 028:774.412 - 0.014ms returns 0x00000000
T6068 028:774.426 JLINK_ReadReg(R13 (SP))
T6068 028:774.442 - 0.014ms returns 0x20015888
T6068 028:774.454 JLINK_ReadReg(R14)
T6068 028:774.470 - 0.014ms returns 0x0802B5E9
T6068 028:774.484 JLINK_ReadReg(R15 (PC))
T6068 028:774.500 - 0.014ms returns 0x0802CB6A
T6068 028:774.512 JLINK_ReadReg(XPSR)
T6068 028:774.528 - 0.013ms returns 0x01000000
T6068 028:774.540 JLINK_ReadReg(MSP)
T6068 028:774.554 - 0.013ms returns 0x20015888
T6068 028:774.570 JLINK_ReadReg(PSP)
T6068 028:774.582 - 0.014ms returns 0x00000000
T6068 028:774.598 JLINK_ReadReg(CFBP)
T6068 028:774.612 - 0.014ms returns 0x00000000
T6068 028:774.628 JLINK_ReadReg(FPSCR)
T6068 028:779.318 - 4.690ms returns 0x00000000
T6068 028:779.338 JLINK_ReadReg(FPS0)
T6068 028:779.354 - 0.014ms returns 0x00000000
T6068 028:779.366 JLINK_ReadReg(FPS1)
T6068 028:779.382 - 0.014ms returns 0x00000000
T6068 028:779.396 JLINK_ReadReg(FPS2)
T6068 028:779.412 - 0.014ms returns 0x00000000
T6068 028:779.424 JLINK_ReadReg(FPS3)
T6068 028:779.436 - 0.013ms returns 0x00000000
T6068 028:779.452 JLINK_ReadReg(FPS4)
T6068 028:779.466 - 0.014ms returns 0x00000000
T6068 028:779.482 JLINK_ReadReg(FPS5)
T6068 028:779.494 - 0.014ms returns 0x00000000
T6068 028:779.510 JLINK_ReadReg(FPS6)
T6068 028:779.524 - 0.014ms returns 0x00000000
T6068 028:779.540 JLINK_ReadReg(FPS7)
T6068 028:779.552 - 0.013ms returns 0x00000000
T6068 028:779.564 JLINK_ReadReg(FPS8)
T6068 028:779.580 - 0.014ms returns 0x00000000
T6068 028:779.594 JLINK_ReadReg(FPS9)
T6068 028:779.610 - 0.013ms returns 0x00000000
T6068 028:779.622 JLINK_ReadReg(FPS10)
T6068 028:779.638 - 0.016ms returns 0x00000000
T6068 028:779.654 JLINK_ReadReg(FPS11)
T6068 028:779.668 - 0.014ms returns 0x00000000
T6068 028:779.702 JLINK_ReadReg(FPS12)
T6068 028:779.716 - 0.014ms returns 0x00000000
T6068 028:779.732 JLINK_ReadReg(FPS13)
T6068 028:779.744 - 0.014ms returns 0x00000000
T6068 028:779.760 JLINK_ReadReg(FPS14)
T6068 028:779.772 - 0.014ms returns 0x00000000
T6068 028:779.788 JLINK_ReadReg(FPS15)
T6068 028:779.802 - 0.014ms returns 0x00000000
T6068 028:779.818 JLINK_ReadReg(FPS16)
T6068 028:779.830 - 0.014ms returns 0x00000000
T6068 028:779.846 JLINK_ReadReg(FPS17)
T6068 028:779.860 - 0.013ms returns 0x00000000
T6068 028:779.876 JLINK_ReadReg(FPS18)
T6068 028:779.888 - 0.014ms returns 0x00000000
T6068 028:779.904 JLINK_ReadReg(FPS19)
T6068 028:779.916 - 0.014ms returns 0x00000000
T6068 028:779.932 JLINK_ReadReg(FPS20)
T6068 028:779.946 - 0.014ms returns 0x00000000
T6068 028:779.958 JLINK_ReadReg(FPS21)
T6068 028:779.974 - 0.014ms returns 0x00000000
T6068 028:779.988 JLINK_ReadReg(FPS22)
T6068 028:780.004 - 0.014ms returns 0x00000000
T6068 028:780.016 JLINK_ReadReg(FPS23)
T6068 028:780.032 - 0.014ms returns 0x00000000
T6068 028:780.044 JLINK_ReadReg(FPS24)
T6068 028:780.060 - 0.014ms returns 0x00000000
T6068 028:780.074 JLINK_ReadReg(FPS25)
T6068 028:780.090 - 0.014ms returns 0x00000000
T6068 028:780.102 JLINK_ReadReg(FPS26)
T6068 028:780.118 - 0.014ms returns 0x00000000
T6068 028:780.132 JLINK_ReadReg(FPS27)
T6068 028:780.144 - 0.014ms returns 0x00000000
T6068 028:780.160 JLINK_ReadReg(FPS28)
T6068 028:780.172 - 0.014ms returns 0x00000000
T6068 028:780.188 JLINK_ReadReg(FPS29)
T6068 028:780.202 - 0.014ms returns 0x00000000
T6068 028:780.218 JLINK_ReadReg(FPS30)
T6068 028:780.230 - 0.014ms returns 0x00000000
T6068 028:780.246 JLINK_ReadReg(FPS31)
T6068 028:780.260 - 0.014ms returns 0x00000000
T316C 028:780.388 JLINK_HasError()
T316C 028:780.410 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T316C 028:780.432   CPU_ReadMem(4 bytes @ 0xE0001004)
T316C 028:780.982   Data:  C2 FE 62 AC
T316C 028:781.004   Debug reg: DWT_CYCCNT
T316C 028:781.024 - 0.614ms returns 1 (0x1)
T316C 028:781.136 JLINK_ReadMemEx(0x0802CB6A, 0x2 Bytes, Flags = 0x02000000)
T316C 028:781.156   CPU_ReadMem(64 bytes @ 0x0802CB40)
T316C 028:782.124    -- Updating C cache (64 bytes @ 0x0802CB40)
T316C 028:782.148    -- Read from C cache (2 bytes @ 0x0802CB6A)
T316C 028:782.170   Data:  08 B1
T316C 028:782.188 - 1.055ms returns 2 (0x2)
T316C 028:782.204 JLINK_ReadMemEx(0x0802CB6C, 0x3C Bytes, Flags = 0x02000000)
T316C 028:782.220   CPU_ReadMem(64 bytes @ 0x0802CB80)
T316C 028:783.124    -- Updating C cache (64 bytes @ 0x0802CB80)
T316C 028:783.146    -- Read from C cache (60 bytes @ 0x0802CB6C)
T316C 028:783.164   Data:  FF E7 F4 E7 9D F8 06 10 4F F4 02 70 00 EB 41 10 ...
T316C 028:783.188 - 0.980ms returns 60 (0x3C)
T316C 028:783.204 JLINK_ReadMemEx(0x0802CB6C, 0x2 Bytes, Flags = 0x02000000)
T316C 028:783.216    -- Read from C cache (2 bytes @ 0x0802CB6C)
T316C 028:783.238   Data:  FF E7
T316C 028:783.258 - 0.055ms returns 2 (0x2)
T6068 029:455.260 JLINK_ReadMemEx(0x0802CB6A, 0x2 Bytes, Flags = 0x02000000)
T6068 029:455.338    -- Read from C cache (2 bytes @ 0x0802CB6A)
T6068 029:455.372   Data:  08 B1
T6068 029:455.412 - 0.149ms returns 2 (0x2)
T6068 029:455.436 JLINK_HasError()
T6068 029:455.466 JLINK_HasError()
T6068 029:455.494 JLINK_Go()
T6068 029:456.292   CPU_ReadMem(4 bytes @ 0xE0001000)
T6068 029:457.756 - 2.265ms
T6068 029:557.924 JLINK_HasError()
T6068 029:558.042 JLINK_IsHalted()
T6068 029:558.874 - 0.830ms returns FALSE
T6068 029:658.970 JLINK_HasError()
T6068 029:659.028 JLINK_IsHalted()
T6068 029:659.616 - 0.589ms returns FALSE
T6068 029:759.828 JLINK_HasError()
T6068 029:759.882 JLINK_IsHalted()
T6068 029:760.772 - 0.887ms returns FALSE
T6068 029:861.024 JLINK_HasError()
T6068 029:861.146 JLINK_IsHalted()
T6068 029:861.830 - 0.683ms returns FALSE
T6068 029:961.884 JLINK_HasError()
T6068 029:961.942 JLINK_IsHalted()
T6068 029:962.708 - 0.764ms returns FALSE
T6068 030:062.890 JLINK_HasError()
T6068 030:062.948 JLINK_HasError()
T6068 030:062.966 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 030:062.996   CPU_ReadMem(4 bytes @ 0xE0001004)
T6068 030:063.708   Data:  12 73 74 B2
T6068 030:063.734   Debug reg: DWT_CYCCNT
T6068 030:063.754 - 0.786ms returns 1 (0x1)
T6068 030:063.872 JLINK_IsHalted()
T6068 030:064.522 - 0.650ms returns FALSE
T6068 030:165.292 JLINK_HasError()
T6068 030:165.350 JLINK_IsHalted()
T6068 030:166.272 - 0.921ms returns FALSE
T6068 030:267.414 JLINK_HasError()
T6068 030:267.498 JLINK_IsHalted()
T6068 030:268.220 - 0.722ms returns FALSE
T6068 030:368.474 JLINK_HasError()
T6068 030:368.576 JLINK_IsHalted()
T6068 030:369.620 - 1.040ms returns FALSE
T6068 030:469.948 JLINK_HasError()
T6068 030:470.000 JLINK_Halt()
T6068 030:473.028 - 3.025ms returns 0x00
T6068 030:473.050 JLINK_IsHalted()
T6068 030:473.062 - 0.014ms returns TRUE
T6068 030:473.078 JLINK_IsHalted()
T6068 030:473.094 - 0.014ms returns TRUE
T6068 030:473.108 JLINK_IsHalted()
T6068 030:473.124 - 0.014ms returns TRUE
T6068 030:473.140 JLINK_HasError()
T6068 030:473.156 JLINK_ReadReg(R15 (PC))
T6068 030:473.172 - 0.016ms returns 0x0803D968
T6068 030:473.188 JLINK_ReadReg(XPSR)
T6068 030:473.204 - 0.014ms returns 0x01000000
T6068 030:473.244 JLINK_HasError()
T6068 030:473.260 JLINK_HasError()
T6068 030:473.276 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T6068 030:473.296   CPU_ReadMem(4 bytes @ 0xE000ED30)
T6068 030:474.004   Data:  01 00 00 00
T6068 030:474.028 - 0.751ms returns 1 (0x1)
T6068 030:474.044 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T6068 030:474.060   CPU_ReadMem(4 bytes @ 0xE0001028)
T6068 030:474.604   Data:  00 00 00 00
T6068 030:474.628   Debug reg: DWT_FUNC[0]
T6068 030:474.646 - 0.603ms returns 1 (0x1)
T6068 030:474.662 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T6068 030:474.678   CPU_ReadMem(4 bytes @ 0xE0001038)
T6068 030:475.226   Data:  00 02 00 00
T6068 030:475.248   Debug reg: DWT_FUNC[1]
T6068 030:475.270 - 0.607ms returns 1 (0x1)
T6068 030:475.284 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T6068 030:475.300   CPU_ReadMem(4 bytes @ 0xE0001048)
T6068 030:475.872   Data:  00 00 00 00
T6068 030:475.894   Debug reg: DWT_FUNC[2]
T6068 030:475.914 - 0.630ms returns 1 (0x1)
T6068 030:475.930 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T6068 030:475.946   CPU_ReadMem(4 bytes @ 0xE0001058)
T6068 030:476.518   Data:  00 00 00 00
T6068 030:476.540   Debug reg: DWT_FUNC[3]
T6068 030:476.560 - 0.630ms returns 1 (0x1)
T6068 030:476.656 JLINK_HasError()
T6068 030:476.676 JLINK_ReadReg(R0)
T6068 030:476.688 - 0.014ms returns 0x00000000
T6068 030:476.704 JLINK_ReadReg(R1)
T6068 030:476.716 - 0.014ms returns 0x0803D961
T6068 030:476.732 JLINK_ReadReg(R2)
T6068 030:476.746 - 0.014ms returns 0x00001F90
T6068 030:476.762 JLINK_ReadReg(R3)
T6068 030:476.778 - 0.014ms returns 0x00000000
T6068 030:476.790 JLINK_ReadReg(R4)
T6068 030:476.806 - 0.014ms returns 0x08040534
T6068 030:476.820 JLINK_ReadReg(R5)
T6068 030:476.836 - 0.014ms returns 0x08040534
T6068 030:476.848 JLINK_ReadReg(R6)
T6068 030:476.860 - 0.014ms returns 0x00000000
T6068 030:476.876 JLINK_ReadReg(R7)
T6068 030:476.890 - 0.014ms returns 0x00000000
T6068 030:476.906 JLINK_ReadReg(R8)
T6068 030:476.918 - 0.014ms returns 0x00000000
T6068 030:476.934 JLINK_ReadReg(R9)
T6068 030:476.948 - 0.014ms returns 0x00000000
T6068 030:476.964 JLINK_ReadReg(R10)
T6068 030:476.976 - 0.014ms returns 0x00000000
T6068 030:476.992 JLINK_ReadReg(R11)
T6068 030:477.004 - 0.014ms returns 0x00000000
T6068 030:477.020 JLINK_ReadReg(R12)
T6068 030:477.034 - 0.013ms returns 0x00000000
T6068 030:477.046 JLINK_ReadReg(R13 (SP))
T6068 030:477.062 - 0.014ms returns 0x20015868
T6068 030:477.076 JLINK_ReadReg(R14)
T6068 030:477.092 - 0.014ms returns 0x0802B5BD
T6068 030:477.108 JLINK_ReadReg(R15 (PC))
T6068 030:477.120 - 0.014ms returns 0x0803D968
T6068 030:477.136 JLINK_ReadReg(XPSR)
T6068 030:477.148 - 0.014ms returns 0x01000000
T6068 030:477.164 JLINK_ReadReg(MSP)
T6068 030:477.178 - 0.014ms returns 0x20015868
T6068 030:477.194 JLINK_ReadReg(PSP)
T6068 030:477.206 - 0.014ms returns 0x00000000
T6068 030:477.226 JLINK_ReadReg(CFBP)
T6068 030:477.242 - 0.014ms returns 0x00000000
T6068 030:477.254 JLINK_ReadReg(FPSCR)
T6068 030:481.724 - 4.468ms returns 0x00000000
T6068 030:481.754 JLINK_ReadReg(FPS0)
T6068 030:481.766 - 0.015ms returns 0x00000000
T6068 030:481.782 JLINK_ReadReg(FPS1)
T6068 030:481.796 - 0.014ms returns 0x00000000
T6068 030:481.812 JLINK_ReadReg(FPS2)
T6068 030:481.824 - 0.014ms returns 0x00000000
T6068 030:481.840 JLINK_ReadReg(FPS3)
T6068 030:481.852 - 0.014ms returns 0x00000000
T6068 030:481.868 JLINK_ReadReg(FPS4)
T6068 030:481.882 - 0.014ms returns 0x00000000
T6068 030:481.898 JLINK_ReadReg(FPS5)
T6068 030:481.910 - 0.014ms returns 0x00000000
T6068 030:481.926 JLINK_ReadReg(FPS6)
T6068 030:481.940 - 0.014ms returns 0x00000000
T6068 030:481.956 JLINK_ReadReg(FPS7)
T6068 030:481.972 - 0.014ms returns 0x00000000
T6068 030:481.984 JLINK_ReadReg(FPS8)
T6068 030:481.996 - 0.014ms returns 0x00000000
T6068 030:482.012 JLINK_ReadReg(FPS9)
T6068 030:482.026 - 0.014ms returns 0x00000000
T6068 030:482.042 JLINK_ReadReg(FPS10)
T6068 030:482.054 - 0.014ms returns 0x00000000
T6068 030:482.070 JLINK_ReadReg(FPS11)
T6068 030:482.086 - 0.014ms returns 0x00000000
T6068 030:482.100 JLINK_ReadReg(FPS12)
T6068 030:482.112 - 0.014ms returns 0x00000000
T6068 030:482.128 JLINK_ReadReg(FPS13)
T6068 030:482.144 - 0.014ms returns 0x00000000
T6068 030:482.156 JLINK_ReadReg(FPS14)
T6068 030:482.172 - 0.014ms returns 0x00000000
T6068 030:482.186 JLINK_ReadReg(FPS15)
T6068 030:482.202 - 0.014ms returns 0x00000000
T6068 030:482.214 JLINK_ReadReg(FPS16)
T6068 030:482.228 - 0.014ms returns 0x00000000
T6068 030:482.244 JLINK_ReadReg(FPS17)
T6068 030:482.260 - 0.014ms returns 0x00000000
T6068 030:482.272 JLINK_ReadReg(FPS18)
T6068 030:482.288 - 0.014ms returns 0x00000000
T6068 030:482.300 JLINK_ReadReg(FPS19)
T6068 030:482.316 - 0.014ms returns 0x00000000
T6068 030:482.330 JLINK_ReadReg(FPS20)
T6068 030:482.342 - 0.014ms returns 0x00000000
T6068 030:482.358 JLINK_ReadReg(FPS21)
T6068 030:482.374 - 0.014ms returns 0x00000000
T6068 030:482.388 JLINK_ReadReg(FPS22)
T6068 030:482.400 - 0.014ms returns 0x00000000
T6068 030:482.416 JLINK_ReadReg(FPS23)
T6068 030:482.432 - 0.014ms returns 0x00000000
T6068 030:482.444 JLINK_ReadReg(FPS24)
T6068 030:482.460 - 0.014ms returns 0x00000000
T6068 030:482.476 JLINK_ReadReg(FPS25)
T6068 030:482.490 - 0.014ms returns 0x00000000
T6068 030:482.506 JLINK_ReadReg(FPS26)
T6068 030:482.518 - 0.014ms returns 0x00000000
T6068 030:482.534 JLINK_ReadReg(FPS27)
T6068 030:482.548 - 0.014ms returns 0x00000000
T6068 030:482.564 JLINK_ReadReg(FPS28)
T6068 030:482.576 - 0.014ms returns 0x00000000
T6068 030:482.592 JLINK_ReadReg(FPS29)
T6068 030:482.604 - 0.014ms returns 0x00000000
T6068 030:482.620 JLINK_ReadReg(FPS30)
T6068 030:482.636 - 0.014ms returns 0x00000000
T6068 030:482.650 JLINK_ReadReg(FPS31)
T6068 030:482.666 - 0.014ms returns 0x00000000
T316C 030:482.716 JLINK_HasError()
T316C 030:482.742 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T316C 030:482.762   CPU_ReadMem(4 bytes @ 0xE0001004)
T316C 030:483.270   Data:  46 6D 87 B6
T316C 030:483.308   Debug reg: DWT_CYCCNT
T316C 030:483.328 - 0.586ms returns 1 (0x1)
T316C 030:483.436 JLINK_ReadMemEx(0x0803D968, 0x3C Bytes, Flags = 0x02000000)
T316C 030:483.456   CPU_ReadMem(128 bytes @ 0x0803D940)
T316C 030:484.684    -- Updating C cache (128 bytes @ 0x0803D940)
T316C 030:484.720    -- Read from C cache (60 bytes @ 0x0803D968)
T316C 030:484.742   Data:  9D F8 03 00 01 99 08 70 02 B0 70 47 80 B5 82 B0 ...
T316C 030:484.764 - 1.326ms returns 60 (0x3C)
T316C 030:484.780 JLINK_ReadMemEx(0x0803D968, 0x2 Bytes, Flags = 0x02000000)
T316C 030:484.796    -- Read from C cache (2 bytes @ 0x0803D968)
T316C 030:484.820   Data:  9D F8
T316C 030:484.838 - 0.058ms returns 2 (0x2)
T316C 030:484.900 JLINK_ReadMemEx(0x0803D96A, 0x2 Bytes, Flags = 0x02000000)
T316C 030:484.912    -- Read from C cache (2 bytes @ 0x0803D96A)
T316C 030:484.934   Data:  03 00
T316C 030:484.956 - 0.060ms returns 2 (0x2)
T6068 135:309.792 JLINK_ReadMemEx(0x0803D968, 0x2 Bytes, Flags = 0x02000000)
T6068 135:309.856    -- Read from C cache (2 bytes @ 0x0803D968)
T6068 135:309.888   Data:  9D F8
T6068 135:309.904 - 0.114ms returns 2 (0x2)
T6068 135:309.920 JLINK_HasError()
T6068 135:309.936 JLINK_HasError()
T6068 135:309.952 JLINK_Go()
T6068 135:310.480   CPU_ReadMem(4 bytes @ 0xE0001000)
T6068 135:311.568 - 1.604ms
T6068 135:411.648 JLINK_HasError()
T6068 135:411.712 JLINK_IsHalted()
T6068 135:412.288 - 0.574ms returns FALSE
T6068 135:512.480 JLINK_HasError()
T6068 135:512.528 JLINK_IsHalted()
T6068 135:513.104 - 0.580ms returns FALSE
T6068 135:614.336 JLINK_HasError()
T6068 135:614.384 JLINK_IsHalted()
T6068 135:614.960 - 0.570ms returns FALSE
T6068 135:715.744 JLINK_HasError()
T6068 135:715.792 JLINK_IsHalted()
T6068 135:716.272 - 0.491ms returns FALSE
T6068 135:816.480 JLINK_HasError()
T6068 135:816.528 JLINK_HasError()
T6068 135:816.544 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 135:816.560   CPU_ReadMem(4 bytes @ 0xE0001004)
T6068 135:817.168   Data:  B5 83 97 BB
T6068 135:817.184   Debug reg: DWT_CYCCNT
T6068 135:817.216 - 0.676ms returns 1 (0x1)
T6068 135:817.344 JLINK_IsHalted()
T6068 135:817.888 - 0.532ms returns FALSE
T6068 135:917.984 JLINK_HasError()
T6068 135:918.048 JLINK_IsHalted()
T6068 135:918.656 - 0.608ms returns FALSE
T6068 136:018.768 JLINK_HasError()
T6068 136:018.816 JLINK_IsHalted()
T6068 136:019.392 - 0.576ms returns FALSE
T6068 136:119.712 JLINK_HasError()
T6068 136:119.760 JLINK_IsHalted()
T6068 136:120.320 - 0.568ms returns FALSE
T6068 136:221.056 JLINK_HasError()
T6068 136:221.168 JLINK_IsHalted()
T6068 136:221.888 - 0.720ms returns FALSE
T6068 136:322.656 JLINK_HasError()
T6068 136:322.704 JLINK_Halt()
T6068 136:326.416 - 3.712ms returns 0x00
T6068 136:326.432 JLINK_IsHalted()
T6068 136:326.448 - 0.014ms returns TRUE
T6068 136:326.464 JLINK_IsHalted()
T6068 136:326.480 - 0.014ms returns TRUE
T6068 136:326.496 JLINK_IsHalted()
T6068 136:326.512 - 0.013ms returns TRUE
T6068 136:326.528 JLINK_HasError()
T6068 136:326.544 JLINK_ReadReg(R15 (PC))
T6068 136:326.560 - 0.016ms returns 0x0803D962
T6068 136:326.560 JLINK_ReadReg(XPSR)
T6068 136:326.576 - 0.014ms returns 0x01000000
T6068 136:326.608 JLINK_HasError()
T6068 136:326.624 JLINK_HasError()
T6068 136:326.624 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T6068 136:326.656   CPU_ReadMem(4 bytes @ 0xE000ED30)
T6068 136:327.200   Data:  01 00 00 00
T6068 136:327.216 - 0.582ms returns 1 (0x1)
T6068 136:327.232 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T6068 136:327.248   CPU_ReadMem(4 bytes @ 0xE0001028)
T6068 136:327.824   Data:  00 00 00 00
T6068 136:327.840   Debug reg: DWT_FUNC[0]
T6068 136:327.872 - 0.636ms returns 1 (0x1)
T6068 136:327.888 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T6068 136:327.904   CPU_ReadMem(4 bytes @ 0xE0001038)
T6068 136:328.464   Data:  00 02 00 00
T6068 136:328.480   Debug reg: DWT_FUNC[1]
T6068 136:328.496 - 0.619ms returns 1 (0x1)
T6068 136:328.512 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T6068 136:328.544   CPU_ReadMem(4 bytes @ 0xE0001048)
T6068 136:329.088   Data:  00 00 00 00
T6068 136:329.120   Debug reg: DWT_FUNC[2]
T6068 136:329.120 - 0.612ms returns 1 (0x1)
T6068 136:329.136 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T6068 136:329.168   CPU_ReadMem(4 bytes @ 0xE0001058)
T6068 136:329.696   Data:  00 00 00 00
T6068 136:329.712   Debug reg: DWT_FUNC[3]
T6068 136:329.728 - 0.587ms returns 1 (0x1)
T6068 136:329.856 JLINK_HasError()
T6068 136:329.872 JLINK_ReadReg(R0)
T6068 136:329.888 - 0.015ms returns 0x000000E8
T6068 136:329.904 JLINK_ReadReg(R1)
T6068 136:329.920 - 0.014ms returns 0x0803D961
T6068 136:329.936 JLINK_ReadReg(R2)
T6068 136:329.952 - 0.014ms returns 0x00001F90
T6068 136:329.952 JLINK_ReadReg(R3)
T6068 136:329.968 - 0.014ms returns 0x00000000
T6068 136:329.984 JLINK_ReadReg(R4)
T6068 136:330.016 - 0.014ms returns 0x08040534
T6068 136:330.016 JLINK_ReadReg(R5)
T6068 136:330.032 - 0.014ms returns 0x08040534
T6068 136:330.048 JLINK_ReadReg(R6)
T6068 136:330.064 - 0.015ms returns 0x00000000
T6068 136:330.080 JLINK_ReadReg(R7)
T6068 136:330.096 - 0.014ms returns 0x00000000
T6068 136:330.112 JLINK_ReadReg(R8)
T6068 136:330.128 - 0.014ms returns 0x00000000
T6068 136:330.144 JLINK_ReadReg(R9)
T6068 136:330.144 - 0.014ms returns 0x00000000
T6068 136:330.208 JLINK_ReadReg(R10)
T6068 136:330.208 - 0.014ms returns 0x00000000
T6068 136:330.224 JLINK_ReadReg(R11)
T6068 136:330.240 - 0.014ms returns 0x00000000
T6068 136:330.256 JLINK_ReadReg(R12)
T6068 136:330.272 - 0.014ms returns 0x00000000
T6068 136:330.288 JLINK_ReadReg(R13 (SP))
T6068 136:330.304 - 0.014ms returns 0x20015868
T6068 136:330.320 JLINK_ReadReg(R14)
T6068 136:330.336 - 0.014ms returns 0x0802B5D1
T6068 136:330.336 JLINK_ReadReg(R15 (PC))
T6068 136:330.352 - 0.014ms returns 0x0803D962
T6068 136:330.368 JLINK_ReadReg(XPSR)
T6068 136:330.400 - 0.014ms returns 0x01000000
T6068 136:330.400 JLINK_ReadReg(MSP)
T6068 136:330.416 - 0.014ms returns 0x20015868
T6068 136:330.432 JLINK_ReadReg(PSP)
T6068 136:330.448 - 0.014ms returns 0x00000000
T6068 136:330.464 JLINK_ReadReg(CFBP)
T6068 136:330.480 - 0.014ms returns 0x00000000
T6068 136:330.496 JLINK_ReadReg(FPSCR)
T6068 136:334.928 - 4.428ms returns 0x00000000
T6068 136:334.960 JLINK_ReadReg(FPS0)
T6068 136:334.976 - 0.016ms returns 0x00000000
T6068 136:334.992 JLINK_ReadReg(FPS1)
T6068 136:335.008 - 0.014ms returns 0x00000000
T6068 136:335.008 JLINK_ReadReg(FPS2)
T6068 136:335.040 - 0.014ms returns 0x00000000
T6068 136:335.056 JLINK_ReadReg(FPS3)
T6068 136:335.072 - 0.014ms returns 0x00000000
T6068 136:335.072 JLINK_ReadReg(FPS4)
T6068 136:335.088 - 0.014ms returns 0x00000000
T6068 136:335.104 JLINK_ReadReg(FPS5)
T6068 136:335.120 - 0.014ms returns 0x00000000
T6068 136:335.136 JLINK_ReadReg(FPS6)
T6068 136:335.136 - 0.014ms returns 0x00000000
T6068 136:335.168 JLINK_ReadReg(FPS7)
T6068 136:335.184 - 0.014ms returns 0x00000000
T6068 136:335.200 JLINK_ReadReg(FPS8)
T6068 136:335.200 - 0.014ms returns 0x00000000
T6068 136:335.216 JLINK_ReadReg(FPS9)
T6068 136:335.232 - 0.013ms returns 0x00000000
T6068 136:335.248 JLINK_ReadReg(FPS10)
T6068 136:335.264 - 0.014ms returns 0x00000000
T6068 136:335.280 JLINK_ReadReg(FPS11)
T6068 136:335.296 - 0.014ms returns 0x00000000
T6068 136:335.312 JLINK_ReadReg(FPS12)
T6068 136:335.328 - 0.014ms returns 0x00000000
T6068 136:335.328 JLINK_ReadReg(FPS13)
T6068 136:335.344 - 0.014ms returns 0x00000000
T6068 136:335.360 JLINK_ReadReg(FPS14)
T6068 136:335.392 - 0.014ms returns 0x00000000
T6068 136:335.392 JLINK_ReadReg(FPS15)
T6068 136:335.408 - 0.014ms returns 0x00000000
T6068 136:335.424 JLINK_ReadReg(FPS16)
T6068 136:335.440 - 0.014ms returns 0x00000000
T6068 136:335.456 JLINK_ReadReg(FPS17)
T6068 136:335.456 - 0.014ms returns 0x00000000
T6068 136:335.472 JLINK_ReadReg(FPS18)
T6068 136:335.488 - 0.014ms returns 0x00000000
T6068 136:335.520 JLINK_ReadReg(FPS19)
T6068 136:335.520 - 0.014ms returns 0x00000000
T6068 136:335.536 JLINK_ReadReg(FPS20)
T6068 136:335.552 - 0.014ms returns 0x00000000
T6068 136:335.568 JLINK_ReadReg(FPS21)
T6068 136:335.584 - 0.014ms returns 0x00000000
T6068 136:335.584 JLINK_ReadReg(FPS22)
T6068 136:335.616 - 0.014ms returns 0x00000000
T6068 136:335.632 JLINK_ReadReg(FPS23)
T6068 136:335.648 - 0.014ms returns 0x00000000
T6068 136:335.648 JLINK_ReadReg(FPS24)
T6068 136:335.664 - 0.014ms returns 0x00000000
T6068 136:335.680 JLINK_ReadReg(FPS25)
T6068 136:335.696 - 0.014ms returns 0x00000000
T6068 136:335.712 JLINK_ReadReg(FPS26)
T6068 136:335.712 - 0.014ms returns 0x00000000
T6068 136:335.744 JLINK_ReadReg(FPS27)
T6068 136:335.760 - 0.014ms returns 0x00000000
T6068 136:335.776 JLINK_ReadReg(FPS28)
T6068 136:335.776 - 0.013ms returns 0x00000000
T6068 136:335.792 JLINK_ReadReg(FPS29)
T6068 136:335.808 - 0.014ms returns 0x00000000
T6068 136:335.824 JLINK_ReadReg(FPS30)
T6068 136:335.840 - 0.014ms returns 0x00000000
T6068 136:335.856 JLINK_ReadReg(FPS31)
T6068 136:335.872 - 0.014ms returns 0x00000000
T316C 136:335.952 JLINK_HasError()
T316C 136:335.968 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T316C 136:336.000   CPU_ReadMem(4 bytes @ 0xE0001004)
T316C 136:336.480   Data:  78 06 A9 C0
T316C 136:336.496   Debug reg: DWT_CYCCNT
T316C 136:336.528 - 0.553ms returns 1 (0x1)
T316C 136:336.640 JLINK_ReadMemEx(0x0803D962, 0x2 Bytes, Flags = 0x02000000)
T316C 136:336.656   CPU_ReadMem(64 bytes @ 0x0803D940)
T316C 136:337.552    -- Updating C cache (64 bytes @ 0x0803D940)
T316C 136:337.568    -- Read from C cache (2 bytes @ 0x0803D962)
T316C 136:337.600   Data:  01 90
T316C 136:337.616 - 0.978ms returns 2 (0x2)
T316C 136:337.632 JLINK_ReadMemEx(0x0803D964, 0x3C Bytes, Flags = 0x02000000)
T316C 136:337.648   CPU_ReadMem(64 bytes @ 0x0803D980)
T316C 136:338.592    -- Updating C cache (64 bytes @ 0x0803D980)
T316C 136:338.608    -- Read from C cache (60 bytes @ 0x0803D964)
T316C 136:338.624   Data:  8D F8 03 10 9D F8 03 00 01 99 08 70 02 B0 70 47 ...
T316C 136:338.656 - 1.013ms returns 60 (0x3C)
T316C 136:338.656 JLINK_ReadMemEx(0x0803D964, 0x2 Bytes, Flags = 0x02000000)
T316C 136:338.672    -- Read from C cache (2 bytes @ 0x0803D964)
T316C 136:338.704   Data:  8D F8
T316C 136:338.720 - 0.055ms returns 2 (0x2)
T6068 141:469.760 JLINK_ReadMemEx(0x0803D962, 0x2 Bytes, Flags = 0x02000000)
T6068 141:469.808    -- Read from C cache (2 bytes @ 0x0803D962)
T6068 141:469.840   Data:  01 90
T6068 141:469.856 - 0.097ms returns 2 (0x2)
T6068 141:469.872 JLINK_HasError()
T6068 141:469.888 JLINK_HasError()
T6068 141:469.904 JLINK_Go()
T6068 141:470.496   CPU_ReadMem(4 bytes @ 0xE0001000)
T6068 141:471.824 - 1.917ms
T6068 141:572.880 JLINK_HasError()
T6068 141:572.960 JLINK_IsHalted()
T6068 141:573.456 - 0.501ms returns FALSE
T6068 141:673.712 JLINK_HasError()
T6068 141:673.776 JLINK_IsHalted()
T6068 141:674.352 - 0.585ms returns FALSE
T6068 141:774.944 JLINK_HasError()
T6068 141:775.008 JLINK_IsHalted()
T6068 141:775.600 - 0.591ms returns FALSE
T6068 141:875.680 JLINK_HasError()
T6068 141:875.744 JLINK_IsHalted()
T6068 141:876.272 - 0.527ms returns FALSE
T6068 141:976.992 JLINK_HasError()
T6068 141:977.104 JLINK_IsHalted()
T6068 141:977.744 - 0.635ms returns FALSE
T6068 142:077.856 JLINK_HasError()
T6068 142:077.984 JLINK_HasError()
T6068 142:078.032 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6068 142:078.112   CPU_ReadMem(4 bytes @ 0xE0001004)
T6068 142:079.168   Data:  48 AA BD C6
T6068 142:079.296   Debug reg: DWT_CYCCNT
T6068 142:079.360 - 1.331ms returns 1 (0x1)
T6068 142:079.728 JLINK_IsHalted()
T6068 142:080.800 - 1.065ms returns FALSE
T6068 142:181.136 JLINK_HasError()
T6068 142:181.248 JLINK_IsHalted()
T6068 142:182.288 - 1.036ms returns FALSE
T6068 142:282.944 JLINK_HasError()
T6068 142:283.056 JLINK_IsHalted()
T6068 142:284.000 - 0.936ms returns FALSE
T6068 142:385.104 JLINK_HasError()
T6068 142:385.152 JLINK_IsHalted()
T6068 142:385.744 - 0.580ms returns FALSE
T6068 142:485.856 JLINK_HasError()
T6068 142:485.920 JLINK_Halt()
T6068 142:488.928 - 3.010ms returns 0x00
T6068 142:488.960 JLINK_IsHalted()
T6068 142:488.976 - 0.014ms returns TRUE
T6068 142:488.992 JLINK_IsHalted()
T6068 142:488.992 - 0.014ms returns TRUE
T6068 142:489.008 JLINK_IsHalted()
T6068 142:489.024 - 0.014ms returns TRUE
T6068 142:489.056 JLINK_HasError()
T6068 142:489.056 JLINK_ReadReg(R15 (PC))
T6068 142:489.072 - 0.017ms returns 0x0802CB5A
T6068 142:489.088 JLINK_ReadReg(XPSR)
T6068 142:489.104 - 0.014ms returns 0x01000000
T6068 142:489.120 JLINK_HasError()
T6068 142:489.136 JLINK_HasError()
T6068 142:489.152 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T6068 142:489.184   CPU_ReadMem(4 bytes @ 0xE000ED30)
T6068 142:489.760   Data:  01 00 00 00
T6068 142:489.792 - 0.642ms returns 1 (0x1)
T6068 142:489.824 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T6068 142:489.840   CPU_ReadMem(4 bytes @ 0xE0001028)
T6068 142:490.352   Data:  00 00 00 00
T6068 142:490.400   Debug reg: DWT_FUNC[0]
T6068 142:490.432 - 0.614ms returns 1 (0x1)
T6068 142:490.448 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T6068 142:490.464   CPU_ReadMem(4 bytes @ 0xE0001038)
T6068 142:490.992   Data:  00 02 00 00
T6068 142:491.024   Debug reg: DWT_FUNC[1]
T6068 142:491.040 - 0.598ms returns 1 (0x1)
T6068 142:491.056 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T6068 142:491.072   CPU_ReadMem(4 bytes @ 0xE0001048)
T6068 142:491.648   Data:  00 00 00 00
T6068 142:491.680   Debug reg: DWT_FUNC[2]
T6068 142:491.680 - 0.634ms returns 1 (0x1)
T6068 142:491.712 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T6068 142:491.728   CPU_ReadMem(4 bytes @ 0xE0001058)
T6068 142:492.304   Data:  00 00 00 00
T6068 142:492.320   Debug reg: DWT_FUNC[3]
T6068 142:492.352 - 0.646ms returns 1 (0x1)
T6068 142:492.464 JLINK_HasError()
T6068 142:492.480 JLINK_ReadReg(R0)
T6068 142:492.496 - 0.016ms returns 0x000000B0
T6068 142:492.512 JLINK_ReadReg(R1)
T6068 142:492.528 - 0.014ms returns 0x000000B0
T6068 142:492.544 JLINK_ReadReg(R2)
T6068 142:492.560 - 0.014ms returns 0x00001F90
T6068 142:492.576 JLINK_ReadReg(R3)
T6068 142:492.576 - 0.014ms returns 0x00000000
T6068 142:492.592 JLINK_ReadReg(R4)
T6068 142:492.608 - 0.014ms returns 0x08040534
T6068 142:492.624 JLINK_ReadReg(R5)
T6068 142:492.640 - 0.014ms returns 0x08040534
T6068 142:492.656 JLINK_ReadReg(R6)
T6068 142:492.672 - 0.014ms returns 0x00000000
T6068 142:492.688 JLINK_ReadReg(R7)
T6068 142:492.704 - 0.014ms returns 0x00000000
T6068 142:492.704 JLINK_ReadReg(R8)
T6068 142:492.736 - 0.014ms returns 0x00000000
T6068 142:492.752 JLINK_ReadReg(R9)
T6068 142:492.768 - 0.013ms returns 0x00000000
T6068 142:492.768 JLINK_ReadReg(R10)
T6068 142:492.784 - 0.014ms returns 0x00000000
T6068 142:492.800 JLINK_ReadReg(R11)
T6068 142:492.816 - 0.014ms returns 0x00000000
T6068 142:492.832 JLINK_ReadReg(R12)
T6068 142:492.848 - 0.014ms returns 0x00000000
T6068 142:492.864 JLINK_ReadReg(R13 (SP))
T6068 142:492.880 - 0.014ms returns 0x20015888
T6068 142:492.896 JLINK_ReadReg(R14)
T6068 142:492.896 - 0.013ms returns 0x0802B5E9
T6068 142:492.912 JLINK_ReadReg(R15 (PC))
T6068 142:492.928 - 0.014ms returns 0x0802CB5A
T6068 142:492.960 JLINK_ReadReg(XPSR)
T6068 142:492.960 - 0.014ms returns 0x01000000
T6068 142:492.976 JLINK_ReadReg(MSP)
T6068 142:492.992 - 0.014ms returns 0x20015888
T6068 142:493.008 JLINK_ReadReg(PSP)
T6068 142:493.024 - 0.013ms returns 0x00000000
T6068 142:493.024 JLINK_ReadReg(CFBP)
T6068 142:493.040 - 0.014ms returns 0x00000000
T6068 142:493.056 JLINK_ReadReg(FPSCR)
T6068 142:497.504 - 4.439ms returns 0x00000000
T6068 142:497.536 JLINK_ReadReg(FPS0)
T6068 142:497.552 - 0.016ms returns 0x00000000
T6068 142:497.568 JLINK_ReadReg(FPS1)
T6068 142:497.568 - 0.014ms returns 0x00000000
T6068 142:497.584 JLINK_ReadReg(FPS2)
T6068 142:497.600 - 0.014ms returns 0x00000000
T6068 142:497.616 JLINK_ReadReg(FPS3)
T6068 142:497.632 - 0.014ms returns 0x00000000
T6068 142:497.648 JLINK_ReadReg(FPS4)
T6068 142:497.664 - 0.014ms returns 0x00000000
T6068 142:497.680 JLINK_ReadReg(FPS5)
T6068 142:497.696 - 0.014ms returns 0x00000000
T6068 142:497.696 JLINK_ReadReg(FPS6)
T6068 142:497.712 - 0.013ms returns 0x00000000
T6068 142:497.728 JLINK_ReadReg(FPS7)
T6068 142:497.760 - 0.014ms returns 0x00000000
T6068 142:497.760 JLINK_ReadReg(FPS8)
T6068 142:497.776 - 0.014ms returns 0x00000000
T6068 142:497.792 JLINK_ReadReg(FPS9)
T6068 142:497.808 - 0.014ms returns 0x00000000
T6068 142:497.824 JLINK_ReadReg(FPS10)
T6068 142:497.824 - 0.014ms returns 0x00000000
T6068 142:497.840 JLINK_ReadReg(FPS11)
T6068 142:497.872 - 0.014ms returns 0x00000000
T6068 142:497.888 JLINK_ReadReg(FPS12)
T6068 142:497.888 - 0.014ms returns 0x00000000
T6068 142:497.904 JLINK_ReadReg(FPS13)
T6068 142:497.920 - 0.014ms returns 0x00000000
T6068 142:497.936 JLINK_ReadReg(FPS14)
T6068 142:497.952 - 0.014ms returns 0x00000000
T6068 142:497.952 JLINK_ReadReg(FPS15)
T6068 142:497.984 - 0.014ms returns 0x00000000
T6068 142:498.000 JLINK_ReadReg(FPS16)
T6068 142:498.016 - 0.014ms returns 0x00000000
T6068 142:498.016 JLINK_ReadReg(FPS17)
T6068 142:498.032 - 0.014ms returns 0x00000000
T6068 142:498.048 JLINK_ReadReg(FPS18)
T6068 142:498.080 - 0.015ms returns 0x00000000
T6068 142:498.080 JLINK_ReadReg(FPS19)
T6068 142:498.096 - 0.014ms returns 0x00000000
T6068 142:498.112 JLINK_ReadReg(FPS20)
T6068 142:498.128 - 0.014ms returns 0x00000000
T6068 142:498.144 JLINK_ReadReg(FPS21)
T6068 142:498.144 - 0.014ms returns 0x00000000
T6068 142:498.176 JLINK_ReadReg(FPS22)
T6068 142:498.192 - 0.014ms returns 0x00000000
T6068 142:498.208 JLINK_ReadReg(FPS23)
T6068 142:498.208 - 0.014ms returns 0x00000000
T6068 142:498.224 JLINK_ReadReg(FPS24)
T6068 142:498.240 - 0.014ms returns 0x00000000
T6068 142:498.256 JLINK_ReadReg(FPS25)
T6068 142:498.272 - 0.014ms returns 0x00000000
T6068 142:498.272 JLINK_ReadReg(FPS26)
T6068 142:498.304 - 0.013ms returns 0x00000000
T6068 142:498.320 JLINK_ReadReg(FPS27)
T6068 142:498.336 - 0.014ms returns 0x00000000
T6068 142:498.336 JLINK_ReadReg(FPS28)
T6068 142:498.352 - 0.013ms returns 0x00000000
T6068 142:498.368 JLINK_ReadReg(FPS29)
T6068 142:498.384 - 0.014ms returns 0x00000000
T6068 142:498.400 JLINK_ReadReg(FPS30)
T6068 142:498.416 - 0.014ms returns 0x00000000
T6068 142:498.432 JLINK_ReadReg(FPS31)
T6068 142:498.448 - 0.014ms returns 0x00000000
T316C 142:498.528 JLINK_HasError()
T316C 142:498.544 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T316C 142:498.560   CPU_ReadMem(4 bytes @ 0xE0001004)
T316C 142:499.040   Data:  7B 2D D2 CA
T316C 142:499.056   Debug reg: DWT_CYCCNT
T316C 142:499.088 - 0.536ms returns 1 (0x1)
T316C 142:499.184 JLINK_ReadMemEx(0x0802CB5A, 0x2 Bytes, Flags = 0x02000000)
T316C 142:499.216   CPU_ReadMem(64 bytes @ 0x0802CB40)
T316C 142:500.080    -- Updating C cache (64 bytes @ 0x0802CB40)
T316C 142:500.112    -- Read from C cache (2 bytes @ 0x0802CB5A)
T316C 142:500.128   Data:  9D F8
T316C 142:500.144 - 0.958ms returns 2 (0x2)
T316C 142:500.160 JLINK_ReadMemEx(0x0802CB5C, 0x3C Bytes, Flags = 0x02000000)
T316C 142:500.176   CPU_ReadMem(64 bytes @ 0x0802CB80)
T316C 142:501.088    -- Updating C cache (64 bytes @ 0x0802CB80)
T316C 142:501.104    -- Read from C cache (60 bytes @ 0x0802CB5C)
T316C 142:501.136   Data:  06 10 4F F4 84 70 00 EB 41 10 FE F7 17 FD 08 B1 ...
T316C 142:501.152 - 0.987ms returns 60 (0x3C)
T316C 142:501.168 JLINK_ReadMemEx(0x0802CB5C, 0x2 Bytes, Flags = 0x02000000)
T316C 142:501.184    -- Read from C cache (2 bytes @ 0x0802CB5C)
T316C 142:501.200   Data:  06 10
T316C 142:501.216 - 0.056ms returns 2 (0x2)
T316C 144:794.160 JLINK_HasError()
T316C 144:801.824 JLINK_Close()
T316C 144:802.560   OnDisconnectTarget() start
T316C 144:802.592    J-Link Script File: Executing OnDisconnectTarget()
T316C 144:802.624   CPU_WriteMem(4 bytes @ 0xE0042004)
T316C 144:803.520   CPU_WriteMem(4 bytes @ 0xE0042008)
T316C 144:804.080   OnDisconnectTarget() end - Took 1.48ms
T316C 144:804.112   CPU_ReadMem(4 bytes @ 0xE0001000)
T316C 144:819.248 - 17.417ms
T316C 144:819.296   
T316C 144:819.296   Closed
