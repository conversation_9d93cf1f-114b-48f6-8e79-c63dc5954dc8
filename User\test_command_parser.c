#include "serial_net_config.h"
#include <stdio.h>
#include <string.h>
#include <assert.h>

/* 测试用例结构体 */
typedef struct {
    const char* input;
    CommandType_t expected_cmd;
    int expected_param_count;
    const char* expected_params[4];
} TestCase_t;

/* 测试用例数据 */
TestCase_t test_cases[] = {
    // 基本命令测试
    {"NET_CONFIG", CMD_NET_CONFIG, 0, {"", "", "", ""}},
    {"net_config", CMD_NET_CONFIG, 0, {"", "", "", ""}},
    {"NETCONFIG", CMD_NET_CONFIG, 0, {"", "", "", ""}},
    
    // 带参数的命令测试
    {"SET_IP *************", CMD_SET_IP, 1, {"*************", "", "", ""}},
    {"set_ip ********", CMD_SET_IP, 1, {"********", "", "", ""}},
    {"SETIP **********", CMD_SET_IP, 1, {"**********", "", "", ""}},
    
    {"SET_MASK *************", CMD_SET_MASK, 1, {"*************", "", "", ""}},
    {"SET_GW ***********", CMD_SET_GW, 1, {"***********", "", "", ""}},
    {"SET_DNS *******", CMD_SET_DNS, 1, {"*******", "", "", ""}},
    
    // 多参数命令测试
    {"AUTH admin123", CMD_AUTH, 1, {"admin123", "", "", ""}},
    
    // 无参数命令测试
    {"APPLY_NET", CMD_APPLY_NET, 0, {"", "", "", ""}},
    {"RESET_NET", CMD_RESET_NET, 0, {"", "", "", ""}},
    {"LOGOUT", CMD_LOGOUT, 0, {"", "", "", ""}},
    {"HELP", CMD_HELP, 0, {"", "", "", ""}},
    {"?", CMD_HELP, 0, {"", "", "", ""}},
    
    // 错误输入测试
    {"", CMD_UNKNOWN, 0, {"", "", "", ""}},
    {"   ", CMD_UNKNOWN, 0, {"", "", "", ""}},
    {"INVALID_CMD", CMD_UNKNOWN, 0, {"", "", "", ""}},
    {"SET_IP", CMD_SET_IP, 0, {"", "", "", ""}}, // 缺少参数
    
    // 带多余空格的测试
    {"  SET_IP   *************  ", CMD_SET_IP, 1, {"*************", "", "", ""}},
    {"\tNET_CONFIG\t", CMD_NET_CONFIG, 0, {"", "", "", ""}},
    
    // 多参数分割测试
    {"SET_IP ************* *************", CMD_SET_IP, 2, {"*************", "*************", "", ""}},
};

/* 响应格式化测试用例 */
void test_response_formatting(void)
{
    char response_buffer[512];
    int len;
    
    printf("Testing response formatting...\n");
    
    // 测试成功响应
    len = format_success_response(response_buffer, sizeof(response_buffer), "Configuration saved");
    printf("Success response: %s", response_buffer);
    assert(len > 0);
    assert(strstr(response_buffer, "OK:Configuration saved") != NULL);
    
    // 测试错误响应
    len = format_error_response(response_buffer, sizeof(response_buffer), NET_CONFIG_ERR_INVALID_IP, "192.168.1.256");
    printf("Error response: %s", response_buffer);
    assert(len > 0);
    assert(strstr(response_buffer, "ERROR:Invalid IP address format") != NULL);
    
    // 测试网络配置显示
    NetConfig_t test_config = {
        .ip = {192, 168, 1, 100},
        .subnet = {255, 255, 255, 0},
        .gateway = {192, 168, 1, 1},
        .dns = {8, 8, 8, 8},
        .mac = {0x00, 0x08, 0xDC, 0x01, 0x02, 0x03},
        .dhcp_mode = 0
    };
    
    len = format_config_display_response(response_buffer, sizeof(response_buffer), &test_config);
    printf("Config display response:\n%s", response_buffer);
    assert(len > 0);
    assert(strstr(response_buffer, "IP=*************") != NULL);
    assert(strstr(response_buffer, "MASK=*************") != NULL);
    
    printf("Response formatting tests passed!\n\n");
}

/* 命令解析测试 */
void test_command_parsing(void)
{
    printf("Testing command parsing...\n");
    
    int num_tests = sizeof(test_cases) / sizeof(test_cases[0]);
    int passed = 0;
    
    for (int i = 0; i < num_tests; i++) {
        SerialCommand_t cmd;
        CommandType_t result = parse_serial_command(test_cases[i].input, &cmd);
        
        printf("Test %d: Input='%s' -> ", i+1, test_cases[i].input);
        
        // 检查命令类型
        if (result != test_cases[i].expected_cmd) {
            printf("FAILED (cmd type: expected %d, got %d)\n", test_cases[i].expected_cmd, result);
            continue;
        }
        
        // 检查参数个数
        if (cmd.param_count != test_cases[i].expected_param_count) {
            printf("FAILED (param count: expected %d, got %d)\n", test_cases[i].expected_param_count, cmd.param_count);
            continue;
        }
        
        // 检查参数内容
        int params_match = 1;
        for (int j = 0; j < cmd.param_count; j++) {
            if (strcmp(cmd.params[j], test_cases[i].expected_params[j]) != 0) {
                printf("FAILED (param %d: expected '%s', got '%s')\n", j, test_cases[i].expected_params[j], cmd.params[j]);
                params_match = 0;
                break;
            }
        }
        
        if (params_match) {
            printf("PASSED\n");
            passed++;
        }
    }
    
    printf("Command parsing tests: %d/%d passed\n\n", passed, num_tests);
}

/* 边界条件测试 */
void test_edge_cases(void)
{
    printf("Testing edge cases...\n");
    
    SerialCommand_t cmd;
    char response_buffer[512];
    
    // 测试NULL指针
    CommandType_t result = parse_serial_command(NULL, &cmd);
    assert(result == CMD_UNKNOWN);
    
    result = parse_serial_command("SET_IP ***********", NULL);
    assert(result == CMD_UNKNOWN);
    
    // 测试超长输入
    char long_input[300];
    memset(long_input, 'A', sizeof(long_input) - 1);
    long_input[sizeof(long_input) - 1] = '\0';
    
    result = parse_serial_command(long_input, &cmd);
    assert(result == CMD_UNKNOWN);
    
    // 测试响应缓冲区溢出
    int len = format_success_response(response_buffer, 10, "This is a very long message that should cause buffer overflow");
    assert(len == -1);
    
    // 测试空白字符处理
    result = parse_serial_command("   \t\r\n   ", &cmd);
    assert(result == CMD_UNKNOWN);
    
    printf("Edge case tests passed!\n\n");
}

/* 主测试函数 */
int main(void)
{
    printf("=== Serial Command Parser Test Suite ===\n\n");
    
    test_command_parsing();
    test_response_formatting();
    test_edge_cases();
    
    printf("All tests completed!\n");
    return 0;
}