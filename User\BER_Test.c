#include "cs4224_api.h"
#include "Drivers.h"
#include <stdio.h>
#include <stdlib.h>
#include "CS4343_operation.h"
#include "BER_Test.h"
#include "Lcd_Interface.h"

#define EYE_HEIGHT  256
#define EYE_WIDTH   128

#define RX_BASE 0x1080

//u32 link3 = 1;
u8 reseton1 =0;
u8 reseton2 =0;
u8 reseton3 =0;
u8 reseton4 =0;

u8 pattern[4] = {PRBS31, PRBS31, PRBS31, PRBS31};          /*  定义码型的全局变量*/
u8 rate = shiGlan;			 /*	定义速率的全局变量*/
u8 mode[4] = {FREE_RUN,FREE_RUN,FREE_RUN,FREE_RUN};			 /*	定义有无定时模式的变量 */
u8 option = swing;			 /*	定义更多的全局变量 */

u8 swing_ch[4] = {sibaimv, sibaimv, sibaimv, sibaimv};
u8 polarity_ch[4] = {normal_pn, normal_pn, normal_pn, normal_pn};     /*	定义极性的全局变量 */
u8 polarity_rx[4] = {normal_pn, normal_pn, normal_pn, normal_pn};
u8 workmode_ch = automode;     /*	定义工作模式的全局变量 */
u32 timer_set[4] = {half_min,half_min,half_min,half_min};	  /* 定义定时长度的变量 */
float def_rate = 10312.5;

u8 Eye_data[130] = {0 };
u8 Eyelane;

uint64_t BEC_Value_ch[4] = {0,0,0,0};		/* 用来储存误码数的全局变量 */	
uint64_t BEC_Value_real[4] = {0,0,0,0};
//uint64_t BEC_Value_tp[4] = {0,0,0,0};		/* 用来储存误码数的全局变量 */	
u8 START_SYNC1 = SYNC1_First;			  /* 用来标志四个通道各自第一次记录开始同步的标志 */
u8 START_SYNC2 = SYNC2_First;
u8 START_SYNC3 = SYNC3_First;
u8 START_SYNC4 = SYNC4_First;

u8 sync_ch0_laststate = 1 ;   //自动同步模式，如果上一轮是fail状态，则置1
u8 sync_ch1_laststate = 1 ;
u8 sync_ch2_laststate = 1 ;
u8 sync_ch3_laststate = 1 ;

u8 sta_ch0 = 1 ;    //0表示fail，1表示link，2 full、用于上位机通信
u8 sta_ch1 = 1;
u8 sta_ch2 = 1;
u8 sta_ch3 = 1; 

u8 mdiochan = 0;

//u8 ber_mode = aBer;

//“EQ调节”：
//     ①Eqpst：000（0）、001（1）、010（2）、011（3）、100（4）、101（5）、110（6）、111（7）；
//     ②Eqpre：000（0）、001（1）、010（2）、011（3）；
EQSet EQ_Ch[4] = {{2,0},
					{2,0},
					{2,0},
					{2,0}
					};

//时钟分频设置,0x0032表示16分频，0x0012表示8分频，默认情况下为8分频
u8 ClockDivFlag = 0;
const u16 clock_div_value[2] = {0x0012, 0x0032};
					
u8 BEC_lcd[12] = {' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' '};		  /* 以下为用于屏显示的字符串 */
u8 BEC_lcd_init[12] = {' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' ',' '};		  /* 以下为用于屏显示的字符串 */
u8 BER_lcd[12] = {' ',' ',' ','0','.','0','0','0','E','-','0','0'};
u8 HGANG[12] = {'-',' ','-',' ','-',' ','-',' ','-',' ','-',' '};
//u8 HGANG1[3] = {'-',' ','-'};
u8 FAIL[4] = {'F','a','i','l'};
u8 LINK[4] = {'L','i','n','k'};
u8 FULL[4] = {'F','u','l','l'};
u8 ERR[20] = {' ',' ',' ',' ',' ',' ',' ','E','R','R','O','R',' ',' ',' ',' ',' ',' ',' ',' '} ;
u8 ERR_id[20] = {' ',' ',' ',' ','I','D',' ','E','R','R','O','R',' ',' ',' ',' ',' ',' ',' ',' '} ;
u8 ERR_Password[20] = {' ',' ',' ','P','A','S','S','W','O','R','D',' ','E','R','R','O','R',' ',' ',' '} ;
u8 ERR_rtc[5] = {'E','R','R','O','R'};
                 
//double f_si570 = 156250000.0;   /*Timer里面根据 (速率/64) 计算的时钟，默认是10.3125G/16  */
float f_si570[40] = {10312.5,  10312.5,   9953.28,   1250.0, 6250.0, 3125.0,  9953.28,       4976.0,      2488.0,  1244.16, 
                       622.08,  14025.0,  10518.75,   8500.0, 4250.0, 2125.0,   1062.5,      10000.0,      5000.0,  15000.0, 
                     10037.27, 2666.057, 11049.107,11270.089,10709.0,11095.0,  11318.0,  10754.60325, 11145.83875,  10137.6,
                       9830.4,   6144.0,    155.52,   622.08, 3000.0, 6000.0,   9000.0,      12500.0,       300.0,  10312.5};  //单位M
DIVSet DIV_Rate[40]={{0,5},{0,5},{0,4},{3,4},{1,6},{2,6},{0,0},{1,4},{2,4},{0,0},
                     {0,0},{0,7},{0,4},{1,8},{1,6},{2,6},{0,0},{0,6},{1,4},{0,7},
                     {0,0},{0,0},{0,0},{0,0},{0,4},{0,4},{0,4},{0,0},{0,0},{0,0},
                     {0,0},{0,0},{6,4},{4,4},{2,6},{1,6},{0,4},{0,7},{5,4},{0,0}};            //ddivpos   rdivpos

//?¨ò? 0-2G??è?·??μ?μ8/64   2-4G??è?·??μ?μ4/64  4-8G??è 2/64  8-11G??è?·??μ?μêy1/64   11-15G??è?·??μ?μêy1/64
//                      128/64   128/128   64/128   32/128   16/128  8/128    4/128  2/128    1/64   1/80  1/100   1/128
DIVSet DIV_userRate[12]={{7,4},   {7,8},    {6,8},   {5,8},   {4,8},  {3,8},  {2,8},  {1,8},  {0,4}, {0,6}, {0,7},  {0,8}};	

u8 userRatepos = 5;

u8 ddiv[8]={1,2,4,8,16,32,64,128};
u8 rdiv[9]={8,16,32,40,64,66,80,100,128};
//配置通讯协议和时钟输入以及误码率计算用到的f_si570
//                     002E 0036 0041 005A 005B 005C 005D 00A9 0208 0238 0239 023E 0305 0306 0519 0532 
u16 siconfig[40][16]={{0x39,0x39,0x0F,0x55,0x55,0xD0,0x00,0xA1,0xA0,0xD8,0xD6,0xC0,0x00,0x0B,0xC0,0x56},/*0:10.3125G*/
											{0x39,0x39,0x0F,0x55,0x55,0xD0,0x00,0xA1,0xA0,0xD8,0xD6,0xC0,0x00,0x0B,0xC0,0x56},/*1:10.3125G*/   //156.25
                      {0x39,0x39,0x0F,0x28,0x5C,0xCF,0x00,0x9F,0xA0,0xC0,0xDE,0xC8,0x00,0x0B,0xC0,0x5A},/*2:9.95328G*/   //155.52
											{0x39,0x39,0x0F,0x55,0x55,0xD0,0x00,0xA1,0xA0,0xD8,0xD6,0xC0,0x00,0x0B,0xC0,0x56},/*3:1.25G*/      //156.25
											{0x39,0x39,0x0F,0x55,0x55,0xD0,0x00,0xA1,0xA0,0xD8,0xD6,0xC0,0x00,0x0B,0xC0,0x56},/*4:6.25G*/      //156.25  
											{0x39,0x39,0x0F,0x55,0x55,0xD0,0x00,0xA1,0xA0,0xD8,0xD6,0xC0,0x00,0x0B,0xC0,0x56},/*5:3.125G*/     //156.25
											{0x39,0x39,0x0F,0x28,0x5C,0xCF,0x00,0x9F,0xA0,0xC0,0xDE,0xC8,0x00,0x0B,0xC0,0x5A},/*6:9.95328G*/   //155.52
											{0x39,0x39,0x0F,0x28,0x5C,0xCF,0x00,0x9F,0xA0,0xC0,0xDE,0xC8,0x00,0x0B,0xC0,0x5A},/*7:4.976G*/     //155.52
											{0x39,0x39,0x0F,0x28,0x5C,0xCF,0x00,0x9F,0xA0,0xC0,0xDE,0xC8,0x00,0x0B,0xC0,0x5A},/*8:2.488G*/     //155.52
											{0x56,0x56,0x05,0x00,0x68,0x00,0x00,0x80,0x00,0x0D,0xC9},/*9*/       
											{0x5B,0x5B,0x03,0xF4,0x6E,0x00,0xD8,0xD6,0x80,0x0D,0x78},/*10*/
											{0x38,0x38,0x0F,0x00,0x00,0xBB,0x00,0x91,0x91,0x40,0x8C,0x80,0x00,0x0C,0xB8,0x5E},/*11:14.025G*/  //->140.25
                      {0x38,0x38,0x0F,0xAA,0x2A,0xDB,0x00,0x9B,0xA5,0xD4,0x8F,0x80,0x80,0x0A,0xB5,0x45},/*12:10.52G*/     //164.375
											{0x3B,0x3B,0x0F,0x55,0x15,0xB1,0x00,0xCA,0x87,0x6A,0x95,0x80,0x80,0x0D,0xD9,0x50},/*13:8.5G*/       //132.8125 
											{0x3B,0x3B,0x0E,0x55,0x55,0x1B,0x01,0xBB,0x6E,0x18,0x92,0x80,0x80,0x10,0xD6,0x5F},/*14:4.25G*/      //106.25
											{0x3B,0x3B,0x0E,0x55,0x55,0x1B,0x01,0xBB,0x6E,0x18,0x92,0x80,0x80,0x10,0xD6,0x5F},/*15:2.125G*/     //106.25
					  {0x54,0x54,0x03,0x00,0x60,0x00,0x00,0xC8,0x00,0x0B,0x78},/*16*/
											{0x36,0x36,0x0E,0x55,0x55,0x4D,0x01,0x83,0x7D,0xA0,0x8C,0x80,0x80,0x0D,0xA3,0x42},/*17:10G*/        //->125.0
											{0x39,0x39,0x0F,0x55,0x55,0xD0,0x00,0xA1,0xA0,0xD8,0xD6,0xC0,0x00,0x0B,0xC0,0x56},/*18:5G*/         //156.25
											{0x35,0x35,0x0F,0x00,0x00,0xC8,0x00,0x6E,0x96,0x80,0x89,0x80,0x00,0x0B,0x94,0x42},/*19:15G*/  //->150.0
											{0x5B,0x5B,0x03,0x00,0x68,0x00,0x00,0xC8,0x00,0x0B,0x78},/*20*/
											{0x59,0x59,0x03,0x40,0x59,0x00,0x00,0xB0,0x80,0x0A,0x78},/*21*/
											{0x59,0x59,0x03,0x30,0x6D,0x00,0xD8,0xD6,0x00,0x0A,0x78},/*22*/
											{0x5A,0x5A,0x03,0x00,0x66,0x00,0x00,0xC8,0x00,0x0A,0x78},/*23*/
											{0x36,0x36,0x0F,0xAA,0x1A,0xDF,0x00,0x7D,0xA8,0x29,0xD1,0xC0,0x00,0x0A,0xA0,0x45},/*24:10.709G*/     //167.328125
											{0x38,0x38,0x0F,0xAA,0x0A,0xE7,0x00,0x9F,0xAE,0x9A,0xD8,0xC0,0x00,0x0A,0xB8,0x45},/*25:11.09G*/      //173.28125
											{0x39,0x39,0x0F,0xAA,0xCA,0xEB,0x00,0xB3,0xB1,0x0E,0xDD,0xC0,0x00,0x0A,0xC4,0x43}, /*26:11.318G*/    //176.84375
											{0x39,0x39,0x0F,0x55,0x55,0xD0,0x00,0xA1,0xA0,0xD8,0xD6,0xC0,0x00,0x0B,0xC0,0x56},/*27*/  //->140.25  EPON 156.25
											{0x39,0x39,0x0F,0x28,0x5C,0xCF,0x00,0x9F,0xA0,0xC0,0xDE,0xC8,0x00,0x0B,0xC0,0x5A},/*28*/  //->150.0->160  GPON 155.52
											{0x39,0x39,0x0F,0x28,0x5C,0xCF,0x00,0x9F,0xA0,0xC0,0xDE,0xC8,0x00,0x0B,0xC0,0x5A},/*29*/ //->125.0  CPON 155.52
											{0x39,0x39,0x0F,0x28,0x5C,0xCF,0x00,0x9F,0xA0,0xC0,0xDE,0xC8,0x00,0x0B,0xC0,0x5A},/*30*/  //XGPON 155.52
											{0x39,0x39,0x0F,0x28,0x5C,0xCF,0x00,0x9F,0xA0,0xC0,0xDE,0xC8,0x00,0x0B,0xC0,0x5A},/*31*/  //XGSPON 155.52
											{0x39,0x39,0x0F,0x28,0x5C,0xCF,0x00,0x9F,0xA0,0xC0,0xDE,0xC8,0x00,0x0B,0xC0,0x5A},/*32*/  //155.52   //0126(6->4)  025F(0->1)
											{0x39,0x39,0x0F,0x28,0x5C,0xCF,0x00,0x9F,0xA0,0xC0,0xDE,0xC8,0x00,0x0B,0xC0,0x5A},/*33*/   //622.04
											{0x35,0x35,0x0F,0x00,0x00,0xC8,0x00,0x6E,0x96,0x80,0x89,0x80,0x00,0x0B,0x94,0x42},/*34*/  //3g   150.0											
											{0x35,0x35,0x0F,0x00,0x00,0xC8,0x00,0x6E,0x96,0x80,0x89,0x80,0x00,0x0B,0x94,0x42},/*35*/  //6g   150.0
											{0x3D,0x3D,0x0F,0x55,0x55,0xD0,0x00,0xBC,0xAF,0xA0,0x8C,0x80,0x00,0x0C,0xF4,0xA6},/*36*/  //9G  140.625
											{0x36,0x36,0x0E,0x55,0x55,0x4D,0x01,0x83,0x7D,0xA0,0x8C,0x80,0x80,0x0D,0xA3,0x42},/*37:12.5G*/ //->125.0
											{0x35,0x35,0x0F,0x00,0x00,0xC8,0x00,0x6E,0x96,0x80,0x89,0x80,0x00,0x0B,0x94,0x42},/*38*/  //300M  150.0  0x36,0x36,0x0E,0x55,0x55,0x4D,0x01,0x83,0x7D,0xA0,0x8C,0x80,0x80,0x0D,0xA3,0x42
											{0x36,0x36,0x0E,0x55,0x55,0x4D,0x01,0x83,0x7D,0xA0,0x8C,0x80,0x80,0x0D,0xA3,0x42}};
											
void Rate_Set(u8 datarate)
{
	
	switch(datarate)	
	{	
		case shiGwan:		
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_10gwan();
		mdiochan = 1;
		Rules_Set_10gwan();
		break;
		
		case shiwuG:	//15000 =	150*100/1		
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_15g();
		mdiochan = 1;
		Rules_Set_15g();	
		break;
		
//		case user_rate:
//		if(def_rate <= 90.0 ) userRatepos = 0;
//		else if(def_rate < 128.0 ) userRatepos = 1;
//		else if(def_rate <= 260.0 ) userRatepos = 2;
//		else if(def_rate <= 520.0 ) userRatepos = 3;
//		else if(def_rate <= 1070.0 ) userRatepos = 4;
//		else if(def_rate <= 2140.0 ) userRatepos = 5;
//		else if(def_rate <= 4200.0 ) userRatepos = 6;
//		else if(def_rate <= 8500.0 ) userRatepos = 7;
//		else if(def_rate <= 11520.0 ) userRatepos = 8;
//		else if(def_rate <= 14400.0 ) userRatepos = 9;
//		else if(def_rate <= 15500.0 ) userRatepos = 10;
//		else userRatepos = 11;
		
//		Freq_Set(def_rate/rdiv[DIV_userRate[userRatepos].rdivpos]*ddiv[DIV_userRate[userRatepos].ddivpos]*2.0);
////		Freq_Set(def_rate/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);
////		Freq_Set(def_rate/64.0*ddiv[DIV_Rate[0].ddivpos]*2.0);	
////		Rules_Set_10g();
//		Rules_Set_Rate();
//		break;
		
		case shiGlan:
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);		
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_10g();
		mdiochan = 1;
		Rules_Set_10g();
		break;
			
		case yiGe:
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);	
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_1g();
		mdiochan = 1;
		Rules_Set_1g();
		break;
		
		case rxaui:
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);	
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_rxaui();
		mdiochan = 1;
		Rules_Set_rxaui();
		break;
		
		case xaui:
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);	
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_xaui();
		mdiochan = 1;
		Rules_Set_xaui();
		break;
		
		case oc96:
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);	
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_oc96();
		mdiochan = 1;
		Rules_Set_oc96();
		break;
			
		case oc48:
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);	
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_oc48();
		mdiochan = 1;
		Rules_Set_oc48();
		break;
		
//		case oc24:
//		Freq_Set(155.52);
//		if(polarity_ch[chan]) cs4343_init_oc24_inv(chan);
//		else cs4343_init_oc24(chan);
//		f_si570[datarate]=1244160000.0;
//		break;
		
//		case oc12:
//		Freq_Set(155.52);
//		if(polarity_ch) cs4343_init_oc12_inv();
//		else cs4343_init_oc12();
//		f_si570[datarate]=622080000.0;
//		break;
		
		case shierdianwuG: //12500=125*100/1
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_12g();
		mdiochan = 1;
		Rules_Set_12g();
		break;
		
		case shiliuGfc:  //14025 = 140.25*100/1
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_fc16g();
		mdiochan = 1;
		Rules_Set_fc16g();
		break;
		
		case shiG_FC:
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);	
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_fc10g();
		mdiochan = 1;
		Rules_Set_fc10g();
		break;
		
		case baG_FC:
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);	
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_fc8g();
		mdiochan = 1;
		Rules_Set_fc8g();
		break;
		
		case siG_FC:
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);	
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_fc4g();
		mdiochan = 1;
		Rules_Set_fc4g();
		break;
		
		case erG_FC:
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);	
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_fc2g();
		mdiochan = 1;
		Rules_Set_fc2g();
		break;
		
		case shiGinfi:
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);	
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_10ginfi();
		mdiochan = 1;
		Rules_Set_10ginfi();
		break;
		
		case wuGinfi:
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);	
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_5ginfi();
		 mdiochan = 1;
		Rules_Set_5ginfi();
		break;
				
		case otu2:
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);	
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_otu2();
		mdiochan = 1;
		Rules_Set_otu2();
		break;
		
		case otu2e:
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);	
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_otu2e();
		mdiochan = 1;
		Rules_Set_otu2e();
		break;
		
		case otu2f:
		Freq_Set(f_si570[datarate]/rdiv[DIV_Rate[datarate].rdivpos]*ddiv[DIV_Rate[datarate].ddivpos]*2.0);	
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	  mdiochan = 0;
		Rules_Set_otu2f();
		mdiochan = 1;
		Rules_Set_otu2f();
		break;
		
		case epon:
		Freq_Set(312.5);		
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	    mdiochan = 0;
		Rules_Set_Epon();
		mdiochan = 1;
		Rules_Set_Epon();
		break;
		
		case gpon:
		Freq_Set(311.04);		
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	    mdiochan = 0;
		Rules_Set_Gpon();
		mdiochan = 1;
		Rules_Set_Gpon();
		break;
		
		case cpon:
		Freq_Set(311.04);		
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	    mdiochan = 0;
		Rules_Set_Cpon();
		mdiochan = 1;
		Rules_Set_Cpon();
		break;
		
		case xgpon:
		Freq_Set(311.04);		
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	    mdiochan = 0;
		Rules_Set_XGpon();
		mdiochan = 1;
		Rules_Set_XGpon();
		break;
		
		case xgspon:
		Freq_Set(311.04);		
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	    mdiochan = 0;
		Rules_Set_XGSpon();
		mdiochan = 1;
		Rules_Set_XGSpon();
		break;
		
		case cpri155:
		Freq_Set(311.04);		
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	    mdiochan = 0;
		Rules_Set_Cpri155();
		mdiochan = 1;
		Rules_Set_Cpri155();
		break;
		
		
		case cpri622:
		Freq_Set(311.04);		
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	    mdiochan = 0;
		Rules_Set_Cpri622();
		mdiochan = 1;
		Rules_Set_Cpri622();
		break;
		
		case cpri3:
		Freq_Set(300);		
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	    mdiochan = 0;
		Rules_Set_Cpri3();
		mdiochan = 1;
		Rules_Set_Cpri3();
		break;
		
		
		case cpri6:
		Freq_Set(300);		
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	    mdiochan = 0;
		Rules_Set_Cpri6();
		mdiochan = 1;
		Rules_Set_Cpri6();
		break;
		
		case cpri9:
		Freq_Set(281.25);		
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	    mdiochan = 0;
		Rules_Set_Cpri9();
		mdiochan = 1;
		Rules_Set_Cpri9();
		break;
		
		case cpri300:
		Freq_Set(300);		
		Si5340_Config(siconfig[datarate][0],siconfig[datarate][1],siconfig[datarate][2],siconfig[datarate][3],siconfig[datarate][4],siconfig[datarate][5],siconfig[datarate][6],siconfig[datarate][7],siconfig[datarate][8],siconfig[datarate][9],siconfig[datarate][10],siconfig[datarate][11],siconfig[datarate][12],siconfig[datarate][13],siconfig[datarate][14],siconfig[datarate][15]);
	    mdiochan = 0;
		Rules_Set_Cpri300();
		mdiochan = 1;
		Rules_Set_Cpri300();
		break;
		
		
		
		default:
		Freq_Set(f_si570[1]/rdiv[DIV_Rate[1].rdivpos]*ddiv[DIV_Rate[1].ddivpos]*2.0);	
		Si5340_Config(siconfig[1][0],siconfig[1][1],siconfig[1][2],siconfig[1][3],siconfig[1][4],siconfig[1][5],siconfig[1][6],siconfig[1][7],siconfig[1][8],siconfig[1][9],siconfig[1][10],siconfig[1][11],siconfig[1][12],siconfig[1][13],siconfig[1][14],siconfig[1][15]);
		mdiochan = 0;
		Rules_Set_10g();
		mdiochan = 1;
		Rules_Set_10g();
//		Rules_Set_Rate();
		break;
	}		
}

void tx_amp_config(u8 swing_champ,u8 chan)
{
	u16 ctrla,ctrlb;
//	u8 slice;
	cs_status status = CS_OK;
	switch(swing_champ)
	{
		case wushimv:    //4  
		{
			ctrla=0x0804;					
			break;			
		}
		case yibaimv:    //8
		{
			ctrla=0x0808;			
			break;			
		}
		case yibaiwumv:    //12
		{
			ctrla=0x080C;				
			break;			
		}
		case erbaimv:    //16
		{
			ctrla=0x0810;				
			break;			
		}
		case erbaiwumv:    //20
		{
			ctrla=0x3814;			
			break;			
		}
			case sanbaimv:    //25
		{
			ctrla=0x3819;			
			break;			
		}
		case sanbaiwumv:    //30
		{
			ctrla=0x481E;	
			break;			
		}
			case sibaimv:    //35
		{
			ctrla=0x4823;
			break;			
		}
		case sibaiwumv:    //40
		{
			ctrla=0x5828;
			break;			
		}
		case wubaimv:    //45
		{
			ctrla=0x682D;
			break;			
		}
		case wubaiwumv:    //50
		{
			ctrla=0x6832;
			break;			
		}
		case liubaimv:    //55
		{
			ctrla=0x7837;
			break;			
		}
		default:    //30  pre 0 post 0
		{
			ctrla=0x481E;
			ctrlb=0x0000;	
			break;			
		}		
	}
	
	ctrlb=(u16)(EQ_Ch[chan].Eqpre<<8)|(EQ_Ch[chan].Eqpst);
//  ctrlb=(u16)(0<<8)|(4);
	switch(chan)
	{
		case 0:
		mdiochan =0;
		status |=cs4224_init_driver_settings(2, CS4224_TX_LINE_INTERFACE, ctrla, ctrlb);
		break;
		case 1:
		mdiochan =0;
		status |=cs4224_init_driver_settings(4, CS4224_TX_LINE_INTERFACE, ctrla, ctrlb);
		break;
		case 2:
		mdiochan =1;
		status |=cs4224_init_driver_settings(2, CS4224_TX_LINE_INTERFACE, ctrla, ctrlb);
		break;
		case 3:
		mdiochan =1;
		status |=cs4224_init_driver_settings(4, CS4224_TX_LINE_INTERFACE, ctrla, ctrlb);
		break;
	}

	if(CS_OK!=status)
		VIEWTECH_A01 (450,380,0x6022,0xf800,0x39c8,222);		
}


void prbs_checker_config(u8 prbs_pattern,u8 chan,u8 invert)
{
	switch(prbs_pattern)
	{
		case PRBS7:
		{
			cs4343_diag_prbs7_checker(chan,invert);
			break;
		}
		case PRBS9:
		{
			cs4343_diag_prbs9_checker(chan,invert);
			break;
		}
		case PRBS9_5:
		{
			cs4343_diag_prbs9_5_checker(chan,invert);
			break;
		}
		case PRBS15:
		{
			cs4343_diag_prbs15_checker(chan,invert);
			break;
		}
		case PRBS23:
		{
			cs4343_diag_prbs23_checker(chan,invert);
			break;
		}
		case PRBS31:
		{
			cs4343_diag_prbs31_checker(chan,invert);
			break;
		}
		case PRBS58:
		{
			cs4343_diag_prbs58_checker(chan,invert);
			break;
		}
		case USER_DEF:
		{
			cs4343_diag_fixpattern_checker(chan);
			break;
		}
		default:
		{
			cs4343_diag_prbs31_checker(chan,invert);
			break;
		}
	}	
}

void prbs_generator_config(u8 prbs_pattern, u8 chan, u8 invert)
{
	switch(prbs_pattern)
	{
		case PRBS7:
		{
			cs4343_diag_prbs7_generator(chan,invert);
			break;
		}
		case PRBS9:
		{
			cs4343_diag_prbs9_generator(chan,invert);
			break;
		}
		case PRBS9_5:
		{
			cs4343_diag_prbs9_5_generator(chan,invert);
			break;
		}
		case PRBS15:
		{
			cs4343_diag_prbs15_generator(chan,invert);
			break;
		}
		case PRBS23:
		{
			cs4343_diag_prbs23_generator(chan,invert);
			break;
		}
		case PRBS31:
		{
			cs4343_diag_prbs31_generator(chan,invert);
			break;
		}
		case PRBS58:
		{
			cs4343_diag_prbs58_generator(chan,invert);
			break;
		}
		case USER_DEF:
		{
			cs4343_diag_fixpattern_generator(chan);
			break;
		}
		default:
		{
			cs4343_diag_prbs31_generator(chan,invert);
			break;
		}
	}
}

/*******************************************************************************
* Function Name  : RUN_SHOW
* Description    : 运行的主程序，码型速率定时等设置，界面误码显示，
* Input          : None
* Output         : None
* Return         : None
* Attention		 : None
//interface: 0-Optical, 1-Host
//lane: 0-9
//prbs: 7,9,15,23,31
//invert: 1-invert 0-noninvert
//autoverify: 1-enable, 0-disabled
//Optical pattern control 30.16

*******************************************************************************/
u16 i=0;
void RUN_SHOW(void)
{
	u8 chan;
//	u16 data1,data2;

	// 显示网络状态在LCD顶部
	//show_network_status();
	if(TX_Rate == 0)
	{
		Rate_Set(rate);
		Delay_Ms(2000);
		TX_Rate = 1;

	}
	
	
	if(TX_First==0)
	{
		
//		cs4224_reg_get_channel(0, 0x1070, &data1);
//		cs4224_reg_get_channel(2, 0x3070, &data2);
//		VIEWTECH_A01 (400,420,0x6022,0xf800,0x39c8,data1);
//	  VIEWTECH_A01 (500,420,0x6022,0xf800,0x39c8,data2);
//		cs4224_reg_set_channel(0, 0x1070, data1|0x2);
//		cs4224_reg_get_channel(0, 0x1070, &data1);
//		cs4224_reg_get_channel(2, 0x3070, &data2);
//		VIEWTECH_A01 (400,400,0x6022,0xf800,0x39c8,data1);
//	  VIEWTECH_A01 (500,400,0x6022,0xf800,0x39c8,data2);
//		cs4343_test_mdio();
		
		for(chan=0;chan<4;chan++)
		{	
			tx_amp_config(swing_ch[chan],chan);				
			prbs_generator_config(pattern[chan],chan,polarity_ch[chan]);	
				mdiochan = 0;
			cs4224_diags_prbs_generator_enable(  chan*2+1,  CS4224_PRBS_LINE_INTERFACE,  FALSE);
//      cs4224_diags_prbs_generator_enable(  chan*2,  CS4224_PRBS_HOST_INTERFACE,  FALSE);
			cs4224_diags_prbs_generator_enable(  chan*2+1,  CS4224_PRBS_HOST_INTERFACE,  FALSE);	
				mdiochan = 1;
			cs4224_diags_prbs_generator_enable(  chan*2+1,  CS4224_PRBS_LINE_INTERFACE,  FALSE);
//      cs4224_diags_prbs_generator_enable(  chan*2,  CS4224_PRBS_HOST_INTERFACE,  FALSE);
			cs4224_diags_prbs_generator_enable(  chan*2+1,  CS4224_PRBS_HOST_INTERFACE,  FALSE);		
		}	
		
	
		
		
		
		  if((TX_Status&0x01)==1) 
			{
//			tx_amp_config(swing_ch[0],0);				
//			prbs_generator_config(pattern[0],0,polarity_ch[0]);	
				mdiochan = 0;
				if(pattern[0]== USER_DEF) cs4224_diags_fix_ptrn_generator_enable(2, CS4224_PRBS_LINE_INTERFACE, TRUE);
				else cs4224_diags_prbs_generator_enable(  2,  CS4224_PRBS_LINE_INTERFACE,  TRUE);
				cs4224_diags_prbs_generator_squelch(2, CS4224_PRBS_LINE_INTERFACE, FALSE);
//				VIEWTECH_71(2,10,90,45,125,10,90);
				VIEWTECH_71(2,5,46,27,71,5,46);
			}
			else if((TX_Status&0x01)==0) 
			{
				mdiochan = 0;
//				cs4224_init_driver_settings(2, CS4224_TX_LINE_INTERFACE, 0x0000, 0x0000);
				if(pattern[0]== USER_DEF) cs4224_diags_fix_ptrn_generator_enable(2, CS4224_PRBS_LINE_INTERFACE, FALSE);
				else cs4224_diags_prbs_generator_enable(  2,  CS4224_PRBS_LINE_INTERFACE,  FALSE);
				cs4224_diags_prbs_generator_squelch(2, CS4224_PRBS_LINE_INTERFACE, TRUE);
//				VIEWTECH_71(25,10,90,45,125,10,90);
				VIEWTECH_71(25,5,46,27,71,5,46);
			}
			
			if((TX_Status&0x02)==2) 
			{
//			tx_amp_config(swing_ch[1],1);				
//			prbs_generator_config(pattern[1],1,polarity_ch[1]);
				mdiochan = 0;
				if(pattern[1]== USER_DEF) cs4224_diags_fix_ptrn_generator_enable(4, CS4224_PRBS_LINE_INTERFACE, TRUE);
				else cs4224_diags_prbs_generator_enable(  4,  CS4224_PRBS_LINE_INTERFACE,  TRUE);
				cs4224_diags_prbs_generator_squelch(4, CS4224_PRBS_LINE_INTERFACE, FALSE);
//				VIEWTECH_71(2,10,170,45,205,10,170);
				VIEWTECH_71(2,5,92,27,117,5,92);
			}
			else if((TX_Status&0x02)==0) 
			{
				mdiochan = 0;
				if(pattern[1]== USER_DEF) cs4224_diags_fix_ptrn_generator_enable(4, CS4224_PRBS_LINE_INTERFACE, FALSE);
				else cs4224_diags_prbs_generator_enable(  4,  CS4224_PRBS_LINE_INTERFACE,  FALSE);
				cs4224_diags_prbs_generator_squelch(4, CS4224_PRBS_LINE_INTERFACE, TRUE);
//				cs4224_init_driver_settings(4, CS4224_TX_LINE_INTERFACE, 0x0000, 0x0000);
//				VIEWTECH_71(25,10,170,45,205,10,170);
				VIEWTECH_71(25,5,92,27,117,5,92);
			}
			
		
			
			if((TX_Status&0x04)==4)
			{
//				tx_amp_config(swing_ch[2],2);				
//				prbs_generator_config(pattern[2],2,polarity_ch[2]);
				mdiochan = 1;
				if(pattern[2]== USER_DEF) cs4224_diags_fix_ptrn_generator_enable(2, CS4224_PRBS_LINE_INTERFACE, TRUE);
				else cs4224_diags_prbs_generator_enable(  2,  CS4224_PRBS_LINE_INTERFACE,  TRUE);
				cs4224_diags_prbs_generator_squelch(2, CS4224_PRBS_LINE_INTERFACE, FALSE);
//				VIEWTECH_71(2,10,250,45,285,10,250);
				VIEWTECH_71(2,5,138,27,163,5,138);
			}
			else if((TX_Status&0x04)==0) 
			{
				mdiochan = 1;
				if(pattern[2]== USER_DEF) cs4224_diags_fix_ptrn_generator_enable(2, CS4224_PRBS_LINE_INTERFACE, FALSE);
				else cs4224_diags_prbs_generator_enable(  2,  CS4224_PRBS_LINE_INTERFACE,  FALSE);
				cs4224_diags_prbs_generator_squelch(2, CS4224_PRBS_LINE_INTERFACE, TRUE);
//				VIEWTECH_71(25,10,250,45,285,10,250);
				VIEWTECH_71(25,5,138,27,163,5,138);
			}
				
			if((TX_Status&0x08)==8) 
			{
//				tx_amp_config(swing_ch[3],3);				
//				prbs_generator_config(pattern[3],3,polarity_ch[3]);
				mdiochan = 1;
				if(pattern[3]== USER_DEF) cs4224_diags_fix_ptrn_generator_enable(4, CS4224_PRBS_LINE_INTERFACE, TRUE);
				else cs4224_diags_prbs_generator_enable(  4,  CS4224_PRBS_LINE_INTERFACE,  TRUE);	
				cs4224_diags_prbs_generator_squelch(4, CS4224_PRBS_LINE_INTERFACE, FALSE);
//				VIEWTECH_71(2,10,330,45,365,10,330);
				VIEWTECH_71(2,5,184,27,209,5,184);
				//update_status();
			}
			else if((TX_Status&0x08)==0) 
			{
				mdiochan = 1;
				if(pattern[3]== USER_DEF) cs4224_diags_fix_ptrn_generator_enable(4, CS4224_PRBS_LINE_INTERFACE, FALSE);
				else cs4224_diags_prbs_generator_enable(  4,  CS4224_PRBS_LINE_INTERFACE,  FALSE);
				cs4224_diags_prbs_generator_squelch(4, CS4224_PRBS_LINE_INTERFACE, TRUE);
//				VIEWTECH_71(25,10,330,45,365,10,330);
				VIEWTECH_71(25,5,184,27,209,5,184);
			}
				
			//IN112510_Write_Reg(30,60,clock_div_value[ClockDivFlag]);
			
			TX_First=1;	
		for(chan=0;chan<4;chan++)
		{							
			prbs_checker_config(pattern[chan],chan,polarity_rx[chan]);
			mdiochan = 0;
      cs4224_diags_prbs_checker_enable(chan*2+1, CS4224_PRBS_LINE_INTERFACE, FALSE);
//			cs4224_diags_prbs_checker_enable(chan*2, CS4224_PRBS_HOST_INTERFACE, FALSE);
			cs4224_diags_prbs_checker_enable(chan*2+1, CS4224_PRBS_HOST_INTERFACE, FALSE);
			mdiochan = 1;
      cs4224_diags_prbs_checker_enable(chan*2+1, CS4224_PRBS_LINE_INTERFACE, FALSE);
//			cs4224_diags_prbs_checker_enable(chan*2, CS4224_PRBS_HOST_INTERFACE, FALSE);
			cs4224_diags_prbs_checker_enable(chan*2+1, CS4224_PRBS_HOST_INTERFACE, FALSE);				
		}
		mdiochan = 0;		
		cs4224_diags_prbs_checker_enable(0, CS4224_PRBS_LINE_INTERFACE, FALSE);	
		cs4224_diags_prbs_checker_enable(6, CS4224_PRBS_LINE_INTERFACE, FALSE);
		cs4224_diags_prbs_checker_enable(0, CS4224_PRBS_HOST_INTERFACE, FALSE);
		cs4224_diags_prbs_checker_enable(6, CS4224_PRBS_HOST_INTERFACE, FALSE);	
		mdiochan = 1;		
		cs4224_diags_prbs_checker_enable(0, CS4224_PRBS_LINE_INTERFACE, FALSE);	
		cs4224_diags_prbs_checker_enable(6, CS4224_PRBS_LINE_INTERFACE, FALSE);
		cs4224_diags_prbs_checker_enable(0, CS4224_PRBS_HOST_INTERFACE, FALSE);
		cs4224_diags_prbs_checker_enable(6, CS4224_PRBS_HOST_INTERFACE, FALSE);			
		
	}
	if((RUN_FLAG == Runing)||(RUN_FLAG1 == Runing1)||(RUN_FLAG2 == Runing2)||(RUN_FLAG3 == Runing3))										/* 检测是否启动测试 */
	{
//		if(START_RUN == First_circle)						/* 检测启动后第一次进入运行循环 */
//		{ 
//		   //配置时钟输入以及误码率计算用到的f_si570			
//     	//amp							
//			//prbs
//					
//			//cs4343_mon_clock_config();	
//		for(chan=0;chan<4;chan++)
//		{							
//			prbs_checker_config(pattern[chan],chan);	
//		}	
//						
//		  START_RUN = nFirst_circle;		/* 将标志设为不是第一次进入运行循环 */  			
//			draw_gang();                 /* 屏幕划线 */
//			
//			BEC_Value_ch[0] = 0;		//将误码数清零，以便下次累加
//			BEC_Value_ch[1] = 0;		//将误码数清零，以便下次累加
//			BEC_Value_ch[2] = 0;		//将误码数清零，以便下次累加
//			BEC_Value_ch[3] = 0;		//将误码数清零，以便下次累加
//      TIME_COUNT = 0;		 
//		}
				
//	  TIM_Cmd(TIM3,ENABLE);//使能TIM3
//	  TIM_ITConfig(TIM3,TIM_IT_Update,ENABLE); //打开500ms更新中断
				
		if(BEC_Read_Status == 1)    //每隔500ms进入一次程序
	 {
		 BEC_Read_Status = 0;		 
		 ++TIME_COUNT;//屏幕时间和误码率计算时间，每500ms加1，计算时取二分之一	 		 
		 if(RUN_FLAG==Runing) {++TIME_COUNT0;++TIME_COUNT00;}
		 if(RUN_FLAG1==Runing1) {++TIME_COUNT1;++TIME_COUNT11;}
		 if(RUN_FLAG2==Runing2) {++TIME_COUNT2;++TIME_COUNT22;}
		 if(RUN_FLAG3==Runing3) {++TIME_COUNT3;++TIME_COUNT33;}		 
	
		 	if(workmode_ch == tramode)	    //传统模式
      {	
		    Tra_Mode();
      } 
     else                          //自动模式
     {
	     Auto_Mode();
     }    
	 } 	 
  }
}



/*******************************************************************************
* Function Name  : SYNC
* Description    : 通道同步判断程序
* Input          : None
* Output         : None
* Return         : 如何通道同步成功，则返回1；不同步，则返回0
* Attention		 : None
*******************************************************************************/
u8 SYNC(u8 chan)
{
	u8 SYNC_FLAG = 0;
	cs_status status = CS_OK;
	cs_uint16 reg_data = 0;
	
//	status |= cs4224_reg_get_channel(chan*2, CS4224_PP_LINE_SDS_COMMON_RXLOCKD0_INTSTATUS, &reg_data);
//	if(0x0 == (reg_data & 0x1))  /* 0 in the PRBS_SYNCs field means synced */
//	{
//			SYNC_FLAG = 0;
//	}
//	else
//	{
//			SYNC_FLAG = 1;
//	}
	switch(chan)
	{
		case 0:
			mdiochan = 0;
			status |= cs4224_reg_get_channel(2, CS4224_PP_LINE_SDS_COMMON_PRBSCHK0_INTSTATUS, &reg_data);//CS4224_PP_LINE_SDS_COMMON_PRBSCHK0_INTSTATUS
			break;
		case 1:
			mdiochan = 0;
			status |= cs4224_reg_get_channel(4, CS4224_PP_LINE_SDS_COMMON_PRBSCHK0_INTSTATUS, &reg_data);
			break;
		case 2:
			mdiochan = 1;
			status |= cs4224_reg_get_channel(2, CS4224_PP_LINE_SDS_COMMON_PRBSCHK0_INTSTATUS, &reg_data);
			break;
		case 3:
			mdiochan = 1;
			status |= cs4224_reg_get_channel(4, CS4224_PP_LINE_SDS_COMMON_PRBSCHK0_INTSTATUS, &reg_data);
			break;
			
	}
	
	if(0x0 == (reg_data & 0x1))  /* 0 in the PRBS_SYNCs field means synced */
	{
			SYNC_FLAG = 1;
	}
	else
	{
			SYNC_FLAG = 0;
	}		
  return SYNC_FLAG;
}


//把4个通道的verify关闭延时再重启
void Verify_Reset(void) 
{
	BEC_Value_ch[0] = 0;		//将误码数清零，以便下次累加
	BEC_Value_ch[1] = 0;		//将误码数清零，以便下次累加
	BEC_Value_ch[2] = 0;		//将误码数清零，以便下次累加
	BEC_Value_ch[3] = 0;		//将误码数清零，以便下次累加	
	BEC_Value_real[0] = 0;		//将误码数清零，以便下次累加
	BEC_Value_real[1] = 0;		//将误码数清零，以便下次累加
	BEC_Value_real[2] = 0;		//将误码数清零，以便下次累加
	BEC_Value_real[3] = 0;		//将误码数清零，以便下次累加	
//  mdioRMW(30,16,0x0000,0x1000);
//	mdioRMW(30,17,0x0000,0x1000);
//	mdioRMW(30,18,0x0000,0x1000);
//	mdioRMW(30,19,0x0000,0x1000);
//	in112510LD_assert_ingress_fifo_reset(); //重启接收FIFO
//	Delay_Ms(100);
//	mdioRMW(30,16,0x1000,0x1000);
//	mdioRMW(30,17,0x1000,0x1000);
//	mdioRMW(30,18,0x1000,0x1000);
//	mdioRMW(30,19,0x1000,0x1000);
}

/*******************************************************************************
* Function Name  : SYNC_full
* Description    : 同步后误码溢出提醒函数
* Input          : None
* Output         : None
* Return         : 如果误码溢出时，则返回1；没有溢出，则返回0
* Attention		 : None
*******************************************************************************/
u8 SYNC_full(u8 chan)		 ///同步误码溢出提醒函数
{
  u8 SYNC_FULL = 0;
  if(BEC_Value_ch[chan] > 900000000000u )   //误码数存储用的是u32，最大值为4294967295  改用u64
//	if(BEC_Value_ch[chan] < BEC_preValue_ch[chan] )
	SYNC_FULL = 1;

  return SYNC_FULL;
}


/*******************************************************************************
* Function Name  : BEC_Read
* Description    : 读取误码数的函数 
* Input          : chan:通道号
* Output         : None
* Return         : ErrorCount_Final：误码数
* Attention		 : None
*******************************************************************************/
uint32_t BEC_Read(u8 chan)
{
  u32 ErrorCount_Final = 0;
	
	cs_status status = CS_OK;
	cs_uint32  prbs_error_count;
 switch(chan)
	{
		case 0:
			mdiochan = 0;
			status |= cs4224_diags_prbs_checker_get_errors(2,CS4224_PRBS_LINE_INTERFACE, &prbs_error_count);
			break;
		case 1:
			mdiochan = 0;
			status |= cs4224_diags_prbs_checker_get_errors(4,CS4224_PRBS_LINE_INTERFACE, &prbs_error_count);
			break;
		case 2:
			mdiochan = 1;
			status |= cs4224_diags_prbs_checker_get_errors(2,CS4224_PRBS_LINE_INTERFACE, &prbs_error_count);
			break;
		case 3:
			mdiochan = 1;
			status |= cs4224_diags_prbs_checker_get_errors(4,CS4224_PRBS_LINE_INTERFACE, &prbs_error_count);
			break;			
	}
		
	if(rate == cpri155) ErrorCount_Final =	(prbs_error_count&0xFFFF);
	else 
     ErrorCount_Final =	prbs_error_count;    //32位误码数
	return ErrorCount_Final;
}

/*******************************************************************************
* Function Name  : BER_Read
* Description    : 读取误码率的函数 
* Input          : chan:通道号
* Output         : None
* Return         : BER：误码率
* Attention		 : None
*******************************************************************************/
double BER_Read(u8 chan)
{
   double BER = 0.0;
  
	//BEC_preValue_ch[chan]=BEC_Value_ch[chan];
	BEC_Value_real[chan] = BEC_Read(chan);  //实时误码数
	BEC_Value_ch[chan]+= BEC_Value_real[chan];   //需累加计算，停止测试按钮按下时，将BEC_Value清零。
 
	//运算求得实际的误码值，如此计算是为了防止计算中发生有效数字丢失的情况//20151027实时显示误码率

	BER = (double)BEC_Value_ch[chan]*1000; 
	switch(chan)
	{
		case 0 :
				if(rate==0) BER = BER/(def_rate*(TIME_COUNT0/2.0));
                else if(rate == cpon) BER = BER/(2488.0*(TIME_COUNT0/2.0));  //CPON
				else if(rate == epon) BER = BER/(1250.0*(TIME_COUNT0/2.0));  //EPON
				else if(rate == gpon) BER = BER/(1244.0*(TIME_COUNT0/2.0));  //GPON
		    	else if(rate == xgpon) BER = BER/(2488.0*(TIME_COUNT0/2.0));  //XGPON
				else if(rate == xgspon) BER = BER/(2488.0*(TIME_COUNT0/2.0));  //XGSPON
	            else BER = BER/(f_si570[rate]*(TIME_COUNT0/2.0));
		break;
		case 1 :
				if(rate==0) BER = BER/(def_rate*(TIME_COUNT1/2.0));
				else if(rate == cpon) BER = BER/(1244.0*(TIME_COUNT1/2.0));  //CPON
				else if(rate == epon) BER = BER/(10312.5*(TIME_COUNT1/2.0));  //EPON
				else if(rate == gpon) BER = BER/(2488.0*(TIME_COUNT1/2.0));  //GPON
		    	else if(rate == xgpon) BER = BER/(9953.28*(TIME_COUNT1/2.0));  //XGPON
				else if(rate == xgspon) BER = BER/(1244.0*(TIME_COUNT1/2.0));  //XGSPON
				else BER = BER/(f_si570[rate]*(TIME_COUNT1/2.0));
		break;
		case 2 :
				if(rate==0) BER = BER/(def_rate*(TIME_COUNT2/2.0));
				else if(rate == cpon) BER = BER/(9953.28*(TIME_COUNT2/2.0));  //CPON
				else if(rate == epon) BER = BER/(10312.5*(TIME_COUNT2/2.0));  //EPON
				else if(rate == gpon) BER = BER/(1244.0*(TIME_COUNT2/2.0));  //GPON
		    	else if(rate == xgpon) BER = BER/(9953.28*(TIME_COUNT2/2.0));  //XGPON
				else if(rate == xgspon) BER = BER/(9953.28*(TIME_COUNT2/2.0));  //XGSPON
				else BER = BER/(f_si570[rate]*(TIME_COUNT2/2.0));
		break;
		case 3 :
				if(rate==0) BER = BER/(def_rate*(TIME_COUNT3/2.0));
				else if(rate == cpon) BER = BER/(2488.0*(TIME_COUNT3/2.0));  //CPON
				else if(rate == epon) BER = BER/(1250.0*(TIME_COUNT3/2.0));  //EPON
				else if(rate == gpon) BER = BER/(2488.0*(TIME_COUNT3/2.0));  //GPON
		    	else if(rate == xgpon) BER = BER/(2488.0*(TIME_COUNT3/2.0));  //XGPON
				else if(rate == xgspon) BER = BER/(9953.28*(TIME_COUNT3/2.0));  //XGSPON
				else BER = BER/(f_si570[rate]*(TIME_COUNT3/2.0));
		break;			
	}
	//BER = BER/16.0;

	return BER;
}



/*******************************************************************************
* Function Name  : Kexue
* Description    : 将误码率转化为科学计数法的函数 
* Input          : chan：通道号
* Output         : None
* Return         : None
* Attention		 : None
*******************************************************************************/
void Kexue(u8 chan)
{   
	  u16 COUNT = 0;			  /* 此两个变量都是用来将误码率转化成科学计数法的 */
    float BER_front = 0.0;	  
    u8 i;
    double a = 0.0;
	  double b;
    uint64_t TMP = 0;
    u32 E_COUNT = 0;
	  u32 PAS;
	
	COUNT = 0;			  /* 此三个变量都是用来将误码率转化成科学计数法的 */
  BER_front = 0.0;

	a =	BER_Read(chan);

	E_COUNT = (u32)a;

	if(E_COUNT > 0)
	{
	    for(i=1; i<10; i++)
	    {									
			  if((u32)(E_COUNT/10) == 0)
			 {
		    	COUNT = i;             //计算BER*e9后的整数个数
			    break;
			 }
			E_COUNT = E_COUNT/10;
	  	}

		if(COUNT > 3)
		{
		 PAS = 1;
		 for(i=0; i<(COUNT-1); i++)
		 {
		  PAS = PAS * 10;
		 }
		 BER_front = a/(double)PAS;
		 COUNT = 10 - COUNT;
		}
		else if(COUNT == 3)
		{
		 BER_front = a/100.0;
		 COUNT = 7;
		}
		else if(COUNT == 2)
		{
		 BER_front = a/10.0;
		 COUNT = 8;
		}
		else if(COUNT == 1)
		{
		 BER_front = a;
		 COUNT = 9;
		}

	}
	else if(E_COUNT == 0)
	{
	     b = a;
		 for(i=1; i<9; i++)
		 {				
		   if(((u32)(b * 10) > 0) | (i == 8 ))
		   {
		   COUNT = i;
		   break;
		   }
		   b = b * 10.0;
		 }
		 if(COUNT == 8)
		 {
		  BER_front = 0.00;
		  COUNT = 16;
		 }
		 else
		 {
		 PAS = 1;
		 for(i=0; i<COUNT; i++)
		 {
		  PAS = PAS * 10;
		 }
		 BER_front = a * (double)PAS;
		 COUNT = 9 + COUNT;
		 }
	}
	 
	   TMP=BEC_Value_ch[chan];
	
		if(TMP> 0x7FFFFFFF)      //触摸屏A01指令最大输出7FFFFFFF
	{
		for(i=12; i>0; i--)
		{
			BEC_lcd[i-1] = TMP%10 + 48;
			TMP = TMP/10;
		}
	}	
	if(BEC_lcd[0] == 48)
	{
		BEC_lcd[0] = ' ';
		if(BEC_lcd[1] == 48)
		{
			BEC_lcd[1] = ' ';
			if(BEC_lcd[2] == 48)
			{
				BEC_lcd[2] = ' ';
			}
		}
	}

	BER_lcd[3] = (u8)(BER_front) + 48;
	BER_lcd[4] = '.';
	BER_lcd[5] = ((u8)(BER_front*10))%10 + 48;
	BER_lcd[6] = ((u16)(BER_front*100))%10 + 48;
	BER_lcd[7] = ((u16)(BER_front*1000))%10 + 48;
	BER_lcd[8] = 'E';
	BER_lcd[9] = '-';
	BER_lcd[10] = (u8)(COUNT/10) + 48;
	BER_lcd[11] = COUNT%10 + 48;
}


void Tra_Mode()
{
}	
/*******************************************************************************
* Function Name  : automode
* Description    : 自动工作程序
* Input          : None
* Output         : None
* Return         : None
* Attention		 : None
*******************************************************************************/
void Auto_Mode(void)
{ 
//	u8 i;
	u8 chan;
	u16 data1=0;
	u16 data2=0;
	int n2;

/*****通道1的同步和误码计算*****/
		for(chan=0; chan<4; chan++)
	 {	
	    if((chan == 0)&&(RUN_FLAG==Runing))
		{
				mdiochan = 0;
			data1=0;
			data2=0;
			cs4224_reg_get(1, 0x305C, &data1);
			cs4224_reg_get(1, 0x3237, &data2);
//			VIEWTECH_A01 (450,380,0x6022,0xf800,0x39c8,data&1);
			if(((data1&1)==0)||((data2&0x20)==0))
			{
					VIEWTECH_98 (138,41,0x21,0x90,1,0xf800,0x85fb,HGANG,12);
			    VIEWTECH_98 (138,61,0x21,0x90,1,0xf800,0x85fb,HGANG,12);
				  VIEWTECH_98 (238,52,0x21,0x90,1,0xf800,0x85fb,FAIL,4);		 /* 失步状态切换 */
				  sta_ch0 = 0;   
				 
				  sync_ch0_laststate = 0;
			}
			else
			{

		if(START_RUN == First_circle)						/* 检测启动后第一次进入运行循环 */
		{ 
//			prbs_checker_config(pattern[chan],chan,polarity_rx[chan]);	
						
		  START_RUN = nFirst_circle;		/* 将标志设为不是第一次进入运行循环 */  			

				VIEWTECH_98 (138,41,0x21,0x90,1,0x0000,0x85fb,HGANG,12);
			    VIEWTECH_98 (138,61,0x21,0x90,1,0x0000,0x85fb,HGANG,12);	
			sync_ch0_laststate = 1 ;
			START_SYNC1 = SYNC1_First;	
      reseton1 = 1;			
		}
			if(reseton1 > 0 )
			{
				BEC_Read(chan); 
				if(TIME_COUNT00 > 0) TIME_COUNT00--;
			if(reseton1) reseton1--;
			else 	reseton1=0;
			}
      else{			
			if(SYNC(chan) == SYNC_succeeded)						 /* 检测是否同步成功 */
		 {
			 if(sync_ch0_laststate == 0)   //从fail到link的过程，先延时750ms，再清零误码和屏幕计时
		   {
//			   Delay_Ms(500);					 
			   TIME_COUNT00 = 0;
			   sync_ch0_laststate = 1;	
				START_SYNC1 = SYNC1_First;					 
					 //Verify_Reset(); 
				//VIEWTECH_A01 (500,420,0x6022,0xf800,0x85fb,222);			 
//			   VIEWTECH_98 (390,97,0x22,0x90,3,0x001f,0x85fb,LINK,4);		  /* 同步状态切换 */
//				 VIEWTECH_98 (235,80,0x22,0x90,3,0x001f,0x85fb,BEC_lcd_init,12);
//				 sta_ch0 = 1;  
//				 BEC_Read(chan);  //复位后读一次，清除偶尔出现的误码				 
       }
		  	 if(START_SYNC1 == SYNC1_First)					  /* 同步成功后第一次进入循环*/
		    {
			   BEC_Value_ch[0] = 0; 
					BEC_Value_real[0] = 0;
					VIEWTECH_98 (238,52,0x21,0x90,1,0x001f,0x85fb,LINK,4);		  /* 同步状态切换 */
					VIEWTECH_98 (138,41,0x21,0x90,1,0x001f,0x85fb,BEC_lcd_init,12);
				 sta_ch0 = 1;   
				 BEC_Read(chan);   //复位后读一次，清除偶尔出现的误码	
			   START_SYNC1 =  SYNC1_nFirst;
					TIME_COUNT00 = 0;
					
			  }			
	
			if(SYNC_full(chan) == 1)			//1为误码溢出,0为误码没溢出
			 {
			 VIEWTECH_98 (238,52,0x21,0x90,1,0xf800,0x85fb,FULL,4);		 /* 误码溢出 */
				sta_ch0 = 2;
			 }
			else
			 {
				 Kexue(chan);													   /* 将误码率转化为科学计数法 */
				   VIEWTECH_98 (138,61,0x21,0x90,1,0x001f,0x85fb,BER_lcd,12);
					
				 if(BEC_Value_ch[chan]> 0x7FFFFFFF)      //触摸屏A01指令最大输出7FFFFFFF	
				   VIEWTECH_98 (138,41,0x21,0x90,1,0x001f,0x85fb,BEC_lcd,12);
				 
				 else				
				 VIEWTECH_A01 (154,41,0xa061,0x001f,0x85fb,BEC_Value_ch[0]);	   /* 误码数显示 */		
				 
			 }
		 }
		 else if(SYNC(chan) == SYNC_failed)						   /* 检测是否同步失败 */
			 {
			    VIEWTECH_98 (138,41,0x21,0x90,1,0xf800,0x85fb,HGANG,12);
			    VIEWTECH_98 (138,61,0x21,0x90,1,0xf800,0x85fb,HGANG,12);
				  VIEWTECH_98 (238,52,0x21,0x90,1,0xf800,0x85fb,FAIL,4);		 /* 失步状态切换 */
				  sta_ch0 = 0;   
				 
				  sync_ch0_laststate = 0;
			 }}}
		}
		
		  /*****通道2的同步和误码计算*****/
	else if((chan == 1)&&(RUN_FLAG1==Runing1))
		{
			mdiochan = 0;
			data1=0;
			data2=0;
			cs4224_reg_get(0, 0x405C, &data1);
			cs4224_reg_get(0, 0x4237, &data2);
//			VIEWTECH_A01 (450,380,0x6022,0xf800,0x39c8,data&1);
			if(((data1&1)==0)||((data2&0x20)==0))
			{
					VIEWTECH_98 (138,87,0x21,0x90,1,0xf800,0x85fb,HGANG,12);
			    VIEWTECH_98 (138,107,0x21,0x90,1,0xf800,0x85fb,HGANG,12);
				  VIEWTECH_98 (238,98,0x21,0x90,1,0xf800,0x85fb,FAIL,4);		 /* 失步状态切换 */
				sta_ch1 = 0;
			  sync_ch1_laststate = 0;
			}
			else
			{
			
					if(START_RUN1 == First1_circle)						/* 检测启动后第一次进入运行循环 */
		{ 
//			prbs_checker_config(pattern[chan],chan,polarity_rx[chan]);	
						
		  START_RUN1 = nFirst1_circle;		/* 将标志设为不是第一次进入运行循环 */  			

//					VIEWTECH_98 (235,160,0x22,0x90,3,0x0000,0x39c8,HGANG,12);
//			    VIEWTECH_98 (235,194,0x22,0x90,3,0x0000,0x39c8,HGANG,12);	
				VIEWTECH_98 (138,87,0x21,0x90,1,0x0000,0x85fb,HGANG,12);
			    VIEWTECH_98 (138,107,0x21,0x90,1,0x0000,0x85fb,HGANG,12);		
			sync_ch1_laststate = 1 ;
			START_SYNC2 = SYNC2_First;
		reseton2 = 1;			
		}
			if(reseton2 > 0 )
			{
				BEC_Read(chan); 
				if(TIME_COUNT11 > 0) TIME_COUNT11--;
			if(reseton2) reseton2--;
			else 	reseton2=0;
			}
      else{	
			
			
		 	 if(SYNC(chan) == SYNC_succeeded)						 /* 检测是否同步成功 */
		  {
			 if(sync_ch1_laststate == 0)   //从fail到link的过程，先延时3s，再清零误码和屏幕计时
		   {
		   START_SYNC2 = SYNC2_First;		
			   TIME_COUNT11 = 0;
			   sync_ch1_laststate = 1;				 
				 //Verify_Reset(); 			 
			   //VIEWTECH_A01 (500,420,0x6022,0xf800,0x85fb,444);
//			   VIEWTECH_98 (390,177,0x22,0x90,3,0x001f,0x85fb,LINK,4);		  /* 同步状态切换 */
//				 VIEWTECH_98 (235,160,0x22,0x90,3,0x001f,0x85fb,BEC_lcd_init,12);
//				 sta_ch1 = 1;
//         BEC_Read(chan);  	//复位后读一次，清除偶尔出现的误码				 				 
       }				 
		  	if(START_SYNC2 == SYNC2_First)					  /* 同步成功后第一次进入循环*/
		    {
					BEC_Value_ch[1] = 0;
					BEC_Value_real[1] = 0;
					VIEWTECH_98 (238,98,0x21,0x90,1,0x001f,0x85fb,LINK,4);		  /* 同步状态切换 */
				VIEWTECH_98 (138,87,0x21,0x90,1,0x001f,0x85fb,BEC_lcd_init,12);
					sta_ch1 = 1;   
					BEC_Read(chan);  //复位后读一次，清除偶尔出现的误码	
					START_SYNC2 = SYNC2_nFirst;
					TIME_COUNT11 = 0;
			  }

		  if(SYNC_full(chan) == 1)			//1为误码溢出,0为误码没溢出
			 {
//			  VIEWTECH_98 (390,177,0x22,0x90,3,0xf800,0x39c8,FULL,4);		 /* 误码溢出 */
			VIEWTECH_98 (238,98,0x21,0x90,1,0xf800,0x85fb,FULL,4);		 /* 误码溢出 */
				sta_ch1 = 2; 
			 }
			else
			 {
				 Kexue(chan);													   /* 将误码率转化为科学计数法 */
//				 VIEWTECH_98 (235,194,0x22,0x90,3,0x001f,0x39c8,BER_lcd,12);
				VIEWTECH_98 (138,107,0x21,0x90,1,0x001f,0x85fb,BER_lcd,12);
				 
				 if(BEC_Value_ch[chan]> 0x7FFFFFFF)      //触摸屏A01指令最大输出7FFFFFFF	
//				   VIEWTECH_98 (235,160,0x22,0x90,3,0x001f,0x39c8,BEC_lcd,12);
				VIEWTECH_98 (138,87,0x21,0x90,1,0x001f,0x85fb,BEC_lcd,12);				 
				 else				
				 {
//					 VIEWTECH_A01 (259,160,0xa062,0x001f,0x39c8,BEC_Value_ch[1]);	   /* 误码数显示 */
					VIEWTECH_A01 (154,87,0xa061,0x001f,0x85fb,BEC_Value_ch[1]);	   /* 误码数显示 */		
				 }  				
			 }			 
		 }
		 else if(SYNC(chan) == SYNC_failed)						   /* 检测是否同步失败 */
		 {
//				VIEWTECH_98 (235,160,0x22,0x90,3,0xf800,0x39c8,HGANG,12);
//				VIEWTECH_98 (235,194,0x22,0x90,3,0xf800,0x39c8,HGANG,12);
//				VIEWTECH_98 (390,177,0x22,0x90,3,0xf800,0x39c8,FAIL,4);		 /* 失步状态切换 */
				VIEWTECH_98 (138,87,0x21,0x90,1,0xf800,0x85fb,HGANG,12);
			    VIEWTECH_98 (138,107,0x21,0x90,1,0xf800,0x85fb,HGANG,12);
				  VIEWTECH_98 (238,98,0x21,0x90,1,0xf800,0x85fb,FAIL,4);		 /* 失步状态切换 */
				sta_ch1 = 0;
			  sync_ch1_laststate = 0;
		 }}}
		}	
		
		
		  /*****通道3的同步和误码计算*****/
		else if((chan == 2)&&(RUN_FLAG2==Runing2))
		{
						mdiochan = 1;
			data1=0;
			data2=0;
			cs4224_reg_get(1, 0x305C, &data1);
			cs4224_reg_get(1, 0x3237, &data2);
//			VIEWTECH_A01 (450,380,0x6022,0xf800,0x39c8,data&1);
			if(((data1&1)==0)||((data2&0x20)==0))
			{
					VIEWTECH_98 (138,133,0x21,0x90,1,0xf800,0x85fb,HGANG,12);
			    VIEWTECH_98 (138,153,0x21,0x90,1,0xf800,0x85fb,HGANG,12);
				  VIEWTECH_98 (238,144,0x21,0x90,1,0xf800,0x85fb,FAIL,4);		 /* 失步状态切换 */
				  sta_ch2 = 0;
				  sync_ch2_laststate = 0;
			}
			else
			{
			
			
								if(START_RUN2 == First2_circle)						/* 检测启动后第一次进入运行循环 */
		{ 
//			prbs_checker_config(pattern[chan],chan,polarity_rx[chan]);	
						
		  START_RUN2 = nFirst2_circle;		/* 将标志设为不是第一次进入运行循环 */  			

//					VIEWTECH_98 (235,240,0x22,0x90,3,0x0000,0x85fb,HGANG,12);
//			    VIEWTECH_98 (235,274,0x22,0x90,3,0x0000,0x85fb,HGANG,12);
				VIEWTECH_98 (138,133,0x21,0x90,1,0x0000,0x85fb,HGANG,12);
			    VIEWTECH_98 (138,153,0x21,0x90,1,0x0000,0x85fb,HGANG,12);			
			sync_ch2_laststate = 1 ;
			START_SYNC3 = SYNC3_First;
		reseton3 = 1;			
		}
			if(reseton3 > 0 )
			{
				BEC_Read(chan); 
				if(TIME_COUNT22 > 0) TIME_COUNT22--;
			if(reseton3) reseton3--;
			else 	reseton3=0;
			}
      else{	
			
			
		 	 if(SYNC(chan) == SYNC_succeeded)						 /* 检测是否同步成功 */
		 {
//				link3++;
//			  if(link3>1)
//				{
								 if(sync_ch2_laststate == 0)   //从fail到link的过程，先延时3s，再清零误码和屏幕计时
		   {
				 START_SYNC3 = SYNC3_First;
			   TIME_COUNT22 = 0;
			   sync_ch2_laststate = 1;
				 //				 Verify_Reset(); 	 
				 //VIEWTECH_A01 (500,420,0x6022,0xf800,0x85fb,999);
//			   VIEWTECH_98 (390,257,0x22,0x90,3,0x001f,0x85fb,LINK,4);		  /* 同步状态切换 */
//				 VIEWTECH_98 (235,240,0x22,0x90,3,0x001f,0x85fb,BEC_lcd_init,12);
//				 sta_ch2 = 1; 
//				 BEC_Read(chan);     //复位后读一次，清除偶尔出现的误码	          				 
       }	
			 
		  	if(START_SYNC3 == SYNC3_First)					  /* 同步成功后第一次进入循环*/
		    {
					BEC_Value_ch[2] = 0;
					BEC_Value_real[2] = 0;
//					VIEWTECH_98 (390,257,0x22,0x90,3,0x001f,0x85fb,LINK,4);		  /* 同步状态切换 */
//					VIEWTECH_98 (235,240,0x22,0x90,3,0x001f,0x85fb,BEC_lcd_init,12);
					VIEWTECH_98 (238,144,0x21,0x90,1,0x001f,0x85fb,LINK,4);		  /* 同步状态切换 */
					VIEWTECH_98 (138,133,0x21,0x90,1,0x001f,0x85fb,BEC_lcd_init,12);
					sta_ch2 = 1;   
					BEC_Read(chan);  //复位后读一次，清除偶尔出现的误码	
					START_SYNC3 =  SYNC3_nFirst;
					TIME_COUNT22 = 0;
			  }

	      if(SYNC_full(chan) == 1)			//1为误码溢出,0为误码没溢出
			 {
			   //			  VIEWTECH_98 (390,97,0x22,0x90,3,0xf800,0x39c8,FULL,4);		 /* 误码溢出 */
				VIEWTECH_98 (238,144,0x21,0x90,1,0xf800,0x85fb,FULL,4);		 /* 误码溢出 */
				 sta_ch2 = 2;
			 }
			 else
			 {
				 Kexue(chan);													   /* 将误码率转化为科学计数法 */
				 //VIEWTECH_98 (235,274,0x22,0x90,3,0x001f,0x85fb,BER_lcd,12);
				 VIEWTECH_98 (138,153,0x21,0x90,1,0x001f,0x85fb,BER_lcd,12);
				 if(BEC_Value_ch[chan]> 0x7FFFFFFF)      //触摸屏A01指令最大输出7FFFFFFF	
				    VIEWTECH_98 (138,133,0x21,0x90,1,0x001f,0x85fb,BEC_lcd,12);
				 
				 else								   
				   //VIEWTECH_A01 (259,240,0xa062,0x001f,0x85fb,BEC_Value_ch[2]);	   /* 误码数显示 */
				   	VIEWTECH_A01 (154,133,0xa061,0x001f,0x85fb,BEC_Value_ch[2]);				  
		 	 }
		 }
		 else if(SYNC(chan) == SYNC_failed)						   /* 检测是否同步失败 */
			 {
			    //VIEWTECH_98 (235,240,0x22,0x90,3,0xf800,0x85fb,HGANG,12);
			   // VIEWTECH_98 (235,274,0x22,0x90,3,0xf800,0x85fb,HGANG,12);
				//  VIEWTECH_98 (390,257,0x22,0x90,3,0xf800,0x85fb,FAIL,4);		 /* 失步状态切换 */
				VIEWTECH_98 (138,133,0x21,0x90,1,0xf800,0x85fb,HGANG,12);
			    VIEWTECH_98 (138,153,0x21,0x90,1,0xf800,0x85fb,HGANG,12);
				  VIEWTECH_98 (238,144,0x21,0x90,1,0xf800,0x85fb,FAIL,4);		 /* 失步状态切换 */
				  sta_ch2 = 0;
				  sync_ch2_laststate = 0;
			 }}}
		}
		  /*****通道4的同步和误码计算*****/
		else if((chan == 3)&&(RUN_FLAG3==Runing3))
		{
			
						//			cs4224_reg_get(1, 0x3237, &data);
//			VIEWTECH_A01 (450,420,0x6022,0xf800,0x39c8,data&0x3F);
			mdiochan = 1;
			data1=0;
			data2=0;
			cs4224_reg_get(0, 0x405C, &data1);
			cs4224_reg_get(0, 0x4237, &data2);
//			VIEWTECH_A01 (450,380,0x6022,0xf800,0x39c8,data&1);
			if(((data1&1)==0)||((data2&0x20)==0))
			{
					VIEWTECH_98 (138,179,0x21,0x90,1,0xf800,0x85fb,HGANG,12);
			    VIEWTECH_98 (138,199,0x21,0x90,1,0xf800,0x85fb,HGANG,12);
				  VIEWTECH_98 (238,190,0x21,0x90,1,0xf800,0x85fb,FAIL,4);		 /* 失步状态切换 */
				  sta_ch3 = 0; 				 
				  sync_ch3_laststate = 0;
			}
			else
			{
			
			if(START_RUN3 == First3_circle)						/* 检测启动后第一次进入运行循环 */
		{ 
			

			
//			prbs_checker_config(pattern[chan],chan,polarity_rx[chan]);	
						
		  START_RUN3 = nFirst3_circle;		/* 将标志设为不是第一次进入运行循环 */  			

			//		VIEWTECH_98 (235,320,0x22,0x90,3,0x0000,0x85fb,HGANG,12);
			 //   VIEWTECH_98 (235,354,0x22,0x90,3,0x0000,0x85fb,HGANG,12);	
			 VIEWTECH_98 (138,179,0x21,0x90,1,0x0000,0x85fb,HGANG,12);
			    VIEWTECH_98 (138,199,0x21,0x90,1,0x0000,0x85fb,HGANG,12);		
			sync_ch3_laststate = 1 ;
			START_SYNC4 = SYNC4_First;
		reseton4 = 1;			
		}
			if(reseton4 > 0 )
			{
				BEC_Read(chan); 
				if(TIME_COUNT33 > 0) TIME_COUNT33--;
			if(reseton4) reseton4--;
			else 	reseton4=0;
			}
      else{	
			
		 	 if(SYNC(chan) == SYNC_succeeded)						 /* 检测是否同步成功 */
		 {
			 if(sync_ch3_laststate == 0)   //从fail到link的过程，先延时3s，再清零误码和屏幕计时
		   {
         START_SYNC4 = SYNC4_First;
			   TIME_COUNT33 = 0;
			   sync_ch3_laststate = 1;
				   //Verify_Reset(); 
				//VIEWTECH_A01 (500,420,0x6022,0xf800,0x85fb,777); 
//			   VIEWTECH_98 (390,337,0x22,0x90,3,0x001f,0x85fb,LINK,4);		  /* 同步状态切换 */
//				 VIEWTECH_98 (235,320,0x22,0x90,3,0x001f,0x85fb,BEC_lcd_init,12);
//				 sta_ch3 = 1; 
 //        BEC_Read(chan);  	//复位后读一次，清除偶尔出现的误码				 
       }				 
		  	 if(START_SYNC4 == SYNC4_First)					  /* 同步成功后第一次进入循环*/
		    {
			  BEC_Value_ch[3] = 0;
				BEC_Value_real[3] = 0;
			//		VIEWTECH_98 (390,337,0x22,0x90,3,0x001f,0x85fb,LINK,4);		  /* 同步状态切换 */
			//	VIEWTECH_98 (235,320,0x22,0x90,3,0x001f,0x85fb,BEC_lcd_init,12);
			VIEWTECH_98 (238,190,0x21,0x90,1,0x001f,0x85fb,LINK,4);		  /* 同步状态切换 */
				VIEWTECH_98 (138,179,0x21,0x90,1,0x001f,0x85fb,BEC_lcd_init,12);
				sta_ch3 = 1;
				BEC_Read(chan);    //复位后读一次，清除偶尔出现的误码	
			  START_SYNC4 = SYNC4_nFirst;
					TIME_COUNT33 = 0;
			  }
			  
			if(SYNC_full(chan) == 1)			//1为误码溢出,0为误码没溢出
			 {
			  //VIEWTECH_98 (390,337,0x22,0x90,3,0xf800,0x85fb,FULL,4);		 /* 误码溢出 */
			  VIEWTECH_98 (238,190,0x21,0x90,1,0xf800,0x85fb,FULL,4);		 /* 误码溢出 */
				sta_ch3 = 2; 
			 }
			else
			 {
				 Kexue(chan);													   /* 将误码率转化为科学计数法 */
				 //VIEWTECH_98 (235,354,0x22,0x90,3,0x001f,0x85fb,BER_lcd,12);
				 VIEWTECH_98 (138,199,0x21,0x90,1,0x001f,0x85fb,BER_lcd,12);
				 if(BEC_Value_ch[chan]> 0x7FFFFFFF)      //触摸屏A01指令最大输出7FFFFFFF	
				  // VIEWTECH_98 (235,320,0x22,0x90,3,0x001f,0x85fb,BEC_lcd,12);
				 	VIEWTECH_98 (138,179,0x21,0x90,1,0x001f,0x85fb,BEC_lcd,12);	
				 else								  
				   //VIEWTECH_A01 (259,320,0xa062,0x001f,0x85fb,BEC_Value_ch[3]);	   /* 误码数显示 */
				   VIEWTECH_A01 (154,179,0xa061,0x001f,0x85fb,BEC_Value_ch[3]);						   
			 }
		 }
		 else if(SYNC(chan) == SYNC_failed)						   /* 检测是否同步失败 */
			 {
			   // VIEWTECH_98 (235,320,0x22,0x90,3,0xf800,0x85fb,HGANG,12);
			   // VIEWTECH_98 (235,354,0x22,0x90,3,0xf800,0x85fb,HGANG,12);
				//  VIEWTECH_98 (390,337,0x22,0x90,3,0xf800,0x85fb,FAIL,4);		 /* 失步状态切换 */
				VIEWTECH_98 (138,179,0x21,0x90,1,0xf800,0x85fb,HGANG,12);
			    VIEWTECH_98 (138,199,0x21,0x90,1,0xf800,0x85fb,HGANG,12);
				  VIEWTECH_98 (238,190,0x21,0x90,1,0xf800,0x85fb,FAIL,4);		 /* 失步状态切换 */
				  sta_ch3 = 0; 
				 
				  sync_ch3_laststate = 0;
			 }}}
		}        
	}
	
	  /*屏幕跑秒和定时是否结束的判断*/
		 if(RUN_FLAG==Runing){	 
	if((((u32)(TIME_COUNT0/2)) >= timer_set[0]) &&  (mode[0] == TIMER_EN )) /* 检测测试是否完毕,+1是为了让屏幕显示30,120,600,1800 *///20151023.4.02去掉了查看8248内部是否检测完毕//
		 {
		    Delay_Ms(10);
	  		//VIEWTECH_71(20,626,117,780,141,626,117);		  /* 测试完毕切换 */
			VIEWTECH_71(20,402,62,468,77,402,62);
		  	Icon = NONE_PRESS;
			//	VIEWTECH_71(26,455,94,598,129,455,94);
					VIEWTECH_71(26,273,50,359,73,273,50);			
			START_RUN = First_circle;
			START_SYNC1 = SYNC1_First;
			RUN_FLAG = noRuning;
//			 cs4224_diags_prbs_checker_enable( 0,   CS4224_PRBS_LINE_INTERFACE,  FALSE);
//			TIME_COUNT0 = 0;
		 }
		 else
		 {			
			//VIEWTECH_71(12,626,117,780,141,626,117);
			//VIEWTECH_A01 (670,114,0x6022,0xf800,0x39c8,((u32)(TIME_COUNT0/2)));
			VIEWTECH_71(12,402,62,468,77,402,62);
			VIEWTECH_A01 (396,60,0x6021,0xf800,0x39c8,((u32)(TIME_COUNT0/2)));	
			 }
		 }
		 
		 		 if(RUN_FLAG1==Runing1) {
		 if((((u32)(TIME_COUNT1/2)) >= timer_set[1]) &&  (mode[1] == TIMER_EN )) /* 检测测试是否完毕,+1是为了让屏幕显示30,120,600,1800 *///20151023.4.02去掉了查看8248内部是否检测完毕//
		 {
		    Delay_Ms(10);
//	  		VIEWTECH_71(20,626,117,780,141,626,117);		  /* 测试完毕切换 */
			// VIEWTECH_71(20,626,195,780,219,626,195);
			VIEWTECH_71(20,402,108,468,123,402,108);
//			 VIEWTECH_71(20,626,273,780,297,626,273);
//			 VIEWTECH_71(20,626,356,780,380,626,356);
		  	Icon = NONE_PRESS;
			//	VIEWTECH_71(26,455,174,598,209,455,174);
			VIEWTECH_71(26,273,96,359,119,273,96);			
			START_RUN1 = First1_circle;
			START_SYNC2 = SYNC2_First;
			RUN_FLAG1 = noRuning1;
//			 cs4224_diags_prbs_checker_enable( 2,   CS4224_PRBS_LINE_INTERFACE,  FALSE);
//			TIME_COUNT1 = 0;
		 }
		 else
		 {			 
		//	 VIEWTECH_71(12,626,195,780,219,626,195);
		//	VIEWTECH_A01 (670,192,0x6022,0xf800,0x39c8,((u32)(TIME_COUNT1/2)));
		VIEWTECH_71(12,402,108,468,123,402,108);
			VIEWTECH_A01 (396,106,0x6021,0xf800,0x39c8,((u32)(TIME_COUNT1/2)));
			 }
		 }
				 
		  if(RUN_FLAG2==Runing2){
		 if((((u32)(TIME_COUNT2/2)) >= timer_set[2]) &&  (mode[2] == TIMER_EN )) /* 检测测试是否完毕,+1是为了让屏幕显示30,120,600,1800 *///20151023.4.02去掉了查看8248内部是否检测完毕//
		 {
		    Delay_Ms(10);
//	  		VIEWTECH_71(20,626,117,780,141,626,117);		  /* 测试完毕切换 */
//			 VIEWTECH_71(20,626,195,780,219,626,195);
			 VIEWTECH_71(20,402,154,468,169,402,154);
//			 VIEWTECH_71(20,626,356,780,380,626,356);
		  	Icon = NONE_PRESS;
				VIEWTECH_71(26,273,142,359,165,273,142);			
			START_RUN2 = First2_circle;
			START_SYNC3 = SYNC3_First;
			RUN_FLAG2 = noRuning2;
//			 cs4224_diags_prbs_checker_enable( 4,   CS4224_PRBS_LINE_INTERFACE,  FALSE);
//			TIME_COUNT2 = 0;
		 }
		 else
		 {			 
			// VIEWTECH_71(12,626,273,780,297,626,273);
			//VIEWTECH_A01 (670,270,0x6022,0xf800,0x39c8,((u32)(TIME_COUNT2/2)));
			VIEWTECH_71(12,402,154,468,169,402,154);
			VIEWTECH_A01 (396,152,0x6021,0xf800,0x39c8,((u32)(TIME_COUNT2/2)));
			 }
		 }
			
		 if(RUN_FLAG3==Runing3){
		 if((((u32)(TIME_COUNT3/2)) >= timer_set[3]) &&  (mode[3] == TIMER_EN )) /* 检测测试是否完毕,+1是为了让屏幕显示30,120,600,1800 *///20151023.4.02去掉了查看8248内部是否检测完毕//
		 {
		    Delay_Ms(10);
//	  		VIEWTECH_71(20,626,117,780,141,626,117);		  /* 测试完毕切换 */
//			 VIEWTECH_71(20,626,195,780,219,626,195);
//			 VIEWTECH_71(20,626,273,780,297,626,273);
			 VIEWTECH_71(20,402,200,468,215,402,200);
		  	Icon = NONE_PRESS;
			VIEWTECH_71(26,273,188,359,210,273,188);			
			START_RUN3 = First3_circle;
			START_SYNC4 = SYNC4_First;
			RUN_FLAG3 = noRuning3;
//			 cs4224_diags_prbs_checker_enable( 6,   CS4224_PRBS_LINE_INTERFACE,  FALSE);
//			TIME_COUNT3 = 0;
		 }
		 else
		 {
			 
			// VIEWTECH_71(12,626,356,780,380,626,356);
			//VIEWTECH_A01 (670,352,0x6022,0xf800,0x39c8,((u32)(TIME_COUNT3/2)));
			VIEWTECH_71(12,402,200,468,215,402,200);
			VIEWTECH_A01 (396,198,0x6021,0xf800,0x39c8,((u32)(TIME_COUNT3/2)));
			 }
		 }
		 
	 
}

void Para_Ini(void)  	//误码测试过程的一些参数归位，以便下次测试。
{
//	BEC_Value_ch[0] = 0;		//将误码数清零，以便下次累加
//	BEC_Value_ch[1] = 0;		//将误码数清零，以便下次累加
//	BEC_Value_ch[2] = 0;		//将误码数清零，以便下次累加
//	BEC_Value_ch[3] = 0;		//将误码数清零，以便下次累加
	VIEWTECH_71(11,551,321,719,388,551,321);      /* 开始 切换 */
	START_RUN = First_circle;
	START_RUN1 = First1_circle;
	START_RUN2 = First2_circle;
	START_RUN3 = First3_circle;
	START_SYNC1 = SYNC1_First;
	START_SYNC2 = SYNC2_First;
	START_SYNC3 = SYNC3_First;
	START_SYNC4 = SYNC4_First;
	RUN_FLAG = noRuning;
	RUN_FLAG1 = noRuning1;
	RUN_FLAG2 = noRuning2;
	RUN_FLAG3 = noRuning3;
	TIME_COUNT = 0;
}

void cs4343_diag_fixpattern_checker(int chan)
{
    cs_status status = CS_OK;    
    cs_uint32  prbs_error_count;
    //cs_boolean prbs_sync;

    cs_uint32 slice;
	 	
			switch(chan)
	{
		case 0:
			mdiochan = 0;
		  slice = 2;
			break;
		case 1:
			mdiochan = 0;
		  slice = 4;
			break;
		case 2:
			mdiochan = 1;
		  slice = 2;
			break;
		case 3:
			mdiochan = 1;
		  slice = 4;
			break;
	}

		status |= cs4224_diags_fix_ptrn_generator_enable(slice, CS4224_PRBS_LINE_INTERFACE, TRUE);
    /* configure the PRBS checker on slice 0 */
    status |= cs4224_diags_prbs_checker_config(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp31, 
                  FALSE, 
                  FALSE);


    /* Turn on the PRBS checker on slice 0 */
    status |= cs4224_diags_prbs_checker_enable(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  TRUE);


    /* clear any initial PRBS errors */
    status |= cs4224_diags_prbs_checker_get_errors(
                  slice,
                  CS4224_PRBS_LINE_INTERFACE,
                  &prbs_error_count);
		//		}

    //return status;	
}


void cs4343_diag_fixpattern_generator(int chan)
{
    cs_status status = CS_OK;    
//    cs_uint32  prbs_error_count;
    //cs_boolean prbs_sync;

    cs_uint32 slice;
	  u32 sqa,sqb;
	
	  sqa =(u32) (udp[chan][0] + (udp[chan][1]<<8) + ( udp[chan][2]<<16 ) + (udp[chan][3]<<24));
	  sqb =(u32) (udp[chan][4] + (udp[chan][5]<<8) + ( udp[chan][6]<<16 ) + (udp[chan][7]<<24));				
			switch(chan)
	{
		case 0:
			mdiochan = 0;
		  slice = 2;
			break;
		case 1:
			mdiochan = 0;
		  slice = 4;
			break;
		case 2:
			mdiochan = 1;
		  slice = 2;
			break;
		case 3:
			mdiochan = 1;
		  slice = 4;
			break;
	}
    //for(slice=0;slice<8;slice=slice+2)
	//{
    /* configure the PRBS generator on slice 0 and
     * put it in PFD mode */
    status |= cs4224_diags_prbs_generator_set_pfd_mode(
                  slice,
                  CS4224_PRBS_LINE_INTERFACE,
                  TRUE);

//    status |= cs4224_diags_prbs_generator_config(
//                  slice,  
//                  CS4224_PRBS_LINE_INTERFACE, 
//                  CS4224_PRBS_Tx_2exp9_5, 
//                  FALSE);

//    /* Turn on the PRBS generator on slice 0 */
//    status |= cs4224_diags_prbs_generator_enable(
//                  slice,  
//                  CS4224_PRBS_LINE_INTERFACE, 
//                  TRUE);
		status |= cs4224_diags_fix_ptrn_generator_cfg(slice, CS4224_PRBS_LINE_INTERFACE, sqb,1,sqa,1);
//		status |= cs4224_diags_fix_ptrn_generator_enable(slice, CS4224_PRBS_LINE_INTERFACE, TRUE);
    /* configure the PRBS checker on slice 0 */
//    status |= cs4224_diags_prbs_checker_config(
//                  slice,    
//                  CS4224_PRBS_LINE_INTERFACE, 
//                  CS4224_PRBS_Tx_2exp31, 
//                  FALSE, 
//                  FALSE);


//    /* Turn on the PRBS checker on slice 0 */
//    status |= cs4224_diags_prbs_checker_enable(
//                  slice,    
//                  CS4224_PRBS_LINE_INTERFACE, 
//                  TRUE);


//    /* clear any initial PRBS errors */
//    status |= cs4224_diags_prbs_checker_get_errors(
//                  slice,
//                  CS4224_PRBS_LINE_INTERFACE,
//                  &prbs_error_count);
		//		}

    //return status;
	
}

