

#ifndef __LCD_INTERFACE_H
#define __LCD_INTERFACE_H

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"  
					  
/* Private define ------------------------------------------------------------*/
#define   No_touch           0					   /* 触屏标志定义 */
#define   Yes_touch          1
#define   Time_read          2
#define   Virtual_keyboard   3

#define   No_Order            0	         /*上位机指令标志定义 */
#define   Down_Startrx          1					  //开始停止rx标记
#define   Up_Config           2           //上传下位机配置程序标记
#define   Down_Setup          3           //下传用户配置标记
#define   Up_Ber              4           //上传误码率等信息标记
#define   Down_Starttx          5					  //开始停止tx标记
#define   Up_Eye              6
#define   Touch_On              7
#define   Up_iBer              8 
//#define   Div_Set             8
//#define   Up_Div              9
//#define   userRate_Set        10
//#define   uRate_Set            11

#define   noRuning       0					  	/* 检测开始与否标志定义 */
#define   Runing         1

#define   noRuning1       0	
#define   Runing1         1

#define   noRuning2       0	
#define   Runing2         1

#define   noRuning3       0	
#define   Runing3         1

#define   txOff        0					  	/* 检测tx开始与否标志定义 */
#define   txOn         1

#define   First_circle   0						/* 第一回合进入运行标志 */
#define   nFirst_circle	 1

#define   First1_circle   0						/* 第一回合进入运行标志 */
#define   nFirst1_circle	 1

#define   First2_circle   0						/* 第一回合进入运行标志 */
#define   nFirst2_circle	 1

#define   First3_circle   0						/* 第一回合进入运行标志 */
#define   nFirst3_circle	 1




#define   SYNC_succeeded 1						/* 同步与否标志 */
#define   SYNC_failed    0

#define	  SYNC1_First	 0						 /* 同步后第一回合进入运行标志 */
#define   SYNC1_nFirst	 1

#define	  SYNC2_First	 0						 /* 同步后第一回合进入运行标志 */
#define   SYNC2_nFirst	 1

#define	  SYNC3_First	 0						 /* 同步后第一回合进入运行标志 */
#define   SYNC3_nFirst	 1

#define	  SYNC4_First	 0						 /* 同步后第一回合进入运行标志 */
#define   SYNC4_nFirst	 1

#define   TIMER_FINISHED 1						/* CHECKER检测完毕标志 */

#define   NONE_PRESS     0					   /* 用来标志按了哪个按钮的值 */
#define   MAXING         1					   
#define   DINGSHI        2
#define   SULV           3
#define   KAISHI         4
#define   TINGZHI        5
#define   GENGDUO        6
#define   TX0SW        7
#define   TX1SW        8
#define   TX2SW        9
#define   TX3SW        10
#define   RERUN        11
#define   KAISHI1         12
#define   KAISHI2         13
#define   KAISHI3         14
#define   TINGZHI1        15
#define   TINGZHI2        16
#define   TINGZHI3        17
#define   RERUN1        18
#define   RERUN2        19
#define   RERUN3        20

#define swing    1               /* 用于定义更多选项选择 */
#define polarity 2
#define about    3
#define help     4
#define clockdiv 5
#define EQ		 6

/* Private function prototypes -----------------------------------------------*/

void Menu_UDP(void);
void Menu_Pattern(void);
void Menu_Timer(void)	;
void Menu_More(void);
void Menu_Rate(void);
void Menu_RTC(void);
void Menu_UDEFRate(void);
void Menu_OtherRate(void);
void reconfigRate(void);
void Menu_UDEFT(void);
void XY_touch(int* XY);
void RUN_SHOW(void);
void RUN_Eye(void);
void Eye_Scan(void);
void Tra_Mode(void);
void Auto_Mode(void);
void draw_gang(void);
void Show_id(void);
void update_status(void);
void User_defined_pattern(void);
void id_read(void);

extern u8 curchan;

extern int XY[2];
extern int biaoji;
extern int USB_biaoji  ;	
extern char xls[20];

extern u8 BEC_Read_Status ;
//extern u8 BEC_Read_Status1 ;
//extern u8 BEC_Read_Status2 ;
//extern u8 BEC_Read_Status3 ;

extern u8 RUN_FLAG;
extern u8 RUN_FLAG1;
extern u8 RUN_FLAG2;
extern u8 RUN_FLAG3;

extern u8 TX_FLAG;
extern u8 TX_Status;
extern u8 TX_First;
extern u8 TX_Rate;
extern u8 START_RUN;
extern u8 START_RUN1;
extern u8 START_RUN2;
extern u8 START_RUN3;

extern u8 START_SYNC;
extern int TIME_COUNT;
extern int TIME_COUNT0;
extern int TIME_COUNT00;
extern int TIME_COUNT1;
extern int TIME_COUNT11;
extern int TIME_COUNT2;
extern int TIME_COUNT22;
extern int TIME_COUNT3;
extern int TIME_COUNT33;
extern u8 Finger_Flag;
extern u8 Icon;



#endif 

/*********************************************************************************************************
      END FILE
*********************************************************************************************************/

