/************************************************************************************************
 *
 *工程名称：4*10G误码仪
 *
 *说	明：USB上位机通信程序
 *
 *版	本：V4.02
 *
 *作	者：LHZ
 *
 *创建时间：2015年12月1日
 *
 *修改时间：20151201
 *
 s************************************************************************************************/

#include "Drivers.h"
#include "cs4224_api.h"
#include "Lcd_Interface.h"
#include "BER_Test.h"

extern u8 curchan;

u8 kls[50]={0};              /* 用来储存USB串口传过来的数据 */
/*--------------------------------------------------------------------------
 下位机USB循环发送程序，8位
--------------------------------------------------------------------------*/
void USB_tx8p (u8 *P,u8 Size)
 {
	 u8 i;
	for(i=0;i<Size;i++) 
	{
	 	USART_SendData(USART1, *(P+i));
		while (USART_GetFlagStatus(USART1, USART_FLAG_TC) == RESET);
		
		USART_SendData(USART3, *(P+i));
		while (USART_GetFlagStatus(USART3, USART_FLAG_TC) == RESET);
	}
 }
 
/*--------------------------------------------------------------------------
 下位机USB循环发送程序，16位   
--------------------------------------------------------------------------*/ 
  void USB_tx16p (u8 *P,u16 Size)
 {
	 u16 i;
	for(i=0;i<Size;i++) 
	{
	 	USART_SendData(USART1, *(P+i));
		while (USART_GetFlagStatus(USART1, USART_FLAG_TC) == RESET);
		
		USART_SendData(USART3, *(P+i));
		while (USART_GetFlagStatus(USART3, USART_FLAG_TC) == RESET);
	}
 }
 
 
 
 /*--------------------------------------------------------------------------
 上传开始停止检测指令 USB_00 
     
      用于向上位机回复是否接收到开始检测和停止检测指令
68 id 00 00 00 校验 16    
--------------------------------------------------------------------------*/
 void USB_00(void)
{
	u8 txdata[256];
	u8 i=0,Sum_Check=0;
	txdata[i++]=0x68;
	txdata[i++]=(u8)(id>>24);
	txdata[i++]=(u8)(id>>16);
	txdata[i++]=(u8)(id>>8);
	txdata[i++]=(u8)(id);
	txdata[i++]=0x00;  //控制字
	txdata[i++]=0x00;  //数据长度0000
	txdata[i++]=0x00;  //数据长度0000
	Sum_Check = Checksum (txdata+1, i-1);
	txdata[i++]= Sum_Check;
	txdata[i++]= 0x16;
	USB_tx8p(txdata,i);
}

 void USB_02(void)
{
	u8 txdata[256];
	u8 i=0,Sum_Check=0;
	txdata[i++]=0x68;
	txdata[i++]=(u8)(id>>24);
	txdata[i++]=(u8)(id>>16);
	txdata[i++]=(u8)(id>>8);
	txdata[i++]=(u8)(id);
	txdata[i++]=0x02;  //控制字
	txdata[i++]=0x00;  //数据长度0000
	txdata[i++]=0x00;  //数据长度0000
	Sum_Check = Checksum (txdata+1, i-1);
	txdata[i++]= Sum_Check;
	txdata[i++]= 0x16;
	USB_tx8p(txdata,i);
}

 void USB_03(void)
{
	u8 txdata[256];
	u8 i=0,Sum_Check=0;
	txdata[i++]=0x68;
	txdata[i++]=(u8)(id>>24);
	txdata[i++]=(u8)(id>>16);
	txdata[i++]=(u8)(id>>8);
	txdata[i++]=(u8)(id);
	txdata[i++]=0x03;  //控制字
	txdata[i++]=0x00;  //数据长度0000
	txdata[i++]=0x00;  //数据长度0000
	Sum_Check = Checksum (txdata+1, i-1);
	txdata[i++]= Sum_Check;
	txdata[i++]= 0x16;
	USB_tx8p(txdata,i);
}

/*--------------------------------------------------------------------------
 上传下位机配置状态指令 USB_01
     
      将下位机目前设置的参数如速率码型等同步到上位机
   
--------------------------------------------------------------------------*/
void USB_01(void)
{
	u8 txdata[256];
	u8 i=0,Sum_Check=0;
	txdata[i++]=0x68;
	txdata[i++]=0x00;  //此时上位机那边的ID还是00 00 00 00
	txdata[i++]=0x00;
	txdata[i++]=0x00;
	txdata[i++]=0x00;
	txdata[i++]=0x01;  //控制字
	txdata[i++]=0x00;  //数据长度60+18
	txdata[i++]=0x52;//0x4E;//0x3C;
	txdata[i++]=(u8)(id>>24);
	txdata[i++]=(u8)(id>>16);
	txdata[i++]=(u8)(id>>8);
	txdata[i++]=(u8)(id);
	txdata[i++]= pattern[0];
	txdata[i++]= udp[0][0];
	txdata[i++]= udp[0][1];
	txdata[i++]= udp[0][2];
	txdata[i++]= udp[0][3];
	txdata[i++]= udp[0][4];
	txdata[i++]= udp[0][5];
	txdata[i++]= udp[0][6];
	txdata[i++]= udp[0][7];
	txdata[i++]= pattern[1];
	txdata[i++]= udp[1][0];
	txdata[i++]= udp[1][1];
	txdata[i++]= udp[1][2];
	txdata[i++]= udp[1][3];
	txdata[i++]= udp[1][4];
	txdata[i++]= udp[1][5];
	txdata[i++]= udp[1][6];
	txdata[i++]= udp[1][7];
	txdata[i++]= pattern[2];
	txdata[i++]= udp[2][0];
	txdata[i++]= udp[2][1];
	txdata[i++]= udp[2][2];
	txdata[i++]= udp[2][3];
	txdata[i++]= udp[2][4];
	txdata[i++]= udp[2][5];
	txdata[i++]= udp[2][6];
	txdata[i++]= udp[2][7];
	txdata[i++]= pattern[3];
	txdata[i++]= udp[3][0];
	txdata[i++]= udp[3][1];
	txdata[i++]= udp[3][2];
	txdata[i++]= udp[3][3];
	txdata[i++]= udp[3][4];
	txdata[i++]= udp[3][5];
	txdata[i++]= udp[3][6];
	txdata[i++]= udp[3][7];
	txdata[i++]= rate;
//	union{u32 a; float userRate;}b;
//	b.userRate = def_rate;
//	txdata[i++] = (u8)(b.a>>24);
//	txdata[i++] = (u8)(b.a>>16);
//	txdata[i++] = (u8)(b.a>>8);
//	txdata[i++] = (u8)(b.a>>0);
	
	txdata[i++]= mode[0];
	txdata[i++]=(u8)(timer_set[0]>>24);
	txdata[i++]=(u8)(timer_set[0]>>16);
	txdata[i++]=(u8)(timer_set[0]>>8);
	txdata[i++]=(u8)(timer_set[0]);
	txdata[i++]= mode[1];
	txdata[i++]=(u8)(timer_set[1]>>24);
	txdata[i++]=(u8)(timer_set[1]>>16);
	txdata[i++]=(u8)(timer_set[1]>>8);
	txdata[i++]=(u8)(timer_set[1]);
	txdata[i++]= mode[2];
	txdata[i++]=(u8)(timer_set[2]>>24);
	txdata[i++]=(u8)(timer_set[2]>>16);
	txdata[i++]=(u8)(timer_set[2]>>8);
	txdata[i++]=(u8)(timer_set[2]);
	txdata[i++]= mode[3];
	txdata[i++]=(u8)(timer_set[3]>>24);
	txdata[i++]=(u8)(timer_set[3]>>16);
	txdata[i++]=(u8)(timer_set[3]>>8);
	txdata[i++]=(u8)(timer_set[3]);
	txdata[i++]= swing_ch[0];
	txdata[i++]= swing_ch[1];
	txdata[i++]= swing_ch[2];
	txdata[i++]= swing_ch[3];
	txdata[i++]= polarity_ch[0];
	txdata[i++]= polarity_ch[1];
	txdata[i++]= polarity_ch[2];
	txdata[i++]= polarity_ch[3];
	//txdata[i++]= ClockDivFlag;
	txdata[i++]= TX_Status;
	txdata[i++]= EQ_Ch[0].Eqpst;
	txdata[i++]= EQ_Ch[0].Eqpre;
	txdata[i++]= EQ_Ch[1].Eqpst;
	txdata[i++]= EQ_Ch[1].Eqpre;
	txdata[i++]= EQ_Ch[2].Eqpst;
	txdata[i++]= EQ_Ch[2].Eqpre;
	txdata[i++]= EQ_Ch[3].Eqpst;
	txdata[i++]= EQ_Ch[3].Eqpre;
	txdata[i++]= polarity_rx[0];
	txdata[i++]= polarity_rx[1];
	txdata[i++]= polarity_rx[2];
	txdata[i++]= polarity_rx[3];
	Sum_Check = Checksum (txdata+1, i-1);
	txdata[i++]= Sum_Check;
	txdata[i++]= 0x16;
	USB_tx8p(txdata,i);
}


//void USB_61(void)
//{
//	u8 txdata[256];
//	u8 i=0,Sum_Check=0;
//	txdata[i++]=0x68;
//	txdata[i++]=(u8)(id>>24);
//	txdata[i++]=(u8)(id>>16);
//	txdata[i++]=(u8)(id>>8);
//	txdata[i++]=(u8)(id);
//	txdata[i++]=0x61;  //控制字
//	txdata[i++]=0x00;  //数据长度02
//	txdata[i++]=0x02;
//	if(rate != 0)
//	{
//		txdata[i++]= DIV_Rate[rate].ddivpos;
//	  txdata[i++]= DIV_Rate[rate].rdivpos;
//	}
//	else
//	{
//		txdata[i++] = DIV_userRate[userRatepos].ddivpos;
//		txdata[i++] = DIV_userRate[userRatepos].rdivpos;
//	}
//	
//	Sum_Check = Checksum (txdata+1, i-1);
//	txdata[i++]= Sum_Check;
//	txdata[i++]= 0x16;
//	USB_tx8p(txdata,i);
//}

/*--------------------------------------------------------------------------
 上传下位机配置状态指令 USB_10
     
      用于向上位机回复是否接收到上位机下达的码型、速率等参数配置的指令
68 id 10 00 00 校验 16    
   
--------------------------------------------------------------------------*/
 void USB_10(void)
{
	u8 txdata[256];
	u8 i=0,Sum_Check=0;
	txdata[i++]=0x68;
	txdata[i++]=(u8)(id>>24);
	txdata[i++]=(u8)(id>>16);
	txdata[i++]=(u8)(id>>8);
	txdata[i++]=(u8)(id);
	txdata[i++]=0x10;  //控制字
	txdata[i++]=0x00;  //数据长度0000
	txdata[i++]=0x00;  //数据长度0000
	Sum_Check = Checksum (txdata+1, i-1);
	txdata[i++]= Sum_Check;
	txdata[i++]= 0x16;
	USB_tx8p(txdata,i);
}

// void USB_11(void)  //设置单通道参数回应
//{
//	u8 txdata[256];
//	u8 i=0,Sum_Check=0;
//	txdata[i++]=0x68;
//	txdata[i++]=(u8)(id>>24);
//	txdata[i++]=(u8)(id>>16);
//	txdata[i++]=(u8)(id>>8);
//	txdata[i++]=(u8)(id);
//	txdata[i++]=0x11;  //控制字
//	txdata[i++]=0x00;  //数据长度0000
//	txdata[i++]=0x00;  //数据长度0000
//	Sum_Check = Checksum (txdata+1, i-1);
//	txdata[i++]= Sum_Check;
//	txdata[i++]= 0x16;
//	USB_tx8p(txdata,i);
//}

// void USB_12(void)  //设置定时回应
//{
//	u8 txdata[256];
//	u8 i=0,Sum_Check=0;
//	txdata[i++]=0x68;
//	txdata[i++]=(u8)(id>>24);
//	txdata[i++]=(u8)(id>>16);
//	txdata[i++]=(u8)(id>>8);
//	txdata[i++]=(u8)(id);
//	txdata[i++]=0x12;  //控制字
//	txdata[i++]=0x00;  //数据长度0000
//	txdata[i++]=0x00;  //数据长度0000
//	Sum_Check = Checksum (txdata+1, i-1);
//	txdata[i++]= Sum_Check;
//	txdata[i++]= 0x16;
//	USB_tx8p(txdata,i);
//}


/*--------------------------------------------------------------------------
 上传下位机配置状态指令 USB_20
     
      用于上传4个通道的同步状态和误码率等信息
   
--------------------------------------------------------------------------*/
void USB_20(void)
{
	u8 txdata[256];
	u8 i=0,Sum_Check=0;
	txdata[i++]=0x68;
	txdata[i++]=(u8)(id>>24);
	txdata[i++]=(u8)(id>>16);
	txdata[i++]=(u8)(id>>8);
	txdata[i++]=(u8)(id);
	txdata[i++]=0x20;  //控制字
	txdata[i++]=0x00;  //数据长度0014    20+5字节
	txdata[i++]=40;
	
	if(TIME_COUNT00 == 0) txdata[i++]= 0;
	else 	txdata[i++]= sta_ch0;
	txdata[i++]= (u8)(BEC_Value_ch[0]>>24);
	txdata[i++]= (u8)(BEC_Value_ch[0]>>16);
	txdata[i++]= (u8)(BEC_Value_ch[0]>>8);
	txdata[i++]= (u8)(BEC_Value_ch[0]);
	txdata[i++]= (u8)(TIME_COUNT00>>24);
	txdata[i++]= (u8)(TIME_COUNT00>>16);
	txdata[i++]= (u8)(TIME_COUNT00>>8);
	txdata[i++]= (u8)(TIME_COUNT00);
	txdata[i++]= (u8)(RUN_FLAG);
	if(TIME_COUNT11 == 0) txdata[i++]= 0;
	else 	txdata[i++]= sta_ch1;
	txdata[i++]= (u8)(BEC_Value_ch[1]>>24);
	txdata[i++]= (u8)(BEC_Value_ch[1]>>16);
	txdata[i++]= (u8)(BEC_Value_ch[1]>>8);
	txdata[i++]= (u8)(BEC_Value_ch[1]);
	txdata[i++]= (u8)(TIME_COUNT11>>24);
	txdata[i++]= (u8)(TIME_COUNT11>>16);
	txdata[i++]= (u8)(TIME_COUNT11>>8);
	txdata[i++]= (u8)(TIME_COUNT11);
	txdata[i++]= (u8)(RUN_FLAG1);
	if(TIME_COUNT22 == 0) txdata[i++]= 0;
	else txdata[i++]= sta_ch2;
	txdata[i++]= (u8)(BEC_Value_ch[2]>>24);
	txdata[i++]= (u8)(BEC_Value_ch[2]>>16);
	txdata[i++]= (u8)(BEC_Value_ch[2]>>8);
	txdata[i++]= (u8)(BEC_Value_ch[2]);
	txdata[i++]= (u8)(TIME_COUNT22>>24);
	txdata[i++]= (u8)(TIME_COUNT22>>16);
	txdata[i++]= (u8)(TIME_COUNT22>>8);
	txdata[i++]= (u8)(TIME_COUNT22);
	txdata[i++]= (u8)(RUN_FLAG2);
	if(TIME_COUNT33 == 0) txdata[i++]= 0;
	else 	txdata[i++]= sta_ch3;
	txdata[i++]= (u8)(BEC_Value_ch[3]>>24);
	txdata[i++]= (u8)(BEC_Value_ch[3]>>16);
	txdata[i++]= (u8)(BEC_Value_ch[3]>>8);
	txdata[i++]= (u8)(BEC_Value_ch[3]);
	txdata[i++]= (u8)(TIME_COUNT33>>24);
	txdata[i++]= (u8)(TIME_COUNT33>>16);
	txdata[i++]= (u8)(TIME_COUNT33>>8);
	txdata[i++]= (u8)(TIME_COUNT33);
	txdata[i++]= (u8)(RUN_FLAG3);
//	txdata[i++]= (u8)(TIME_COUNT>>24);
//	txdata[i++]= (u8)(TIME_COUNT>>16);
//	txdata[i++]= (u8)(TIME_COUNT>>8);
//	txdata[i++]= (u8)(TIME_COUNT);	
//	txdata[i++]= (u8)(RUN_FLAG);
	
	Sum_Check = Checksum (txdata+1, i-1);
	txdata[i++]= Sum_Check;
	txdata[i++]= 0x16;
	USB_tx8p(txdata,i);
}

void USB_40(void)
{
	u8 txdata[256];
	u8 i=0,Sum_Check=0;
	txdata[i++]=0x68;
	txdata[i++]=(u8)(id>>24);
	txdata[i++]=(u8)(id>>16);
	txdata[i++]=(u8)(id>>8);
	txdata[i++]=(u8)(id);
	txdata[i++]=0x40;  //控制字
	txdata[i++]=0x00;  //数据长度0014    20+5字节
	txdata[i++]=40;
	
	if(TIME_COUNT00 == 0) txdata[i++]= 0;
	else 	txdata[i++]= sta_ch0;
	txdata[i++]= (u8)(BEC_Value_real[0]>>24);
	txdata[i++]= (u8)(BEC_Value_real[0]>>16);
	txdata[i++]= (u8)(BEC_Value_real[0]>>8);
	txdata[i++]= (u8)(BEC_Value_real[0]);
	txdata[i++]= (u8)(TIME_COUNT00>>24);
	txdata[i++]= (u8)(TIME_COUNT00>>16);
	txdata[i++]= (u8)(TIME_COUNT00>>8);
	txdata[i++]= (u8)(TIME_COUNT00);
	txdata[i++]= (u8)(RUN_FLAG);
	if(TIME_COUNT11 == 0) txdata[i++]= 0;
	else 	txdata[i++]= sta_ch1;
	txdata[i++]= (u8)(BEC_Value_real[1]>>24);
	txdata[i++]= (u8)(BEC_Value_real[1]>>16);
	txdata[i++]= (u8)(BEC_Value_real[1]>>8);
	txdata[i++]= (u8)(BEC_Value_real[1]);
	txdata[i++]= (u8)(TIME_COUNT11>>24);
	txdata[i++]= (u8)(TIME_COUNT11>>16);
	txdata[i++]= (u8)(TIME_COUNT11>>8);
	txdata[i++]= (u8)(TIME_COUNT11);
	txdata[i++]= (u8)(RUN_FLAG1);
	if(TIME_COUNT22 == 0) txdata[i++]= 0;
	else txdata[i++]= sta_ch2;
	txdata[i++]= (u8)(BEC_Value_real[2]>>24);
	txdata[i++]= (u8)(BEC_Value_real[2]>>16);
	txdata[i++]= (u8)(BEC_Value_real[2]>>8);
	txdata[i++]= (u8)(BEC_Value_real[2]);
	txdata[i++]= (u8)(TIME_COUNT22>>24);
	txdata[i++]= (u8)(TIME_COUNT22>>16);
	txdata[i++]= (u8)(TIME_COUNT22>>8);
	txdata[i++]= (u8)(TIME_COUNT22);
	txdata[i++]= (u8)(RUN_FLAG2);
	if(TIME_COUNT33 == 0) txdata[i++]= 0;
	else 	txdata[i++]= sta_ch3;
	txdata[i++]= (u8)(BEC_Value_real[3]>>24);
	txdata[i++]= (u8)(BEC_Value_real[3]>>16);
	txdata[i++]= (u8)(BEC_Value_real[3]>>8);
	txdata[i++]= (u8)(BEC_Value_real[3]);
	txdata[i++]= (u8)(TIME_COUNT33>>24);
	txdata[i++]= (u8)(TIME_COUNT33>>16);
	txdata[i++]= (u8)(TIME_COUNT33>>8);
	txdata[i++]= (u8)(TIME_COUNT33);
	txdata[i++]= (u8)(RUN_FLAG3);
//	txdata[i++]= (u8)(TIME_COUNT>>24);
//	txdata[i++]= (u8)(TIME_COUNT>>16);
//	txdata[i++]= (u8)(TIME_COUNT>>8);
//	txdata[i++]= (u8)(TIME_COUNT);	
//	txdata[i++]= (u8)(RUN_FLAG);
	
	Sum_Check = Checksum (txdata+1, i-1);
	txdata[i++]= Sum_Check;
	txdata[i++]= 0x16;
	USB_tx8p(txdata,i);
}


void USB_30(void)
{
	u8 txdata[256];
	u8 i=0,Sum_Check=0,j;
	txdata[i++]=0x68;
	txdata[i++]=(u8)(id>>24);
	txdata[i++]=(u8)(id>>16);
	txdata[i++]=(u8)(id>>8);
	txdata[i++]=(u8)(id);
	txdata[i++]=0x30;  //控制字
	txdata[i++]=0x00;  //数据长    128字节
	txdata[i++]=0x82;
	for(j=0;j<130;j++)
	{
		txdata[i++]= (u8)Eye_data[j];		
	}

	Sum_Check = Checksum (txdata+1, i-1);
	txdata[i++]= Sum_Check;
	txdata[i++]= 0x16;
	USB_tx8p(txdata,i);
}


// void USB_60(void)
//{
//	u8 txdata[256];
//	u8 i=0,Sum_Check=0;
//	txdata[i++]=0x68;
//	txdata[i++]=(u8)(id>>24);
//	txdata[i++]=(u8)(id>>16);
//	txdata[i++]=(u8)(id>>8);
//	txdata[i++]=(u8)(id);
//	txdata[i++]=0x60;  //控制字
//	txdata[i++]=0x00;  //数据长度0000
//	txdata[i++]=0x00;  //数据长度0000
//	Sum_Check = Checksum (txdata+1, i-1);
//	txdata[i++]= Sum_Check;
//	txdata[i++]= 0x16;
//	USB_tx8p(txdata,i);
//}

// void USB_62(void)
//{
//	u8 txdata[256];
//	u8 i=0,Sum_Check=0;
//	txdata[i++]=0x68;
//	txdata[i++]=(u8)(id>>24);
//	txdata[i++]=(u8)(id>>16);
//	txdata[i++]=(u8)(id>>8);
//	txdata[i++]=(u8)(id);
//	txdata[i++]=0x62;  //控制字
//	txdata[i++]=0x00;  //数据长度0000
//	txdata[i++]=0x00;  //数据长度0000
//	Sum_Check = Checksum (txdata+1, i-1);
//	txdata[i++]= Sum_Check;
//	txdata[i++]= 0x16;
//	USB_tx8p(txdata,i);
//}

/*--------------------------------------------------------------------------
 解析上位机下传的指令部分,只需要解析数据域不为0的

     解析上位机发射的控制字为0x00的指令   USB_Reso_00       
     解析上位机发射的控制字为0x10的指令   USB_Reso_10
     
--------------------------------------------------------------------------*/
void USB_Reso_00(void)
{

	
	u8 Flag = kls[7];
	 curchan = kls[8];
	
	 switch(curchan)
	 {
		 case 0:
		 {
			 RUN_FLAG=Flag;
			 if(RUN_FLAG==0)
			 {
				 Icon = NONE_PRESS;
				 VIEWTECH_71(25,273,50,359,73,273,50);  //ch0  run  stop				 
				  VIEWTECH_71(11,402,62,468,77,402,62);	                /* 显示暂停状态  */
				START_RUN = First_circle;
	      START_SYNC1 = SYNC1_First;
				RUN_FLAG = noRuning;
			 }
			 else 
			 {
				 Icon = NONE_PRESS;
				 VIEWTECH_71(12,273,50,359,73,273,50);    //on/off
				 RUN_FLAG = Runing; 
			 START_RUN = First_circle;
	    START_SYNC1 = SYNC1_First;				 
			TIME_COUNT0 = -1;
			TIME_COUNT00 =-1;	
			BEC_Value_ch[0] = 0;
				 BEC_Value_real[0] = 0;
			 }
			 break;			 
		 }
		 case 1:
		 {
			 RUN_FLAG1=Flag;
			 if(RUN_FLAG1==0)
			 {
				 Icon = NONE_PRESS;
				 VIEWTECH_71(25,273,96,359,119,273,96);  //ch1  run  stop				 
				  VIEWTECH_71(11,402,108,468,123,402,108);	                /* 显示暂停状态  */
				START_RUN1 = First1_circle;
	      START_SYNC2 = SYNC2_First;
				RUN_FLAG1 = noRuning1;
			 }
			 else 
			 {
				 Icon = NONE_PRESS;
				VIEWTECH_71(12,273,96,359,119,273,96);    //on/off
				 RUN_FLAG1 = Runing1; 
				 START_RUN1 = First1_circle;
	    START_SYNC2 = SYNC2_First;	 
		  TIME_COUNT1 = -1;	
		  TIME_COUNT11 =-1;
			BEC_Value_ch[1] = 0;
				 BEC_Value_real[1] = 0;
			 }
			 break;
		 }
		  case 2:
		 {
			 RUN_FLAG2=Flag;
			 if(RUN_FLAG2==0)
			 {
				 Icon = NONE_PRESS;
				VIEWTECH_71(25,273,142,359,165,273,142);  //ch2  run  stop				 
				  VIEWTECH_71(11,402,154,468,169,402,154);	                /* 显示暂停状态  */
//					VIEWTECH_98 (265,417,0x23,0x90,3,0x2e9f,0x39c8,HGANG1,3);
//				  Para_Ini();	
				START_RUN2 = First2_circle;
	      START_SYNC3 = SYNC3_First;
				RUN_FLAG2 = noRuning2;
			 }
			 else 
			 {
				 Icon = NONE_PRESS;
							VIEWTECH_71(12,273,142,359,165,273,142);    //on/off
							 RUN_FLAG2 = Runing2; 
			//				 TIME_COUNT = 0;
			START_RUN2 = First2_circle;
	    START_SYNC3 = SYNC3_First;	 
		  TIME_COUNT2 = -1;	
		  TIME_COUNT22 =-1;
			BEC_Value_ch[2] = 0;
				 BEC_Value_real[2] = 0;
			 }
			 break;
		 }
		  case 3:
		 {
			 RUN_FLAG3=Flag;
			 if(RUN_FLAG3==0)
			 {
				 Icon = NONE_PRESS;
				VIEWTECH_71(25,273,188,359,210,273,188);  //ch1  run  stop				 
				  VIEWTECH_71(11,402,200,468,215,402,200);	                /* 显示暂停状态  */
//					VIEWTECH_98 (265,417,0x23,0x90,3,0x2e9f,0x39c8,HGANG1,3);
//				  Para_Ini();	
				START_RUN3 = First3_circle;
	      START_SYNC4 = SYNC4_First;
				RUN_FLAG3 = noRuning3;
			 }
			 else 
			 {
				 Icon = NONE_PRESS;
						VIEWTECH_71(12,273,188,359,210,273,188);    //on/off
							 RUN_FLAG3 = Runing3; 
			//				 TIME_COUNT = 0;
				START_RUN3 = First3_circle;
	    START_SYNC4 = SYNC4_First;	 
		  TIME_COUNT3 = -1;	
		  TIME_COUNT33 =-1;
			BEC_Value_ch[3] = 0;	
				 BEC_Value_real[3] = 0;
			 }
			 break;
		 }		 
	 }
}

//void USB_Reso_00(void)
//{
//	int i; 
//	u8 startStop = kls[7];
//	
//	 if(startStop==0)   //如果上位机发停止命令，将
//	 {
//		 START_RUN = 0;
//     START_SYNC1 = 0;
//     START_SYNC2 = 0;
//     START_SYNC3 = 0;
//     START_SYNC4 = 0;

//		 			Icon = NONE_PRESS;
//			    VIEWTECH_71(16,551,321,719,388,551,321); 
//				for(i=0; i<4; i++)
//				{								
//					cs4224_diags_prbs_checker_enable( i*2,   CS4224_PRBS_LINE_INTERFACE,  FALSE);			  /* 关闭CHECKER */
////					switch(pattern[i])
////					{
////						case USER_DEF:
////						{
////							cs4224_diags_fix_ptrn_generator_enable(i*2, CS4224_PRBS_LINE_INTERFACE, FALSE);
////							break;
////						}
////						default:
////						{
////							cs4224_diags_prbs_generator_enable(   i*2,  CS4224_PRBS_LINE_INTERFACE, FALSE);
////							break;
////						}
////					}
//				}
//		    //delay_ms(1000);
//				VIEWTECH_71(11,526,233,745,282,526,233);	                /* 显示暂停状态  */
//				Para_Ini();	

//   }
////	 else if(RUN_FLAG==1)
////	 {
////			BEC_Value_ch[0] = 0;		//将误码数清零，以便下次累加
////			BEC_Value_ch[1] = 0;		//将误码数清零，以便下次累加
////			BEC_Value_ch[2] = 0;		//将误码数清零，以便下次累加
////			BEC_Value_ch[3] = 0;		//将误码数清零，以便下次累加
////      TIME_COUNT = 0;		 
////	 }
//}

void USB_Reso_10(void)
{
	u8 rate_init;
	curchan = kls[27];
	
	pattern[curchan] = kls[7];      //kls[0]从ID开始，省略了ox68
	udp[curchan][0] =    kls[8];
	udp[curchan][1] =    kls[9];
	udp[curchan][2] =   kls[10];
	udp[curchan][3] =   kls[11];
	udp[curchan][4] =   kls[12];
	udp[curchan][5] =   kls[13];
	udp[curchan][6] =   kls[14];
	udp[curchan][7] =   kls[15];
	
	rate_init = rate;
	rate =    kls[16];
	if(rate_init != rate ) TX_Rate = 0;
	mode[curchan] =    kls[17];
	timer_set[curchan] =   ((kls[18]*256 + kls[19])*256 + kls[20])*256 +kls[21];
	swing_ch[curchan] =    kls[22];
	polarity_ch[curchan] = kls[23];
	EQ_Ch[curchan].Eqpst = kls[24];
	EQ_Ch[curchan].Eqpre = kls[25];
	polarity_rx[curchan] = kls[26];
	update_status();
}


void USB_Reso_02(void)
{	
	u8 bited;
	TX_FLAG = kls[7];	
	bited= kls[8];
	 
	if(TX_FLAG==0)   //如果上位机发停止命令，将
	 {
			TX_First=0;
		 if((bited&0x01)==1) 
			{TX_Status&=0xE;}
			if((bited&0x02)==2) 
			{TX_Status&=0xD;}
			if((bited&0x04)==4)
			{TX_Status&=0xB;}
			if((bited&0x08)==8) 
			{TX_Status&=0x7;}
   }
	 else	if(TX_FLAG==1)   //如果上位机发开始命令，将
	 {
		 TX_First=0;
		 	if((bited&0x01)==1) 
			{TX_Status|=0x1;}
			if((bited&0x02)==2) 
			{TX_Status|=0x2;}
			if((bited&0x04)==4)
			{TX_Status|=0x4;}
			if((bited&0x08)==8) 
			{TX_Status|=0x8;}	
   }
	 update_status();
}

void USB_Reso_30(void)
{
	Eyelane = kls[7]; 
}

//void USB_Reso_60(void)
//{	
//	rate = kls[7];
//	if(rate!=0)
//	{
//		DIV_Rate[rate].ddivpos =  kls[8];
//	  DIV_Rate[rate].rdivpos =  kls[9];
//	}
//	else 
//	{
//		DIV_userRate[userRatepos].ddivpos = kls[8];
//		DIV_userRate[userRatepos].rdivpos = kls[9];
//		
//	}
//	TX_Rate = 0;
//	
//	update_status();
//}


